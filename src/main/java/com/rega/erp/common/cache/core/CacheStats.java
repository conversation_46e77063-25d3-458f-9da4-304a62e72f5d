package com.rega.erp.common.cache.core;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 缓存统计信息
 *
 * <AUTHOR>
 */
@Data
@Builder
public class CacheStats {
    
    /**
     * 缓存类型
     */
    private CacheType cacheType;
    
    /**
     * 缓存名称
     */
    private String cacheName;
    
    /**
     * 命中次数
     */
    private long hitCount;
    
    /**
     * 未命中次数
     */
    private long missCount;
    
    /**
     * 加载次数
     */
    private long loadCount;
    
    /**
     * 加载异常次数
     */
    private long loadExceptionCount;
    
    /**
     * 总加载时间（纳秒）
     */
    private long totalLoadTime;
    
    /**
     * 驱逐次数
     */
    private long evictionCount;
    
    /**
     * 当前缓存大小
     */
    private long size;
    
    /**
     * 最大缓存大小
     */
    private long maxSize;
    
    /**
     * 统计开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 统计结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 计算命中率
     *
     * @return 命中率（0.0-1.0）
     */
    public double getHitRate() {
        long totalRequests = hitCount + missCount;
        return totalRequests == 0 ? 0.0 : (double) hitCount / totalRequests;
    }
    
    /**
     * 计算未命中率
     *
     * @return 未命中率（0.0-1.0）
     */
    public double getMissRate() {
        return 1.0 - getHitRate();
    }
    
    /**
     * 计算平均加载时间（毫秒）
     *
     * @return 平均加载时间
     */
    public double getAverageLoadTime() {
        return loadCount == 0 ? 0.0 : (double) totalLoadTime / loadCount / 1_000_000;
    }
    
    /**
     * 计算加载成功率
     *
     * @return 加载成功率（0.0-1.0）
     */
    public double getLoadSuccessRate() {
        long totalLoads = loadCount + loadExceptionCount;
        return totalLoads == 0 ? 0.0 : (double) loadCount / totalLoads;
    }
    
    /**
     * 获取总请求次数
     *
     * @return 总请求次数
     */
    public long getTotalRequests() {
        return hitCount + missCount;
    }
    
    /**
     * 获取缓存使用率
     *
     * @return 缓存使用率（0.0-1.0）
     */
    public double getUsageRate() {
        return maxSize == 0 ? 0.0 : (double) size / maxSize;
    }
    
    /**
     * 重置统计信息
     */
    public void reset() {
        this.hitCount = 0;
        this.missCount = 0;
        this.loadCount = 0;
        this.loadExceptionCount = 0;
        this.totalLoadTime = 0;
        this.evictionCount = 0;
        this.startTime = LocalDateTime.now();
        this.endTime = null;
    }
    
    /**
     * 创建空的统计信息
     *
     * @param cacheType 缓存类型
     * @param cacheName 缓存名称
     * @return 统计信息
     */
    public static CacheStats empty(CacheType cacheType, String cacheName) {
        return CacheStats.builder()
                .cacheType(cacheType)
                .cacheName(cacheName)
                .hitCount(0)
                .missCount(0)
                .loadCount(0)
                .loadExceptionCount(0)
                .totalLoadTime(0)
                .evictionCount(0)
                .size(0)
                .maxSize(0)
                .startTime(LocalDateTime.now())
                .build();
    }
    
    @Override
    public String toString() {
        return String.format(
                "CacheStats{type=%s, name='%s', hitRate=%.2f%%, size=%d/%d, avgLoadTime=%.2fms}",
                cacheType.getName(),
                cacheName,
                getHitRate() * 100,
                size,
                maxSize,
                getAverageLoadTime()
        );
    }
}
