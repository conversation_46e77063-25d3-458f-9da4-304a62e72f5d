package com.rega.erp.common.cache.redis;

import com.rega.erp.common.cache.core.CacheService;
import com.rega.erp.common.cache.core.CacheStats;
import com.rega.erp.common.cache.core.CacheType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Redis 缓存服务实现
 *
 * <AUTHOR>
 */
@Slf4j
public class RedisCacheService implements CacheService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final String cacheName;
    
    // Lua 脚本：分布式锁
    private static final String UNLOCK_SCRIPT = 
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
            "    return redis.call('del', KEYS[1]) " +
            "else " +
            "    return 0 " +
            "end";
    
    private static final DefaultRedisScript<Long> UNLOCK_LUA_SCRIPT = new DefaultRedisScript<>(UNLOCK_SCRIPT, Long.class);
    
    public RedisCacheService(RedisTemplate<String, Object> redisTemplate) {
        this(redisTemplate, "default");
    }
    
    public RedisCacheService(RedisTemplate<String, Object> redisTemplate, String cacheName) {
        this.redisTemplate = redisTemplate;
        this.cacheName = cacheName;
    }
    
    @Override
    public void set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            log.debug("Redis缓存设置成功: key={}", key);
        } catch (Exception e) {
            log.error("Redis缓存设置失败: key={}", key, e);
            throw new RuntimeException("Redis缓存设置失败", e);
        }
    }
    
    @Override
    public void set(String key, Object value, long timeout, TimeUnit timeUnit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
            log.debug("Redis缓存设置成功: key={}, timeout={} {}", key, timeout, timeUnit);
        } catch (Exception e) {
            log.error("Redis缓存设置失败: key={}", key, e);
            throw new RuntimeException("Redis缓存设置失败", e);
        }
    }
    
    @Override
    public void set(String key, Object value, Duration duration) {
        set(key, value, duration.toMillis(), TimeUnit.MILLISECONDS);
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            log.debug("Redis缓存获取: key={}, found={}", key, value != null);
            return (T) value;
        } catch (Exception e) {
            log.error("Redis缓存获取失败: key={}", key, e);
            return null;
        }
    }
    
    @Override
    public <T> T get(String key, Class<T> clazz) {
        Object value = get(key);
        if (value == null) {
            return null;
        }
        
        if (clazz.isInstance(value)) {
            return clazz.cast(value);
        }
        
        log.warn("Redis缓存值类型不匹配: key={}, expected={}, actual={}", 
                key, clazz.getSimpleName(), value.getClass().getSimpleName());
        return null;
    }
    
    @Override
    public <T> T get(String key, Function<String, T> loader) {
        T value = get(key);
        if (value != null) {
            return value;
        }
        
        // 缓存未命中，通过加载器获取值
        try {
            value = loader.apply(key);
            if (value != null) {
                set(key, value);
            }
            return value;
        } catch (Exception e) {
            log.error("缓存加载器执行失败: key={}", key, e);
            return null;
        }
    }
    
    @Override
    public <T> T get(String key, Function<String, T> loader, long timeout, TimeUnit timeUnit) {
        T value = get(key);
        if (value != null) {
            return value;
        }
        
        // 缓存未命中，通过加载器获取值
        try {
            value = loader.apply(key);
            if (value != null) {
                set(key, value, timeout, timeUnit);
            }
            return value;
        } catch (Exception e) {
            log.error("缓存加载器执行失败: key={}", key, e);
            return null;
        }
    }
    
    @Override
    public boolean delete(String key) {
        try {
            Boolean result = redisTemplate.delete(key);
            boolean deleted = Boolean.TRUE.equals(result);
            log.debug("Redis缓存删除: key={}, deleted={}", key, deleted);
            return deleted;
        } catch (Exception e) {
            log.error("Redis缓存删除失败: key={}", key, e);
            return false;
        }
    }
    
    @Override
    public long delete(Collection<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return 0;
        }
        
        try {
            Long result = redisTemplate.delete(keys);
            long deleted = result != null ? result : 0;
            log.debug("Redis批量删除缓存: keys={}, deleted={}", keys.size(), deleted);
            return deleted;
        } catch (Exception e) {
            log.error("Redis批量删除缓存失败: keys={}", keys.size(), e);
            return 0;
        }
    }
    
    @Override
    public boolean exists(String key) {
        try {
            Boolean result = redisTemplate.hasKey(key);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("Redis缓存存在性检查失败: key={}", key, e);
            return false;
        }
    }
    
    @Override
    public boolean expire(String key, long timeout, TimeUnit timeUnit) {
        try {
            Boolean result = redisTemplate.expire(key, timeout, timeUnit);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("Redis设置过期时间失败: key={}", key, e);
            return false;
        }
    }
    
    @Override
    public long getExpire(String key) {
        try {
            Long result = redisTemplate.getExpire(key, TimeUnit.SECONDS);
            return result != null ? result : -2;
        } catch (Exception e) {
            log.error("Redis获取过期时间失败: key={}", key, e);
            return -2;
        }
    }
    
    @Override
    public void multiSet(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return;
        }
        
        try {
            redisTemplate.opsForValue().multiSet(map);
            log.debug("Redis批量设置缓存: count={}", map.size());
        } catch (Exception e) {
            log.error("Redis批量设置缓存失败: count={}", map.size(), e);
            throw new RuntimeException("Redis批量设置缓存失败", e);
        }
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T> Map<String, T> multiGet(Collection<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return new HashMap<>();
        }
        
        try {
            List<String> keyList = new ArrayList<>(keys);
            List<Object> values = redisTemplate.opsForValue().multiGet(keyList);
            
            Map<String, T> result = new HashMap<>();
            for (int i = 0; i < keyList.size(); i++) {
                Object value = values != null && i < values.size() ? values.get(i) : null;
                if (value != null) {
                    result.put(keyList.get(i), (T) value);
                }
            }
            
            log.debug("Redis批量获取缓存: requested={}, found={}", keys.size(), result.size());
            return result;
        } catch (Exception e) {
            log.error("Redis批量获取缓存失败: keys={}", keys.size(), e);
            return new HashMap<>();
        }
    }
    
    @Override
    public Set<String> keys(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            return keys != null ? keys : new HashSet<>();
        } catch (Exception e) {
            log.error("Redis获取键模式失败: pattern={}", pattern, e);
            return new HashSet<>();
        }
    }
    
    @Override
    public long deleteByPattern(String pattern) {
        try {
            Set<String> keys = keys(pattern);
            return delete(keys);
        } catch (Exception e) {
            log.error("Redis按模式删除失败: pattern={}", pattern, e);
            return 0;
        }
    }
    
    @Override
    public long increment(String key) {
        try {
            Long result = redisTemplate.opsForValue().increment(key);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("Redis递增失败: key={}", key, e);
            throw new RuntimeException("Redis递增失败", e);
        }
    }
    
    @Override
    public long increment(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().increment(key, delta);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("Redis递增失败: key={}, delta={}", key, delta, e);
            throw new RuntimeException("Redis递增失败", e);
        }
    }
    
    @Override
    public long decrement(String key) {
        try {
            Long result = redisTemplate.opsForValue().decrement(key);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("Redis递减失败: key={}", key, e);
            throw new RuntimeException("Redis递减失败", e);
        }
    }
    
    @Override
    public long decrement(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().decrement(key, delta);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("Redis递减失败: key={}, delta={}", key, delta, e);
            throw new RuntimeException("Redis递减失败", e);
        }
    }
    
    @Override
    public boolean tryLock(String lockKey, long timeout, TimeUnit unit) {
        try {
            String lockValue = UUID.randomUUID().toString();
            Boolean result = redisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, timeout, unit);
            boolean locked = Boolean.TRUE.equals(result);
            log.debug("Redis分布式锁尝试: key={}, locked={}", lockKey, locked);
            return locked;
        } catch (Exception e) {
            log.error("Redis分布式锁失败: key={}", lockKey, e);
            return false;
        }
    }
    
    @Override
    public boolean unlock(String lockKey) {
        try {
            String lockValue = get(lockKey, String.class);
            if (lockValue == null) {
                return true; // 锁已经不存在
            }
            
            Long result = redisTemplate.execute(UNLOCK_LUA_SCRIPT, 
                    Collections.singletonList(lockKey), lockValue);
            boolean unlocked = result != null && result > 0;
            log.debug("Redis分布式锁释放: key={}, unlocked={}", lockKey, unlocked);
            return unlocked;
        } catch (Exception e) {
            log.error("Redis分布式锁释放失败: key={}", lockKey, e);
            return false;
        }
    }
    
    @Override
    public boolean executeWithLock(String lockKey, long timeout, TimeUnit unit, Runnable runnable) {
        if (tryLock(lockKey, timeout, unit)) {
            try {
                runnable.run();
                return true;
            } finally {
                unlock(lockKey);
            }
        }
        return false;
    }
    
    @Override
    public <T> T executeWithLock(String lockKey, long timeout, TimeUnit unit, Function<String, T> supplier) {
        if (tryLock(lockKey, timeout, unit)) {
            try {
                return supplier.apply(lockKey);
            } finally {
                unlock(lockKey);
            }
        }
        return null;
    }
    
    @Override
    public CacheStats getStats() {
        // Redis 本身不提供详细的统计信息，这里返回基本信息
        return CacheStats.builder()
                .cacheType(CacheType.REDIS)
                .cacheName(cacheName)
                .hitCount(0)
                .missCount(0)
                .loadCount(0)
                .loadExceptionCount(0)
                .totalLoadTime(0)
                .evictionCount(0)
                .size(0)
                .maxSize(0)
                .build();
    }
    
    @Override
    public void clear() {
        try {
            Set<String> keys = redisTemplate.keys("*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("Redis缓存清空完成: deleted={}", keys.size());
            }
        } catch (Exception e) {
            log.error("Redis缓存清空失败", e);
            throw new RuntimeException("Redis缓存清空失败", e);
        }
    }
    
    @Override
    public CacheType getType() {
        return CacheType.REDIS;
    }
}
