<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
      </profile>
      <profile name="Annotation profile for RegaWebERP" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/projectlombok/lombok/1.18.26/lombok-1.18.26.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/projectlombok/lombok/1.18.26/lombok-1.18.26.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
        </processorPath>
        <module name="rega-common-sdk" />
        <module name="rega-common-i18n" />
        <module name="rega-common-security" />
        <module name="rega-report" />
        <module name="rega-form" />
        <module name="rega-system" />
        <module name="rega-api" />
        <module name="rega-tenant" />
        <module name="rega-workflow" />
        <module name="rega-common-log" />
        <module name="rega-common-core" />
      </profile>
      <profile name="Annotation profile for rega-common-cache" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/projectlombok/lombok/1.18.26/lombok-1.18.26.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
        </processorPath>
        <module name="rega-common-cache" />
      </profile>
      <profile name="Annotation profile for rega-common-db" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/projectlombok/lombok/1.18.26/lombok-1.18.26.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
        </processorPath>
        <module name="rega-common-db" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="rega-common-anyline" target="17" />
      <module name="rega-common-redis" target="17" />
    </bytecodeTargetLevel>
  </component>
</project>