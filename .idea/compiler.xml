<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Annotation profile for RegaWebERP" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/projectlombok/lombok/1.18.26/lombok-1.18.26.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
        </processorPath>
        <module name="rega-common-i18n" />
        <module name="rega-api" />
        <module name="rega-common-security" />
        <module name="rega-tenant" />
        <module name="rega-workflow" />
        <module name="rega-common-redis" />
        <module name="rega-form" />
        <module name="rega-common-log" />
        <module name="rega-system" />
      </profile>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="rega-common-sdk" />
        <module name="rega-common-db" />
      </profile>
      <profile name="Annotation profile for rega-common-core" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/projectlombok/lombok/1.18.26/lombok-1.18.26.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/projectlombok/lombok/1.18.26/lombok-1.18.26.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
        </processorPath>
        <module name="rega-common-core" />
      </profile>
      <profile name="Annotation profile for rega-report" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/projectlombok/lombok/1.18.26/lombok-1.18.26.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/projectlombok/lombok/1.18.26/lombok-1.18.26.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../maven/repo/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
        </processorPath>
        <module name="rega-report" />
      </profile>
    </annotationProcessing>
  </component>
</project>