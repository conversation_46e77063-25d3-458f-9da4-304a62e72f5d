<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="aliyun" />
      <option name="name" value="aliyun" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="huawei-mirror" />
      <option name="name" value="HuaweiCloud Mirror" />
      <option name="url" value="https://mirrors.huaweicloud.com/repository/maven/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="ali-mirror" />
      <option name="name" value="AliYun Mirror" />
      <option name="url" value="https://maven.aliyun.com/repository/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="ossrh" />
      <option name="name" value="OSS Sonatype Snapshots" />
      <option name="url" value="https://oss.sonatype.org/content/repositories/snapshots/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="ossrh" />
      <option name="name" value="ossrh" />
      <option name="url" value="https://oss.sonatype.org/content/repositories/snapshots/" />
    </remote-repository>
  </component>
</project>