<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/rega-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-anyline/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-anyline/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-cache/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-cache/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-db/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-db/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-i18n/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-i18n/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-log/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-log/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/rega-common-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-form/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-form/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-report/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-report/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-tenant/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-tenant/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-workflow/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/rega-workflow/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
  </component>
</project>