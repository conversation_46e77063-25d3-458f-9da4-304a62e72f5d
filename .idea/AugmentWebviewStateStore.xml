<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>