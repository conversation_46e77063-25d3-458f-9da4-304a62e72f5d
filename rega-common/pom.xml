<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.rega.erp</groupId>
        <artifactId>rega-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>rega-common</artifactId>
    <packaging>pom</packaging>
    <name>RegaWebERP Common</name>
    <description>RegaWebERP Common Modules</description>

    <modules>
        <module>rega-common-core</module>
        <module>rega-common-anyline</module>
        <module>rega-common-cache</module>
        <module>rega-common-security</module>
        <module>rega-common-log</module>
        <module>rega-common-i18n</module>
        <module>rega-common-db</module>
        <module>rega-common-sdk</module>
    </modules>
</project> 