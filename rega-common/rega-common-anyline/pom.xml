<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.rega.erp</groupId>
        <artifactId>rega-common</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>rega-common-anyline</artifactId>
    <name>rega-common-anyline</name>
    <description>RegaWebERP Anyline ORM 集成模块</description>

    <dependencies>
        <!-- 依赖核心模块 -->
        <dependency>
            <groupId>com.rega.erp</groupId>
            <artifactId>rega-common-core</artifactId>
        </dependency>

        <!-- Anyline 核心依赖 -->
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-data-jdbc</artifactId>
        </dependency>

        <!-- Anyline PostgreSQL 适配器 -->
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-data-jdbc-postgresql</artifactId>
        </dependency>

        <!-- PostgreSQL 驱动 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- Spring Boot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring Boot JDBC -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <!-- HikariCP 连接池 -->
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>

        <!-- CosID -->
        <dependency>
            <groupId>me.ahoo.cosid</groupId>
            <artifactId>cosid-spring-boot-starter</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Spring Boot Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
