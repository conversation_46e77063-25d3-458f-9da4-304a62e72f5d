package com.rega.erp.common.anyline.service;

import com.rega.erp.common.core.constant.ErrorCode;
import com.rega.erp.common.core.constant.ErrorMessage;
import com.rega.erp.common.core.domain.TenantBaseEntity;
import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.model.PageResult;
import com.rega.erp.common.core.util.Assert;
import com.rega.erp.common.core.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.anyline.data.param.ConfigStore;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户 Anyline 服务示例
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class UserAnylineService extends AnylineBaseService {

    /**
     * 用户实体类（示例）
     */
    public static class User extends TenantBaseEntity {
        private String username;
        private String password;
        private String realName;
        private String email;
        private String phone;
        private Integer gender;
        private LocalDateTime lastLoginTime;
        private String lastLoginIp;
        private Integer loginCount;

        // Getters and Setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        
        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        
        public Integer getGender() { return gender; }
        public void setGender(Integer gender) { this.gender = gender; }
        
        public LocalDateTime getLastLoginTime() { return lastLoginTime; }
        public void setLastLoginTime(LocalDateTime lastLoginTime) { this.lastLoginTime = lastLoginTime; }
        
        public String getLastLoginIp() { return lastLoginIp; }
        public void setLastLoginIp(String lastLoginIp) { this.lastLoginIp = lastLoginIp; }
        
        public Integer getLoginCount() { return loginCount; }
        public void setLoginCount(Integer loginCount) { this.loginCount = loginCount; }
    }

    /**
     * 创建用户
     * 
     * @param user 用户对象
     * @return 用户ID
     */
    public Long createUser(User user) {
        Assert.notNull(user, ErrorMessage.USER_CREATE_ERROR);
        Assert.hasText(user.getUsername(), "用户名不能为空");
        Assert.hasText(user.getPassword(), "密码不能为空");

        // 检查用户名是否已存在
        User existingUser = findByUsername(user.getUsername());
        Assert.dataNotExists(existingUser);

        try {
            // 设置基础信息
            user.setId(generateId());
            user.setTenantId(getCurrentTenantId());
            user.setCreateTime(LocalDateTime.now());
            user.setCreateBy(getCurrentUserId());
            user.setUpdateTime(LocalDateTime.now());
            user.setUpdateBy(getCurrentUserId());
            user.setDeleted(0);
            user.setStatus(1);
            user.setLoginCount(0);

            // 使用 Anyline 保存用户
            int result = insert(user);
            
            if (result > 0) {
                log.info("用户创建成功: username={}, id={}", user.getUsername(), user.getId());
                return user.getId();
            } else {
                throw new BusinessException(ErrorCode.USER_CREATE_ERROR, ErrorMessage.USER_CREATE_ERROR);
            }
        } catch (Exception e) {
            log.error("创建用户失败: username={}", user.getUsername(), e);
            throw new BusinessException(ErrorCode.USER_CREATE_ERROR, "创建用户失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户对象
     */
    public User findByUsername(String username) {
        Assert.hasText(username, "用户名不能为空");

        try {
            ConfigStore configs = createBaseConfigs();
            configs.and("USERNAME", username);
            
            return service.select(User.class, configs);
        } catch (Exception e) {
            log.error("根据用户名查询用户失败: username={}", username, e);
            throw new BusinessException(ErrorCode.DATABASE_ERROR, "查询用户失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询用户
     * 
     * @param username 用户名（模糊查询）
     * @param realName 真实姓名（模糊查询）
     * @param status 状态
     * @param current 当前页码
     * @param size 每页大小
     * @return 分页结果
     */
    public PageResult<User> queryUsers(String username, String realName, Integer status, 
                                      long current, long size) {
        try {
            ConfigStore configs = createBaseConfigs();
            
            // 动态条件
            if (StringUtils.isNotEmpty(username)) {
                configs.and("USERNAME", "LIKE", "%" + username + "%");
            }
            
            if (StringUtils.isNotEmpty(realName)) {
                configs.and("REAL_NAME", "LIKE", "%" + realName + "%");
            }
            
            if (status != null) {
                configs.and("STATUS", status);
            }
            
            return findPage(User.class, configs, current, size);
        } catch (Exception e) {
            log.error("分页查询用户失败: username={}, realName={}, status={}", 
                     username, realName, status, e);
            throw new BusinessException(ErrorCode.DATABASE_ERROR, "查询用户失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户信息
     * 
     * @param user 用户对象
     * @return 是否更新成功
     */
    public boolean updateUser(User user) {
        Assert.notNull(user, ErrorMessage.USER_UPDATE_ERROR);
        Assert.validId(user.getId(), "用户ID无效");

        try {
            // 检查用户是否存在
            User existingUser = findById(User.class, user.getId());
            Assert.userExists(existingUser);

            // 设置更新信息
            user.setUpdateTime(LocalDateTime.now());
            user.setUpdateBy(getCurrentUserId());

            int result = update(user);
            
            if (result > 0) {
                log.info("用户更新成功: id={}, username={}", user.getId(), user.getUsername());
                return true;
            } else {
                throw new BusinessException(ErrorCode.USER_UPDATE_ERROR, ErrorMessage.USER_UPDATE_ERROR);
            }
        } catch (Exception e) {
            log.error("更新用户失败: id={}", user.getId(), e);
            throw new BusinessException(ErrorCode.USER_UPDATE_ERROR, "更新用户失败: " + e.getMessage());
        }
    }

    /**
     * 删除用户（逻辑删除）
     * 
     * @param userId 用户ID
     * @return 是否删除成功
     */
    public boolean deleteUser(Long userId) {
        Assert.validId(userId, "用户ID无效");

        try {
            // 检查用户是否存在
            User existingUser = findById(User.class, userId);
            Assert.userExists(existingUser);

            boolean result = deleteById(User.class, userId);
            
            if (result) {
                log.info("用户删除成功: id={}, username={}", userId, existingUser.getUsername());
                return true;
            } else {
                throw new BusinessException(ErrorCode.USER_DELETE_ERROR, ErrorMessage.USER_DELETE_ERROR);
            }
        } catch (Exception e) {
            log.error("删除用户失败: id={}", userId, e);
            throw new BusinessException(ErrorCode.USER_DELETE_ERROR, "删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户登录信息
     * 
     * @param userId 用户ID
     * @param loginIp 登录IP
     */
    public void updateLoginInfo(Long userId, String loginIp) {
        Assert.validId(userId, "用户ID无效");

        try {
            User user = findById(User.class, userId);
            Assert.userExists(user);

            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp(loginIp);
            user.setLoginCount(user.getLoginCount() + 1);
            user.setUpdateTime(LocalDateTime.now());
            user.setUpdateBy(getCurrentUserId());

            update(user);
            
            log.info("用户登录信息更新成功: id={}, ip={}", userId, loginIp);
        } catch (Exception e) {
            log.error("更新用户登录信息失败: id={}, ip={}", userId, loginIp, e);
            // 登录信息更新失败不影响登录流程，只记录日志
        }
    }

    /**
     * 统计用户数量
     * 
     * @return 用户总数
     */
    public long countUsers() {
        try {
            ConfigStore configs = createBaseConfigs();
            configs.and("STATUS", 1); // 只统计正常状态的用户
            
            return service.count(User.class, configs);
        } catch (Exception e) {
            log.error("统计用户数量失败", e);
            return 0;
        }
    }
}
