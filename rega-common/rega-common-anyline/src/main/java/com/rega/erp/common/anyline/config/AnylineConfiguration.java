package com.rega.erp.common.anyline.config;

import lombok.extern.slf4j.Slf4j;
import org.anyline.data.adapter.DriverAdapter;
import org.anyline.data.runtime.DataRuntime;
import org.anyline.service.AnylineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;

/**
 * Anyline 配置类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Configuration
@ConditionalOnClass(AnylineService.class)
public class AnylineConfiguration {

    @Autowired(required = false)
    private DataSource dataSource;

    @Autowired(required = false)
    private AnylineService anylineService;

    /**
     * 初始化配置
     */
    @PostConstruct
    public void init() {
        log.info("=== Anyline ORM 配置初始化 ===");
        
        if (dataSource != null) {
            log.info("✅ 数据源配置成功: {}", dataSource.getClass().getSimpleName());
        } else {
            log.warn("⚠️  未找到数据源配置");
        }
        
        if (anylineService != null) {
            log.info("✅ AnylineService 注入成功");
            
            try {
                // 测试数据库连接
                testDatabaseConnection();
            } catch (Exception e) {
                log.error("❌ 数据库连接测试失败: {}", e.getMessage());
            }
        } else {
            log.warn("⚠️  AnylineService 未注入");
        }
        
        log.info("=== Anyline ORM 配置完成 ===");
    }

    /**
     * 测试数据库连接
     */
    private void testDatabaseConnection() {
        try {
            // 获取数据库信息
            DataRuntime runtime = anylineService.runtime();
            if (runtime != null) {
                DriverAdapter adapter = runtime.getAdapter();
                if (adapter != null) {
                    log.info("数据库类型: {}", adapter.type());
                    log.info("数据库版本: {}", adapter.version());
                }
            }
            
            // 测试简单查询
            String sql = "SELECT 1 as test_connection";
            Object result = anylineService.query(sql);
            log.info("✅ 数据库连接测试成功: {}", result);
            
        } catch (Exception e) {
            log.error("数据库连接测试失败", e);
            throw e;
        }
    }

    /**
     * 配置主数据源的 AnylineService
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(name = "primaryAnylineService")
    public AnylineService primaryAnylineService() {
        if (anylineService != null) {
            log.info("使用自动配置的 AnylineService");
            return anylineService;
        }
        
        log.warn("未找到 AnylineService，请检查配置");
        return null;
    }
}
