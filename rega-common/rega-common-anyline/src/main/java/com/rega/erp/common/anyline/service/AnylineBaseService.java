package com.rega.erp.common.anyline.service;

import com.rega.erp.common.core.constant.ErrorCode;
import com.rega.erp.common.core.constant.ErrorMessage;
import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.model.PageResult;
import com.rega.erp.common.core.util.Assert;
import lombok.extern.slf4j.Slf4j;
import org.anyline.data.param.ConfigStore;
import org.anyline.data.runtime.DataRuntime;
import org.anyline.entity.DataRow;
import org.anyline.entity.DataSet;
import org.anyline.entity.PageNavi;
import org.anyline.service.AnylineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Anyline 基础服务类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class AnylineBaseService {

    @Autowired
    @Qualifier("anyline.service")
    protected AnylineService service;

    /**
     * 生成雪花算法ID
     * 
     * @return 雪花算法ID
     */
    protected Long generateId() {
        try {
            return service.primary().generate();
        } catch (Exception e) {
            log.warn("使用 Anyline 生成ID失败，使用时间戳备选方案: {}", e.getMessage());
            return System.currentTimeMillis() * 1000 + (long) (Math.random() * 1000);
        }
    }

    /**
     * 获取当前租户ID
     * 
     * @return 租户ID
     */
    protected Long getCurrentTenantId() {
        // TODO: 从上下文获取当前租户ID
        return 1L; // 临时返回默认租户ID
    }

    /**
     * 获取当前用户ID
     * 
     * @return 用户ID
     */
    protected Long getCurrentUserId() {
        // TODO: 从安全上下文获取当前用户ID
        return 1L; // 临时返回默认用户ID
    }

    /**
     * 保存实体（新增或更新）
     *
     * @param entity 实体对象
     * @param <T> 实体类型
     * @return 保存后的实体
     */
    public <T> T save(T entity) {
        Assert.notNull(entity, "实体对象不能为空");

        try {
            // 使用 Anyline 的 save 方法，自动判断新增或更新
            int result = service.save(entity);

            if (result > 0) {
                return entity;
            } else {
                throw new BusinessException(ErrorCode.DATABASE_ERROR, "保存数据失败");
            }
        } catch (Exception e) {
            log.error("保存实体失败: entity={}", entity, e);
            throw new BusinessException(ErrorCode.DATABASE_ERROR, "保存数据失败: " + e.getMessage());
        }
    }

    /**
     * 插入实体
     *
     * @param entity 实体对象
     * @param <T> 实体类型
     * @return 插入的记录数
     */
    public <T> int insert(T entity) {
        Assert.notNull(entity, "实体对象不能为空");

        try {
            return service.insert(entity);
        } catch (Exception e) {
            log.error("插入实体失败: entity={}", entity, e);
            throw new BusinessException(ErrorCode.DATABASE_ERROR, "插入数据失败: " + e.getMessage());
        }
    }

    /**
     * 更新实体
     *
     * @param entity 实体对象
     * @param <T> 实体类型
     * @return 更新的记录数
     */
    public <T> int update(T entity) {
        Assert.notNull(entity, "实体对象不能为空");

        try {
            return service.update(entity);
        } catch (Exception e) {
            log.error("更新实体失败: entity={}", entity, e);
            throw new BusinessException(ErrorCode.DATABASE_ERROR, "更新数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询实体
     *
     * @param clazz 实体类型
     * @param id 主键ID
     * @param <T> 实体类型
     * @return 实体对象
     */
    public <T> T findById(Class<T> clazz, Long id) {
        Assert.notNull(clazz, "实体类型不能为空");
        Assert.validId(id, "ID无效");

        try {
            ConfigStore configs = new ConfigStore();
            configs.and("ID", id);
            configs.and("TENANT_ID", getCurrentTenantId());
            configs.and("DELETED", 0);

            return service.select(clazz, configs);
        } catch (Exception e) {
            log.error("根据ID查询实体失败: clazz={}, id={}", clazz.getSimpleName(), id, e);
            throw new BusinessException(ErrorCode.DATABASE_ERROR, "查询数据失败: " + e.getMessage());
        }
    }

    /**
     * 查询实体列表
     *
     * @param clazz 实体类型
     * @param configs 查询条件
     * @param <T> 实体类型
     * @return 实体列表
     */
    public <T> List<T> findList(Class<T> clazz, ConfigStore configs) {
        Assert.notNull(clazz, "实体类型不能为空");

        try {
            if (configs == null) {
                configs = new ConfigStore();
            }

            // 添加租户隔离和逻辑删除条件
            configs.and("TENANT_ID", getCurrentTenantId());
            configs.and("DELETED", 0);

            return service.selects(clazz, configs);
        } catch (Exception e) {
            log.error("查询实体列表失败: clazz={}, configs={}", clazz.getSimpleName(), configs, e);
            throw new BusinessException(ErrorCode.DATABASE_ERROR, "查询数据失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询实体
     *
     * @param clazz 实体类型
     * @param configs 查询条件
     * @param current 当前页码
     * @param size 每页大小
     * @param <T> 实体类型
     * @return 分页结果
     */
    public <T> PageResult<T> findPage(Class<T> clazz, ConfigStore configs, long current, long size) {
        Assert.notNull(clazz, "实体类型不能为空");

        try {
            if (configs == null) {
                configs = new ConfigStore();
            }

            // 添加租户隔离和逻辑删除条件
            configs.and("TENANT_ID", getCurrentTenantId());
            configs.and("DELETED", 0);

            PageNavi navi = new PageNavi((int) current, (int) size);
            List<T> records = service.selects(clazz, configs, navi);

            return PageResult.ok(records, navi.getTotalRow(), current, size);
        } catch (Exception e) {
            log.error("分页查询实体失败: clazz={}, configs={}, current={}, size={}",
                     clazz.getSimpleName(), configs, current, size, e);
            throw new BusinessException(ErrorCode.DATABASE_ERROR, "查询数据失败: " + e.getMessage());
        }
    }

    /**
     * 删除实体
     *
     * @param entity 实体对象
     * @param <T> 实体类型
     * @return 删除的记录数
     */
    public <T> int delete(T entity) {
        Assert.notNull(entity, "实体对象不能为空");

        try {
            return service.delete(entity);
        } catch (Exception e) {
            log.error("删除实体失败: entity={}", entity, e);
            throw new BusinessException(ErrorCode.DATABASE_ERROR, "删除数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID删除实体（逻辑删除）
     *
     * @param clazz 实体类型
     * @param id 主键ID
     * @param <T> 实体类型
     * @return 是否删除成功
     */
    public <T> boolean deleteById(Class<T> clazz, Long id) {
        Assert.notNull(clazz, "实体类型不能为空");
        Assert.validId(id, "ID无效");

        try {
            ConfigStore configs = new ConfigStore();
            configs.and("ID", id);
            configs.and("TENANT_ID", getCurrentTenantId());
            configs.and("DELETED", 0);

            DataRow updateData = new DataRow();
            updateData.put("DELETED", 1);
            updateData.put("UPDATE_TIME", LocalDateTime.now());
            updateData.put("UPDATE_BY", getCurrentUserId());

            int result = service.update(clazz, updateData, configs);
            return result > 0;
        } catch (Exception e) {
            log.error("删除实体失败: clazz={}, id={}", clazz.getSimpleName(), id, e);
            throw new BusinessException(ErrorCode.DATABASE_ERROR, "删除数据失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除实体（逻辑删除）
     * 
     * @param tableName 表名
     * @param ids 主键ID列表
     * @return 删除的记录数
     */
    public int deleteByIds(String tableName, List<Long> ids) {
        Assert.hasText(tableName, "表名不能为空");
        Assert.notEmpty(ids, "ID列表不能为空");

        try {
            ConfigStore configs = new ConfigStore();
            configs.and("ID", "IN", ids);
            configs.and("TENANT_ID", getCurrentTenantId());
            configs.and("DELETED", 0);
            
            DataRow updateData = new DataRow();
            updateData.put("DELETED", 1);
            updateData.put("UPDATE_TIME", LocalDateTime.now());
            updateData.put("UPDATE_BY", getCurrentUserId());
            
            return service.update(tableName, updateData, configs);
        } catch (Exception e) {
            log.error("批量删除实体失败: tableName={}, ids={}", tableName, ids, e);
            throw new BusinessException(ErrorCode.DATABASE_ERROR, "删除数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取表名（从实体类推断）
     *
     * @param clazz 实体类型
     * @return 表名
     */
    protected String getTableName(Class<?> clazz) {
        // Anyline 会自动根据类名推断表名
        // 默认：UserEntity -> user_entity
        return clazz.getSimpleName().toLowerCase();
    }

    /**
     * 创建基础查询条件（包含租户隔离和逻辑删除）
     *
     * @return ConfigStore
     */
    protected ConfigStore createBaseConfigs() {
        ConfigStore configs = new ConfigStore();
        configs.and("TENANT_ID", getCurrentTenantId());
        configs.and("DELETED", 0);
        return configs;
    }
}
