package com.rega.erp.common.anyline.converter;

import com.rega.erp.common.core.domain.BaseEntity;
import com.rega.erp.common.core.domain.TenantBaseEntity;
import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.constant.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.anyline.entity.DataRow;
import org.anyline.entity.DataSet;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 实体转换器
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class EntityConverter {

    /**
     * 实体转换为 DataRow
     * 
     * @param entity 实体对象
     * @return DataRow
     */
    public DataRow convertToDataRow(Object entity) {
        if (entity == null) {
            return null;
        }

        DataRow row = new DataRow();
        
        try {
            Class<?> clazz = entity.getClass();
            
            // 处理所有字段，包括父类字段
            while (clazz != null && clazz != Object.class) {
                Field[] fields = clazz.getDeclaredFields();
                
                for (Field field : fields) {
                    field.setAccessible(true);
                    Object value = field.get(entity);
                    
                    if (value != null) {
                        String columnName = convertFieldNameToColumnName(field.getName());
                        row.put(columnName, value);
                    }
                }
                
                clazz = clazz.getSuperclass();
            }
            
        } catch (Exception e) {
            log.error("实体转换为 DataRow 失败: entity={}", entity, e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "实体转换失败: " + e.getMessage());
        }
        
        return row;
    }

    /**
     * DataRow 转换为实体
     * 
     * @param row DataRow
     * @param clazz 实体类型
     * @param <T> 实体类型
     * @return 实体对象
     */
    public <T> T convertFromDataRow(DataRow row, Class<T> clazz) {
        if (row == null) {
            return null;
        }

        try {
            T entity = clazz.getDeclaredConstructor().newInstance();
            
            // 处理所有字段，包括父类字段
            Class<?> currentClass = clazz;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                
                for (Field field : fields) {
                    field.setAccessible(true);
                    String columnName = convertFieldNameToColumnName(field.getName());
                    
                    if (row.containsKey(columnName)) {
                        Object value = row.get(columnName);
                        if (value != null) {
                            // 类型转换
                            Object convertedValue = convertValue(value, field.getType());
                            field.set(entity, convertedValue);
                        }
                    }
                }
                
                currentClass = currentClass.getSuperclass();
            }
            
            return entity;
            
        } catch (Exception e) {
            log.error("DataRow 转换为实体失败: row={}, clazz={}", row, clazz, e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "实体转换失败: " + e.getMessage());
        }
    }

    /**
     * DataSet 转换为实体列表
     * 
     * @param dataSet DataSet
     * @param clazz 实体类型
     * @param <T> 实体类型
     * @return 实体列表
     */
    public <T> List<T> convertFromDataSet(DataSet dataSet, Class<T> clazz) {
        if (dataSet == null || dataSet.isEmpty()) {
            return List.of();
        }
        
        return dataSet.stream()
                .map(row -> convertFromDataRow(row, clazz))
                .collect(Collectors.toList());
    }

    /**
     * 字段名转换为列名
     * Java 驼峰命名 -> 数据库下划线命名
     * 
     * @param fieldName 字段名
     * @return 列名
     */
    private String convertFieldNameToColumnName(String fieldName) {
        if (fieldName == null || fieldName.isEmpty()) {
            return fieldName;
        }
        
        // 特殊字段映射
        switch (fieldName) {
            case "searchValue":
            case "params":
                return null; // 这些字段不需要映射到数据库
        }
        
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < fieldName.length(); i++) {
            char c = fieldName.charAt(i);
            
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    result.append('_');
                }
                result.append(Character.toLowerCase(c));
            } else {
                result.append(Character.toUpperCase(c));
            }
        }
        
        return result.toString();
    }

    /**
     * 值类型转换
     * 
     * @param value 原始值
     * @param targetType 目标类型
     * @return 转换后的值
     */
    private Object convertValue(Object value, Class<?> targetType) {
        if (value == null || targetType.isAssignableFrom(value.getClass())) {
            return value;
        }
        
        try {
            // String 类型转换
            if (targetType == String.class) {
                return value.toString();
            }
            
            // 数值类型转换
            if (targetType == Long.class || targetType == long.class) {
                if (value instanceof Number) {
                    return ((Number) value).longValue();
                }
                return Long.parseLong(value.toString());
            }
            
            if (targetType == Integer.class || targetType == int.class) {
                if (value instanceof Number) {
                    return ((Number) value).intValue();
                }
                return Integer.parseInt(value.toString());
            }
            
            if (targetType == Double.class || targetType == double.class) {
                if (value instanceof Number) {
                    return ((Number) value).doubleValue();
                }
                return Double.parseDouble(value.toString());
            }
            
            if (targetType == Float.class || targetType == float.class) {
                if (value instanceof Number) {
                    return ((Number) value).floatValue();
                }
                return Float.parseFloat(value.toString());
            }
            
            // 布尔类型转换
            if (targetType == Boolean.class || targetType == boolean.class) {
                if (value instanceof Number) {
                    return ((Number) value).intValue() != 0;
                }
                return Boolean.parseBoolean(value.toString());
            }
            
            // 时间类型转换
            if (targetType == LocalDateTime.class) {
                if (value instanceof java.sql.Timestamp) {
                    return ((java.sql.Timestamp) value).toLocalDateTime();
                }
                // 其他时间类型转换可以在这里添加
            }
            
            // 默认返回原值
            return value;
            
        } catch (Exception e) {
            log.warn("值类型转换失败: value={}, targetType={}, error={}", 
                    value, targetType.getSimpleName(), e.getMessage());
            return value;
        }
    }

    /**
     * 填充基础实体字段
     * 
     * @param row DataRow
     * @param entity 实体对象
     * @param isInsert 是否为插入操作
     */
    public void fillBaseEntityFields(DataRow row, Object entity, boolean isInsert) {
        if (entity instanceof BaseEntity) {
            BaseEntity baseEntity = (BaseEntity) entity;
            
            if (isInsert) {
                // 插入时设置创建信息
                if (baseEntity.getCreateTime() == null) {
                    baseEntity.setCreateTime(LocalDateTime.now());
                    row.put("CREATE_TIME", baseEntity.getCreateTime());
                }
                
                if (baseEntity.getCreateBy() == null) {
                    // TODO: 从上下文获取当前用户ID
                    baseEntity.setCreateBy(1L);
                    row.put("CREATE_BY", baseEntity.getCreateBy());
                }
                
                if (baseEntity.getDeleted() == null) {
                    baseEntity.setDeleted(0);
                    row.put("DELETED", baseEntity.getDeleted());
                }
            }
            
            // 更新时间
            baseEntity.setUpdateTime(LocalDateTime.now());
            row.put("UPDATE_TIME", baseEntity.getUpdateTime());
            
            if (baseEntity.getUpdateBy() == null) {
                // TODO: 从上下文获取当前用户ID
                baseEntity.setUpdateBy(1L);
                row.put("UPDATE_BY", baseEntity.getUpdateBy());
            }
        }
        
        if (entity instanceof TenantBaseEntity) {
            TenantBaseEntity tenantEntity = (TenantBaseEntity) entity;
            
            if (tenantEntity.getTenantId() == null) {
                // TODO: 从上下文获取当前租户ID
                tenantEntity.setTenantId(1L);
                row.put("TENANT_ID", tenantEntity.getTenantId());
            }
            
            if (isInsert && tenantEntity.getStatus() == null) {
                tenantEntity.setStatus(1);
                row.put("STATUS", tenantEntity.getStatus());
            }
            
            if (isInsert && tenantEntity.getSortOrder() == null) {
                tenantEntity.setSortOrder(0);
                row.put("SORT_ORDER", tenantEntity.getSortOrder());
            }
        }
    }
}
