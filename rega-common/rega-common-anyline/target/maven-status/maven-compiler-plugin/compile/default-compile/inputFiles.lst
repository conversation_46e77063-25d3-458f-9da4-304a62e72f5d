/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-common/rega-common-anyline/src/main/java/com/rega/erp/common/anyline/config/AnylineAutoConfiguration.java
/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-common/rega-common-anyline/src/main/java/com/rega/erp/common/anyline/service/AnylineBaseService.java
/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-common/rega-common-anyline/src/main/java/com/rega/erp/common/anyline/converter/EntityConverter.java
/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-common/rega-common-anyline/src/main/java/com/rega/erp/common/anyline/config/AnylineConfiguration.java
/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-common/rega-common-anyline/src/main/java/com/rega/erp/common/anyline/service/UserAnylineService.java
