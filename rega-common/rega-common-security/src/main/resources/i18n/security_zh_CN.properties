# 安全模块国际化消息 - 中文
security.login.success=登录成功
security.login.failed=登录失败
security.login.username.empty=用户名不能为空
security.login.password.empty=密码不能为空
security.login.captcha.empty=验证码不能为空
security.login.captcha.invalid=验证码错误
security.login.captcha.expired=验证码已过期
security.login.user.not.found=用户不存在
security.login.user.disabled=用户已被禁用
security.login.user.locked=用户已被锁定
security.login.password.incorrect=密码错误
security.login.max.retry.exceeded=登录失败次数过多，账户已被锁定{0}分钟
security.login.tenant.invalid=租户信息无效
security.login.tenant.disabled=租户已被禁用

security.logout.success=退出成功
security.logout.failed=退出失败

security.permission.denied=权限不足
security.permission.required=需要权限：{0}
security.role.denied=角色权限不足
security.role.required=需要角色：{0}
security.login.required=请先登录

security.password.weak=密码强度不足
security.password.too.short=密码长度不能少于{0}位
security.password.too.long=密码长度不能超过{0}位
security.password.contains.username=密码不能包含用户名
security.password.common=密码过于简单，请使用更复杂的密码
security.password.complexity.low=密码复杂度不足，请包含大小写字母、数字和特殊字符
security.password.change.success=密码修改成功
security.password.change.failed=密码修改失败
security.password.old.incorrect=原密码错误
security.password.new.same=新密码不能与原密码相同

security.token.invalid=令牌无效
security.token.expired=令牌已过期
security.token.refresh.success=令牌刷新成功
security.token.refresh.failed=令牌刷新失败

security.user.id.get.error=获取用户ID异常
security.user.info.get.error=获取用户信息异常
security.tenant.get.error=获取租户信息异常
security.tenant.set.error=设置租户信息异常

security.sms.code.invalid=短信验证码错误
security.sms.code.expired=短信验证码已过期
security.sms.code.send.success=短信验证码发送成功
security.sms.code.send.failed=短信验证码发送失败
security.sms.code.frequent=短信验证码发送过于频繁

security.social.login.failed=第三方登录失败
security.social.bind.success=第三方账号绑定成功
security.social.bind.failed=第三方账号绑定失败
security.social.unbind.success=第三方账号解绑成功
security.social.unbind.failed=第三方账号解绑失败

security.data.scope.denied=数据权限不足
security.data.scope.invalid=数据权限配置无效

security.session.timeout=会话超时，请重新登录
security.session.invalid=会话无效
security.session.concurrent=您的账号在其他地方登录，请重新登录

security.encrypt.failed=加密失败
security.decrypt.failed=解密失败
security.sign.failed=签名失败
security.verify.failed=验证失败
