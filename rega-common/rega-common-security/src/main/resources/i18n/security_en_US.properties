# Security Module Internationalization Messages - English
security.login.success=Login successful
security.login.failed=Login failed
security.login.username.empty=Username cannot be empty
security.login.password.empty=Password cannot be empty
security.login.captcha.empty=Captcha cannot be empty
security.login.captcha.invalid=Invalid captcha
security.login.captcha.expired=Captcha has expired
security.login.user.not.found=User not found
security.login.user.disabled=User has been disabled
security.login.user.locked=User has been locked
security.login.password.incorrect=Incorrect password
security.login.max.retry.exceeded=Too many login failures, account locked for {0} minutes
security.login.tenant.invalid=Invalid tenant information
security.login.tenant.disabled=Tenant has been disabled

security.logout.success=Logout successful
security.logout.failed=Logout failed

security.permission.denied=Permission denied
security.permission.required=Permission required: {0}
security.role.denied=Role permission denied
security.role.required=Role required: {0}
security.login.required=Please login first

security.password.weak=Password strength insufficient
security.password.too.short=Password length cannot be less than {0} characters
security.password.too.long=Password length cannot exceed {0} characters
security.password.contains.username=Password cannot contain username
security.password.common=Password is too simple, please use a more complex password
security.password.complexity.low=Password complexity insufficient, please include uppercase, lowercase, numbers and special characters
security.password.change.success=Password changed successfully
security.password.change.failed=Password change failed
security.password.old.incorrect=Old password is incorrect
security.password.new.same=New password cannot be the same as old password

security.token.invalid=Invalid token
security.token.expired=Token has expired
security.token.refresh.success=Token refreshed successfully
security.token.refresh.failed=Token refresh failed

security.user.id.get.error=Error getting user ID
security.user.dept.get.error=Error getting department ID
security.user.username.get.error=Error getting username
security.user.nickname.get.error=Error getting user nickname
security.user.info.get.error=Error getting user information
security.tenant.get.error=Error getting tenant information
security.tenant.set.error=Error setting tenant information

security.sms.code.invalid=Invalid SMS verification code
security.sms.code.expired=SMS verification code has expired
security.sms.code.send.success=SMS verification code sent successfully
security.sms.code.send.failed=Failed to send SMS verification code
security.sms.code.frequent=SMS verification code sent too frequently

security.social.login.failed=Third-party login failed
security.social.bind.success=Third-party account bound successfully
security.social.bind.failed=Third-party account binding failed
security.social.unbind.success=Third-party account unbound successfully
security.social.unbind.failed=Third-party account unbinding failed

security.data.scope.denied=Data permission denied
security.data.scope.invalid=Invalid data permission configuration

security.session.timeout=Session timeout, please login again
security.session.invalid=Invalid session
security.session.concurrent=Your account is logged in elsewhere, please login again

security.encrypt.failed=Encryption failed
security.decrypt.failed=Decryption failed
security.sign.failed=Signature failed
security.verify.failed=Verification failed
