package com.rega.erp.common.security.listener;

import com.rega.erp.common.security.event.LoginEvent;
import com.rega.erp.common.security.event.PermissionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 安全事件监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SecurityEventListener {

    /**
     * 监听登录事件
     */
    @Async("securityTaskExecutor")
    @EventListener
    public void handleLoginEvent(LoginEvent event) {
        try {
            if (event.isSuccess()) {
                log.info("用户登录成功: 租户={}, 用户ID={}, 用户名={}, IP={}, 登录类型={}, 来源={}", 
                        event.getTenantId(), event.getUserId(), event.getUsername(), 
                        event.getClientIp(), event.getLoginType(), event.getLoginSource());
            } else {
                log.warn("用户登录失败: 租户={}, 用户名={}, IP={}, 登录类型={}, 失败原因={}, 来源={}", 
                        event.getTenantId(), event.getUsername(), event.getClientIp(), 
                        event.getLoginType(), event.getFailureReason(), event.getLoginSource());
            }
            
            // 这里可以扩展其他处理逻辑，如：
            // 1. 记录登录日志到数据库
            // 2. 发送登录通知
            // 3. 更新用户最后登录时间
            // 4. 统计登录次数
            
        } catch (Exception e) {
            log.error("处理登录事件失败", e);
        }
    }

    /**
     * 监听权限验证事件
     */
    @Async("securityTaskExecutor")
    @EventListener
    public void handlePermissionEvent(PermissionEvent event) {
        try {
            if (!event.isGranted()) {
                log.warn("权限验证失败: 租户={}, 用户ID={}, 用户名={}, IP={}, 权限类型={}, 权限={}, 资源={}, 方法={}", 
                        event.getTenantId(), event.getUserId(), event.getUsername(), 
                        event.getClientIp(), event.getPermissionType(), event.getPermission(),
                        event.getResource(), event.getMethod());
                
                // 这里可以扩展其他处理逻辑，如：
                // 1. 记录权限拒绝日志
                // 2. 发送安全告警
                // 3. 统计权限拒绝次数
                // 4. 触发安全策略
            } else {
                log.debug("权限验证通过: 租户={}, 用户ID={}, 权限类型={}, 权限={}", 
                         event.getTenantId(), event.getUserId(), event.getPermissionType(), event.getPermission());
            }
            
        } catch (Exception e) {
            log.error("处理权限验证事件失败", e);
        }
    }
}
