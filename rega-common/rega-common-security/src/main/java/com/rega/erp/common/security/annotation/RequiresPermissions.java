package com.rega.erp.common.security.annotation;

import java.lang.annotation.*;

/**
 * 权限认证注解
 *
 * <AUTHOR>
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresPermissions {

    /**
     * 需要校验的权限码
     */
    String[] value() default {};

    /**
     * 验证模式：AND | OR，默认AND
     */
    Logical logical() default Logical.AND;

    /**
     * 多账号体系下所属的账号体系标识
     */
    String type() default "";

    /**
     * 验证未通过时的提示消息
     */
    String message() default "权限不足";

    /**
     * 逻辑枚举
     */
    enum Logical {
        /**
         * 必须具有所有的元素
         */
        AND,

        /**
         * 只需具有其中一个元素
         */
        OR
    }
}
