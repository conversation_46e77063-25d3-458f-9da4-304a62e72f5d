package com.rega.erp.common.security.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 安全配置属性
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "rega.security")
public class SecurityConfig {

    /**
     * 是否启用安全模块
     */
    private boolean enabled = true;

    /**
     * 排除路径
     */
    private List<String> excludes;

    /**
     * 密码配置
     */
    private Password password = new Password();

    /**
     * 验证码配置
     */
    private Captcha captcha = new Captcha();

    /**
     * 登录配置
     */
    private Login login = new Login();

    /**
     * 密码配置
     */
    @Data
    public static class Password {
        /**
         * 密码最小长度
         */
        private int minLength = 6;

        /**
         * 密码最大长度
         */
        private int maxLength = 20;

        /**
         * 密码复杂度要求 (1-简单, 2-中等, 3-复杂)
         */
        private int complexity = 2;

        /**
         * 是否检查弱密码
         */
        private boolean checkWeak = true;

        /**
         * 是否检查密码包含用户名
         */
        private boolean checkUsername = true;

        /**
         * 密码重试次数
         */
        private int maxRetryCount = 5;

        /**
         * 密码锁定时间（分钟）
         */
        private int lockTime = 10;
    }

    /**
     * 验证码配置
     */
    @Data
    public static class Captcha {
        /**
         * 是否启用验证码
         */
        private boolean enabled = true;

        /**
         * 验证码类型 (math-数学, char-字符, line-线条)
         */
        private String type = "math";

        /**
         * 验证码长度
         */
        private int length = 4;

        /**
         * 验证码过期时间（分钟）
         */
        private int expireTime = 2;

        /**
         * 验证码宽度
         */
        private int width = 111;

        /**
         * 验证码高度
         */
        private int height = 36;
    }

    /**
     * 登录配置
     */
    @Data
    public static class Login {
        /**
         * 登录失败最大次数
         */
        private int maxRetryCount = 5;

        /**
         * 登录锁定时间（分钟）
         */
        private int lockTime = 10;

        /**
         * 是否单点登录
         */
        private boolean singleLogin = false;

        /**
         * 会话超时时间（分钟）
         */
        private int sessionTimeout = 30;

        /**
         * 记住我时间（天）
         */
        private int rememberMeTimeout = 7;
    }
}
