package com.rega.erp.common.security.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 安全事件基类
 *
 * <AUTHOR>
 */
@Getter
public abstract class SecurityEvent extends ApplicationEvent {

    /**
     * 事件类型
     */
    private final String eventType;

    /**
     * 租户ID
     */
    private final String tenantId;

    /**
     * 用户ID
     */
    private final Long userId;

    /**
     * 用户名
     */
    private final String username;

    /**
     * 客户端IP
     */
    private final String clientIp;

    /**
     * 用户代理
     */
    private final String userAgent;

    /**
     * 事件时间
     */
    private final LocalDateTime eventTime;

    /**
     * 事件描述
     */
    private final String description;

    public SecurityEvent(Object source, String eventType, String tenantId, Long userId, 
                        String username, String clientIp, String userAgent, String description) {
        super(source);
        this.eventType = eventType;
        this.tenantId = tenantId;
        this.userId = userId;
        this.username = username;
        this.clientIp = clientIp;
        this.userAgent = userAgent;
        this.description = description;
        this.eventTime = LocalDateTime.now();
    }
}
