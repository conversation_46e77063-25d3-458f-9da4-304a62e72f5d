package com.rega.erp.common.security.aspect;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.dev33.satoken.stp.StpUtil;
import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.util.ServletUtils;
import com.rega.erp.common.security.annotation.RequiresLogin;
import com.rega.erp.common.security.annotation.RequiresPermissions;
import com.rega.erp.common.security.annotation.RequiresRoles;
import com.rega.erp.common.security.domain.LoginUser;
import com.rega.erp.common.security.event.PermissionEvent;
import com.rega.erp.common.security.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Set;

/**
 * 权限认证切面
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@Order(1)
@RequiredArgsConstructor
public class PermissionAspect {

    private final ApplicationEventPublisher eventPublisher;

    /**
     * 登录认证切面
     */
    @Around("@annotation(requiresLogin)")
    public Object checkLogin(ProceedingJoinPoint joinPoint, RequiresLogin requiresLogin) throws Throwable {
        try {
            // 检查是否登录
            StpUtil.checkLogin();
            return joinPoint.proceed();
        } catch (NotLoginException e) {
            log.warn("用户未登录，访问被拒绝: {}", e.getMessage());
            throw new ServiceException(requiresLogin.message(), 401);
        }
    }

    /**
     * 权限认证切面
     */
    @Around("@annotation(requiresPermissions)")
    public Object checkPermissions(ProceedingJoinPoint joinPoint, RequiresPermissions requiresPermissions) throws Throwable {
        try {
            // 检查是否登录
            StpUtil.checkLogin();
            
            // 获取权限列表
            String[] permissions = requiresPermissions.value();
            if (permissions.length == 0) {
                return joinPoint.proceed();
            }

            // 获取用户权限
            Set<String> userPermissions = SecurityUtils.getLoginUser().getPermissions();
            
            // 检查权限
            boolean hasPermission = false;
            if (requiresPermissions.logical() == RequiresPermissions.Logical.AND) {
                // AND模式：必须拥有所有权限
                hasPermission = true;
                for (String permission : permissions) {
                    if (!SecurityUtils.hasPermi(userPermissions, permission)) {
                        hasPermission = false;
                        break;
                    }
                }
            } else {
                // OR模式：拥有任意一个权限即可
                for (String permission : permissions) {
                    if (SecurityUtils.hasPermi(userPermissions, permission)) {
                        hasPermission = true;
                        break;
                    }
                }
            }

            if (!hasPermission) {
                log.warn("用户权限不足，访问被拒绝: 需要权限 {}", String.join(",", permissions));
                throw new ServiceException(requiresPermissions.message(), 403);
            }

            return joinPoint.proceed();
        } catch (NotLoginException e) {
            log.warn("用户未登录，访问被拒绝: {}", e.getMessage());
            throw new ServiceException("请先登录", 401);
        } catch (NotPermissionException e) {
            log.warn("用户权限不足，访问被拒绝: {}", e.getMessage());
            throw new ServiceException(requiresPermissions.message(), 403);
        }
    }

    /**
     * 角色认证切面
     */
    @Around("@annotation(requiresRoles)")
    public Object checkRoles(ProceedingJoinPoint joinPoint, RequiresRoles requiresRoles) throws Throwable {
        try {
            // 检查是否登录
            StpUtil.checkLogin();
            
            // 获取角色列表
            String[] roles = requiresRoles.value();
            if (roles.length == 0) {
                return joinPoint.proceed();
            }

            // 获取用户角色
            Set<String> userRoles = SecurityUtils.getLoginUser().getRoles();
            
            // 检查角色
            boolean hasRole = false;
            if (requiresRoles.logical() == RequiresRoles.Logical.AND) {
                // AND模式：必须拥有所有角色
                hasRole = true;
                for (String role : roles) {
                    if (!SecurityUtils.hasRole(userRoles, role)) {
                        hasRole = false;
                        break;
                    }
                }
            } else {
                // OR模式：拥有任意一个角色即可
                for (String role : roles) {
                    if (SecurityUtils.hasRole(userRoles, role)) {
                        hasRole = true;
                        break;
                    }
                }
            }

            if (!hasRole) {
                log.warn("用户角色不足，访问被拒绝: 需要角色 {}", String.join(",", roles));
                throw new ServiceException(requiresRoles.message(), 403);
            }

            return joinPoint.proceed();
        } catch (NotLoginException e) {
            log.warn("用户未登录，访问被拒绝: {}", e.getMessage());
            throw new ServiceException("请先登录", 401);
        } catch (NotRoleException e) {
            log.warn("用户角色不足，访问被拒绝: {}", e.getMessage());
            throw new ServiceException(requiresRoles.message(), 403);
        }
    }

    /**
     * 获取方法上的注解
     */
    private <T> T getMethodAnnotation(ProceedingJoinPoint joinPoint, Class<T> annotationClass) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        return method.getAnnotation(annotationClass);
    }
}
