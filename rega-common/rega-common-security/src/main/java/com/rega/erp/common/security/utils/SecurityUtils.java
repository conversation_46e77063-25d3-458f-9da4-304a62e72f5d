package com.rega.erp.common.security.utils;

import cn.dev33.satoken.stp.StpUtil;
import com.rega.erp.common.core.context.TenantContextHolder;
import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.util.StringUtils;
import com.rega.erp.common.security.domain.LoginUser;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 安全服务工具类
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SecurityUtils {

    /**
     * 用户ID
     */
    public static Long getUserId() {
        try {
            return getLoginUser().getUserId();
        } catch (Exception e) {
            throw new BusinessException("security.user.id.get.error");
        }
    }

    /**
     * 获取部门ID
     */
    public static Long getDeptId() {
        try {
            return getLoginUser().getDeptId();
        } catch (Exception e) {
            throw new BusinessException("security.user.dept.get.error");
        }
    }

    /**
     * 获取用户账户
     */
    public static String getUsername() {
        try {
            return getLoginUser().getUsername();
        } catch (Exception e) {
            throw new BusinessException("security.user.username.get.error");
        }
    }

    /**
     * 获取用户昵称
     */
    public static String getNickname() {
        try {
            return getLoginUser().getNickname();
        } catch (Exception e) {
            throw new BusinessException("security.user.nickname.get.error");
        }
    }

    /**
     * 获取租户ID
     */
    public static String getTenantId() {
        try {
            // 优先从登录用户获取，其次从租户上下文获取
            LoginUser loginUser = getLoginUserOrNull();
            if (loginUser != null && StringUtils.isNotBlank(loginUser.getTenantId())) {
                return loginUser.getTenantId();
            }
            return TenantContextHolder.getTenantId();
        } catch (Exception e) {
            throw new BusinessException("security.tenant.get.error");
        }
    }

    /**
     * 获取用户
     */
    public static LoginUser getLoginUser() {
        try {
            return (LoginUser) StpUtil.getSession().get("loginUser");
        } catch (Exception e) {
            throw new BusinessException("security.user.info.get.error");
        }
    }

    /**
     * 获取用户（允许为空）
     */
    public static LoginUser getLoginUserOrNull() {
        try {
            if (StpUtil.isLogin()) {
                return (LoginUser) StpUtil.getSession().get("loginUser");
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 设置用户数据(多级缓存)
     */
    public static void setLoginUser(LoginUser loginUser) {
        StpUtil.getSession().set("loginUser", loginUser);
    }

    /**
     * 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId) {
        return LoginUser.isAdmin(userId);
    }

    /**
     * 是否为管理员
     *
     * @return 结果
     */
    public static boolean isAdmin() {
        return LoginUser.isAdmin(getUserId());
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password) {
        return PasswordUtils.encryptPassword(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword     真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        return PasswordUtils.matchesPassword(rawPassword, encodedPassword);
    }

    /**
     * 是否登录
     */
    public static boolean isLogin() {
        return StpUtil.isLogin();
    }

    /**
     * 验证用户是否具备某权限
     *
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    public static boolean hasPermi(String permission) {
        return hasPermi(getLoginUser().getPermissions(), permission);
    }

    /**
     * 验证用户是否不具备某权限，与 hasPermi逻辑相反
     *
     * @param permission 权限字符串
     * @return 用户是否不具备某权限
     */
    public static boolean lacksPermi(String permission) {
        return hasPermi(permission) != true;
    }

    /**
     * 验证用户是否具有以下任意一个权限
     *
     * @param permissions 以 PERMISSION_DELIMETER 为分隔符的权限列表
     * @return 用户是否具有以下任意一个权限
     */
    public static boolean hasAnyPermi(String permissions) {
        return hasAnyPermi(getLoginUser().getPermissions(), permissions);
    }

    /**
     * 判断用户是否拥有某个角色
     *
     * @param role 角色字符串
     * @return 用户是否具备某角色
     */
    public static boolean hasRole(String role) {
        return hasRole(getLoginUser().getRoles(), role);
    }

    /**
     * 验证用户是否不具备某角色，与 isRole逻辑相反。
     *
     * @param role 角色名称
     * @return 用户是否不具备某角色
     */
    public static boolean lacksRole(String role) {
        return hasRole(role) != true;
    }

    /**
     * 验证用户是否具有以下任意一个角色
     *
     * @param roles 以 ROLE_NAMES_DELIMETER 为分隔符的角色列表
     * @return 用户是否具有以下任意一个角色
     */
    public static boolean hasAnyRole(String roles) {
        return hasAnyRole(getLoginUser().getRoles(), roles);
    }

    /**
     * 权限分隔符
     */
    public static final String PERMISSION_DELIMETER = ",";

    /**
     * 角色分隔符
     */
    public static final String ROLE_NAMES_DELIMETER = ",";

    /**
     * 验证用户是否具备某权限
     *
     * @param permissions 权限列表
     * @param permission  权限字符串
     * @return 用户是否具备某权限
     */
    public static boolean hasPermi(java.util.Set<String> permissions, String permission) {
        return permissions.contains("*:*:*") || permissions.contains(permission);
    }

    /**
     * 验证用户是否具有以下任意一个权限
     *
     * @param permissions 权限列表
     * @param perms       以 PERMISSION_DELIMETER 为分隔符的权限列表
     * @return 用户是否具有以下任意一个权限
     */
    public static boolean hasAnyPermi(java.util.Set<String> permissions, String perms) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(perms)) {
            return false;
        }
        for (String permission : perms.split(PERMISSION_DELIMETER)) {
            if (permission != null && hasPermi(permissions, permission.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断用户是否拥有某个角色
     *
     * @param roles 角色列表
     * @param role  角色
     * @return 用户是否具备某角色
     */
    public static boolean hasRole(java.util.Set<String> roles, String role) {
        return roles.contains("admin") || roles.contains(role);
    }

    /**
     * 验证用户是否具有以下任意一个角色
     *
     * @param roles     角色列表
     * @param roleNames 以 ROLE_NAMES_DELIMETER 为分隔符的角色列表
     * @return 用户是否具有以下任意一个角色
     */
    public static boolean hasAnyRole(java.util.Set<String> roles, String roleNames) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(roleNames)) {
            return false;
        }
        for (String role : roleNames.split(ROLE_NAMES_DELIMETER)) {
            if (hasRole(roles, role.trim())) {
                return true;
            }
        }
        return false;
    }
}
