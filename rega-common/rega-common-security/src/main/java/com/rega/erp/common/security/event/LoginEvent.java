package com.rega.erp.common.security.event;

import lombok.Getter;

/**
 * 登录事件
 *
 * <AUTHOR>
 */
@Getter
public class LoginEvent extends SecurityEvent {

    /**
     * 登录类型
     */
    private final String loginType;

    /**
     * 登录状态
     */
    private final boolean success;

    /**
     * 失败原因
     */
    private final String failureReason;

    /**
     * 登录来源
     */
    private final String loginSource;

    public LoginEvent(Object source, String tenantId, Long userId, String username, 
                     String clientIp, String userAgent, String loginType, boolean success, 
                     String failureReason, String loginSource) {
        super(source, "LOGIN", tenantId, userId, username, clientIp, userAgent, 
              success ? "用户登录成功" : "用户登录失败: " + failureReason);
        this.loginType = loginType;
        this.success = success;
        this.failureReason = failureReason;
        this.loginSource = loginSource;
    }

    /**
     * 创建登录成功事件
     */
    public static LoginEvent success(Object source, String tenantId, Long userId, String username,
                                   String clientIp, String userAgent, String loginType, String loginSource) {
        return new LoginEvent(source, tenantId, userId, username, clientIp, userAgent, 
                            loginType, true, null, loginSource);
    }

    /**
     * 创建登录失败事件
     */
    public static LoginEvent failure(Object source, String tenantId, String username,
                                   String clientIp, String userAgent, String loginType, 
                                   String failureReason, String loginSource) {
        return new LoginEvent(source, tenantId, null, username, clientIp, userAgent, 
                            loginType, false, failureReason, loginSource);
    }
}
