package com.rega.erp.common.security.service;

import com.rega.erp.common.security.domain.LoginUser;

/**
 * 登录服务接口
 *
 * <AUTHOR>
 */
public interface LoginService {

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    String login(String username, String password, String code, String uuid);

    /**
     * 登录验证（无验证码）
     *
     * @param username 用户名
     * @param password 密码
     * @return 结果
     */
    String login(String username, String password);

    /**
     * 手机号登录
     *
     * @param phonenumber 手机号
     * @param code        验证码
     * @return 结果
     */
    String smsLogin(String phonenumber, String code);

    /**
     * 第三方登录
     *
     * @param source 登录来源
     * @param code   授权码
     * @param state  状态
     * @return 结果
     */
    String socialLogin(String source, String code, String state);

    /**
     * 退出登录
     */
    void logout();

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌
     */
    String refreshToken(String refreshToken);

    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param status   状态
     * @param message  消息
     * @param args     列表
     */
    void recordLoginInfo(String username, String status, String message, Object... args);

    /**
     * 校验验证码
     *
     * @param code 验证码
     * @param uuid 唯一标识
     */
    void validateCaptcha(String code, String uuid);

    /**
     * 校验短信验证码
     *
     * @param phonenumber 手机号
     * @param code        验证码
     */
    void validateSmsCode(String phonenumber, String code);

    /**
     * 检查登录失败次数
     *
     * @param username 用户名
     */
    void checkLoginFailCount(String username);

    /**
     * 清除登录失败次数
     *
     * @param username 用户名
     */
    void clearLoginFailCount(String username);

    /**
     * 增加登录失败次数
     *
     * @param username 用户名
     */
    void increaseLoginFailCount(String username);

    /**
     * 获取用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    LoginUser getUserInfo(String username);

    /**
     * 构建登录用户
     *
     * @param user 用户信息
     * @return 登录用户
     */
    LoginUser buildLoginUser(Object user);
}
