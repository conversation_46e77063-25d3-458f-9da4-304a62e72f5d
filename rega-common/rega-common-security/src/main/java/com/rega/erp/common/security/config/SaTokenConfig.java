package com.rega.erp.common.security.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.jwt.StpLogicJwtForSimple;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token 配置
 *
 * <AUTHOR>
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册Sa-Token拦截器，打开注解式鉴权功能
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验。
        registry.addInterceptor(new SaInterceptor(handle -> StpUtil.checkLogin()))
                .addPathPatterns("/**")
                .excludePathPatterns(
                        // 排除登录接口
                        "/login", "/logout", "/register",
                        // 排除静态资源
                        "/static/**", "/css/**", "/js/**", "/images/**", "/fonts/**",
                        // 排除Swagger文档
                        "/swagger-ui/**", "/v3/api-docs/**", "/swagger-resources/**",
                        // 排除健康检查
                        "/actuator/**", "/health", "/info",
                        // 排除错误页面
                        "/error", "/favicon.ico",
                        // 排除公开API
                        "/api/public/**",
                        // 排除验证码
                        "/captcha/**"
                );
    }

    /**
     * Sa-Token 整合 jwt (简单模式)
     */
    @Bean
    public StpLogic getStpLogicJwt() {
        return new StpLogicJwtForSimple();
    }
}
