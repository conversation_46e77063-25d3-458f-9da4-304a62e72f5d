package com.rega.erp.common.security.utils;

import cn.hutool.core.util.StrUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.security.SecureRandom;
import java.util.regex.Pattern;

/**
 * 密码工具类
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PasswordUtils {

    private static final BCryptPasswordEncoder PASSWORD_ENCODER = new BCryptPasswordEncoder();

    /**
     * 密码复杂度正则表达式
     * 至少包含一个大写字母、一个小写字母、一个数字和一个特殊字符，长度至少8位
     */
    private static final String COMPLEX_PASSWORD_PATTERN = 
        "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$";

    /**
     * 中等复杂度密码正则表达式
     * 至少包含字母和数字，长度至少6位
     */
    private static final String MEDIUM_PASSWORD_PATTERN = 
        "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d@$!%*?&]{6,}$";

    /**
     * 简单密码正则表达式
     * 长度至少6位
     */
    private static final String SIMPLE_PASSWORD_PATTERN = "^.{6,}$";

    private static final Pattern COMPLEX_PATTERN = Pattern.compile(COMPLEX_PASSWORD_PATTERN);
    private static final Pattern MEDIUM_PATTERN = Pattern.compile(MEDIUM_PASSWORD_PATTERN);
    private static final Pattern SIMPLE_PATTERN = Pattern.compile(SIMPLE_PASSWORD_PATTERN);

    /**
     * 加密密码
     *
     * @param password 原始密码
     * @return 加密后的密码
     */
    public static String encryptPassword(String password) {
        if (StrUtil.isBlank(password)) {
            return null;
        }
        return PASSWORD_ENCODER.encode(password);
    }

    /**
     * 验证密码
     *
     * @param rawPassword     原始密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        if (StrUtil.isBlank(rawPassword) || StrUtil.isBlank(encodedPassword)) {
            return false;
        }
        return PASSWORD_ENCODER.matches(rawPassword, encodedPassword);
    }

    /**
     * 生成随机密码
     *
     * @param length 密码长度
     * @return 随机密码
     */
    public static String generateRandomPassword(int length) {
        if (length < 6) {
            length = 6;
        }
        
        String upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String lowerCase = "abcdefghijklmnopqrstuvwxyz";
        String numbers = "0123456789";
        String specialChars = "@$!%*?&";
        String allChars = upperCase + lowerCase + numbers + specialChars;
        
        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder();
        
        // 确保至少包含一个大写字母、小写字母、数字和特殊字符
        password.append(upperCase.charAt(random.nextInt(upperCase.length())));
        password.append(lowerCase.charAt(random.nextInt(lowerCase.length())));
        password.append(numbers.charAt(random.nextInt(numbers.length())));
        password.append(specialChars.charAt(random.nextInt(specialChars.length())));
        
        // 填充剩余长度
        for (int i = 4; i < length; i++) {
            password.append(allChars.charAt(random.nextInt(allChars.length())));
        }
        
        // 打乱字符顺序
        return shuffleString(password.toString());
    }

    /**
     * 生成简单随机密码（只包含字母和数字）
     *
     * @param length 密码长度
     * @return 随机密码
     */
    public static String generateSimplePassword(int length) {
        if (length < 6) {
            length = 6;
        }
        
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            password.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return password.toString();
    }

    /**
     * 验证密码强度
     *
     * @param password 密码
     * @return 密码强度等级 (1-弱, 2-中, 3-强)
     */
    public static int getPasswordStrength(String password) {
        if (StrUtil.isBlank(password)) {
            return 0;
        }
        
        if (COMPLEX_PATTERN.matcher(password).matches()) {
            return 3; // 强
        } else if (MEDIUM_PATTERN.matcher(password).matches()) {
            return 2; // 中
        } else if (SIMPLE_PATTERN.matcher(password).matches()) {
            return 1; // 弱
        } else {
            return 0; // 不符合要求
        }
    }

    /**
     * 验证密码是否符合复杂度要求
     *
     * @param password 密码
     * @return 是否符合要求
     */
    public static boolean isComplexPassword(String password) {
        return getPasswordStrength(password) >= 3;
    }

    /**
     * 验证密码是否符合中等复杂度要求
     *
     * @param password 密码
     * @return 是否符合要求
     */
    public static boolean isMediumPassword(String password) {
        return getPasswordStrength(password) >= 2;
    }

    /**
     * 验证密码是否符合基本要求
     *
     * @param password 密码
     * @return 是否符合要求
     */
    public static boolean isValidPassword(String password) {
        return getPasswordStrength(password) >= 1;
    }

    /**
     * 获取密码强度描述
     *
     * @param password 密码
     * @return 强度描述
     */
    public static String getPasswordStrengthDesc(String password) {
        int strength = getPasswordStrength(password);
        switch (strength) {
            case 0:
                return "密码不符合要求";
            case 1:
                return "弱";
            case 2:
                return "中";
            case 3:
                return "强";
            default:
                return "未知";
        }
    }

    /**
     * 检查密码是否包含用户名
     *
     * @param password 密码
     * @param username 用户名
     * @return 是否包含
     */
    public static boolean containsUsername(String password, String username) {
        if (StrUtil.isBlank(password) || StrUtil.isBlank(username)) {
            return false;
        }
        return password.toLowerCase().contains(username.toLowerCase());
    }

    /**
     * 检查密码是否为常见弱密码
     *
     * @param password 密码
     * @return 是否为弱密码
     */
    public static boolean isWeakPassword(String password) {
        if (StrUtil.isBlank(password)) {
            return true;
        }
        
        String[] weakPasswords = {
            "123456", "password", "123456789", "12345678", "12345",
            "1234567", "1234567890", "qwerty", "abc123", "111111",
            "123123", "admin", "letmein", "welcome", "monkey",
            "dragon", "pass", "master", "hello", "freedom"
        };
        
        String lowerPassword = password.toLowerCase();
        for (String weak : weakPasswords) {
            if (lowerPassword.equals(weak)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 打乱字符串
     *
     * @param str 原字符串
     * @return 打乱后的字符串
     */
    private static String shuffleString(String str) {
        char[] chars = str.toCharArray();
        SecureRandom random = new SecureRandom();
        
        for (int i = chars.length - 1; i > 0; i--) {
            int j = random.nextInt(i + 1);
            char temp = chars[i];
            chars[i] = chars[j];
            chars[j] = temp;
        }
        
        return new String(chars);
    }

    /**
     * 生成盐值
     *
     * @return 盐值
     */
    public static String generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[16];
        random.nextBytes(salt);
        return java.util.Base64.getEncoder().encodeToString(salt);
    }
}
