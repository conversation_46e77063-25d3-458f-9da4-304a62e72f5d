package com.rega.erp.common.security.event;

import lombok.Getter;

/**
 * 权限验证事件
 *
 * <AUTHOR>
 */
@Getter
public class PermissionEvent extends SecurityEvent {

    /**
     * 权限类型 (PERMISSION, ROLE, DATA_SCOPE)
     */
    private final String permissionType;

    /**
     * 权限标识
     */
    private final String permission;

    /**
     * 验证结果
     */
    private final boolean granted;

    /**
     * 请求资源
     */
    private final String resource;

    /**
     * 请求方法
     */
    private final String method;

    public PermissionEvent(Object source, String tenantId, Long userId, String username,
                          String clientIp, String userAgent, String permissionType, 
                          String permission, boolean granted, String resource, String method) {
        super(source, "PERMISSION", tenantId, userId, username, clientIp, userAgent,
              String.format("权限验证: %s %s %s", permissionType, permission, granted ? "通过" : "拒绝"));
        this.permissionType = permissionType;
        this.permission = permission;
        this.granted = granted;
        this.resource = resource;
        this.method = method;
    }

    /**
     * 创建权限验证事件
     */
    public static PermissionEvent create(Object source, String tenantId, Long userId, String username,
                                       String clientIp, String userAgent, String permissionType,
                                       String permission, boolean granted, String resource, String method) {
        return new PermissionEvent(source, tenantId, userId, username, clientIp, userAgent,
                                 permissionType, permission, granted, resource, method);
    }
}
