package com.rega.erp.common.security.config;

import com.rega.erp.common.security.aspect.DataScopeAspect;
import com.rega.erp.common.security.aspect.PermissionAspect;
import com.rega.erp.common.security.interceptor.TenantSecurityInterceptor;
import com.rega.erp.common.security.listener.SecurityEventListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 安全模块自动配置
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(SecurityConfig.class)
@ConditionalOnProperty(prefix = "rega.security", name = "enabled", havingValue = "true", matchIfMissing = true)
@Import({SaTokenConfig.class})
public class SecurityAutoConfiguration implements WebMvcConfigurer {

    public SecurityAutoConfiguration() {
        log.info("RegaWebERP Security Module Auto Configuration Initialized");
    }

    /**
     * 权限切面
     */
    @Bean
    @ConditionalOnMissingBean
    public PermissionAspect permissionAspect(ApplicationEventPublisher eventPublisher) {
        log.info("Register PermissionAspect");
        return new PermissionAspect(eventPublisher);
    }

    /**
     * 数据权限切面
     */
    @Bean
    @ConditionalOnMissingBean
    public DataScopeAspect dataScopeAspect() {
        log.info("Register DataScopeAspect");
        return new DataScopeAspect();
    }

    /**
     * 多租户安全拦截器
     */
    @Bean
    @ConditionalOnMissingBean
    public TenantSecurityInterceptor tenantSecurityInterceptor() {
        log.info("Register TenantSecurityInterceptor");
        return new TenantSecurityInterceptor();
    }

    /**
     * 安全事件监听器
     */
    @Bean
    @ConditionalOnMissingBean
    public SecurityEventListener securityEventListener() {
        log.info("Register SecurityEventListener");
        return new SecurityEventListener();
    }

    /**
     * 安全任务执行器
     */
    @Bean("securityTaskExecutor")
    @ConditionalOnMissingBean(name = "securityTaskExecutor")
    public Executor securityTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("security-task-");
        
        // 拒绝策略：由调用线程处理该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        log.info("Register SecurityTaskExecutor");
        return executor;
    }

    /**
     * 注册拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tenantSecurityInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns(
                        // 排除静态资源
                        "/static/**", "/css/**", "/js/**", "/images/**", "/fonts/**",
                        // 排除Swagger文档
                        "/swagger-ui/**", "/v3/api-docs/**", "/swagger-resources/**",
                        // 排除健康检查
                        "/actuator/**", "/health", "/info",
                        // 排除错误页面
                        "/error", "/favicon.ico"
                );
    }
}
