package com.rega.erp.common.security.aspect;

import com.rega.erp.common.core.domain.BaseEntity;
import com.rega.erp.common.security.annotation.DataScope;
import com.rega.erp.common.security.domain.LoginUser;
import com.rega.erp.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 数据过滤处理
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@Order(2)
public class DataScopeAspect {

    /**
     * 全部数据权限
     */
    public static final String DATA_SCOPE_ALL = "1";

    /**
     * 自定数据权限
     */
    public static final String DATA_SCOPE_CUSTOM = "2";

    /**
     * 部门数据权限
     */
    public static final String DATA_SCOPE_DEPT = "3";

    /**
     * 部门及以下数据权限
     */
    public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";

    /**
     * 仅本人数据权限
     */
    public static final String DATA_SCOPE_SELF = "5";

    /**
     * 数据权限过滤关键字
     */
    public static final String DATA_SCOPE = "dataScope";

    @Before("@annotation(controllerDataScope)")
    public void doBefore(JoinPoint point, DataScope controllerDataScope) throws Throwable {
        clearDataScope(point);
        handleDataScope(point, controllerDataScope);
    }

    protected void handleDataScope(final JoinPoint joinPoint, DataScope dataScope) {
        // 如果是超级管理员，则不过滤数据
        LoginUser loginUser = SecurityUtils.getLoginUserOrNull();
        if (loginUser == null) {
            return;
        }

        if (SecurityUtils.isAdmin(loginUser.getUserId())) {
            return;
        }

        String permission = dataScope.permission();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(permission) && !SecurityUtils.hasPermi(permission)) {
            return;
        }

        StringBuilder sqlString = new StringBuilder();
        String dataScope1 = loginUser.getDataScope();

        if (DATA_SCOPE_ALL.equals(dataScope1)) {
            // 全部数据权限
            sqlString.append("");
        } else if (DATA_SCOPE_CUSTOM.equals(dataScope1)) {
            // 自定数据权限
            sqlString.append(String.format(
                    " OR %s.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id IN ( SELECT role_id FROM sys_user_role WHERE user_id = %d ) )",
                    dataScope.deptAlias(), loginUser.getUserId()));
        } else if (DATA_SCOPE_DEPT.equals(dataScope1)) {
            // 部门数据权限
            sqlString.append(String.format(" OR %s.dept_id = %d ", dataScope.deptAlias(), loginUser.getDeptId()));
        } else if (DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope1)) {
            // 部门及子部门数据权限 (使用 Anyline 自动方言转换)
            sqlString.append(String.format(
                    " OR %s.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = %d or find_in_set( %d , ancestors ) )",
                    dataScope.deptAlias(), loginUser.getDeptId(), loginUser.getDeptId()));
        } else if (DATA_SCOPE_SELF.equals(dataScope1)) {
            // 仅本人数据权限
            if (org.apache.commons.lang3.StringUtils.isNotBlank(dataScope.userAlias())) {
                sqlString.append(String.format(" OR %s.user_id = %d ", dataScope.userAlias(), loginUser.getUserId()));
            } else {
                // 数据权限为仅本人且没有userAlias别名，不查询任何数据
                sqlString.append(String.format(" OR %s.dept_id = 0 ", dataScope.deptAlias()));
            }
        }

        // 多租户支持
        if (org.apache.commons.lang3.StringUtils.isNotBlank(loginUser.getTenantId())) {
            sqlString.append(String.format(" AND %s.tenant_id = '%s' ", dataScope.deptAlias(), loginUser.getTenantId()));
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(sqlString.toString())) {
            Object params = joinPoint.getArgs()[0];
            if (params instanceof BaseEntity) {
                BaseEntity baseEntity = (BaseEntity) params;
                baseEntity.getParams().put(DATA_SCOPE, " AND (" + sqlString.substring(4) + ")");
            }
        }
    }

    /**
     * 拼接权限sql前先清空params.dataScope参数防止注入
     */
    private void clearDataScope(final JoinPoint joinPoint) {
        Object params = joinPoint.getArgs()[0];
        if (params instanceof BaseEntity) {
            BaseEntity baseEntity = (BaseEntity) params;
            baseEntity.getParams().put(DATA_SCOPE, "");
        }
    }
}
