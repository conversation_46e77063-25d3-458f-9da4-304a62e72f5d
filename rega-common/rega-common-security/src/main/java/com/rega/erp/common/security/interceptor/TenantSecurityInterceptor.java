package com.rega.erp.common.security.interceptor;

import cn.hutool.core.util.StrUtil;
import com.rega.erp.common.core.context.TenantContextHolder;
import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.util.StringUtils;
import com.rega.erp.common.security.domain.LoginUser;
import com.rega.erp.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 多租户安全拦截器
 * 确保租户上下文的正确设置和验证
 *
 * <AUTHOR>
 */
@Slf4j
public class TenantSecurityInterceptor implements HandlerInterceptor {

    /**
     * 租户ID请求头名称
     */
    private static final String TENANT_HEADER = "X-Tenant-Id";

    /**
     * 租户ID请求参数名称
     */
    private static final String TENANT_PARAM = "tenantId";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            // 从请求中获取租户ID
            String tenantId = getTenantIdFromRequest(request);
            
            // 如果用户已登录，验证租户ID
            if (SecurityUtils.isLogin()) {
                LoginUser loginUser = SecurityUtils.getLoginUserOrNull();
                if (loginUser != null) {
                    String userTenantId = loginUser.getTenantId();
                    
                    // 如果请求中有租户ID，验证是否与用户租户ID一致
                    if (StringUtils.isNotBlank(tenantId) && StringUtils.isNotBlank(userTenantId)) {
                        if (!tenantId.equals(userTenantId)) {
                            log.warn("租户ID不匹配: 请求租户ID={}, 用户租户ID={}", tenantId, userTenantId);
                            throw new BusinessException("security.tenant.invalid");
                        }
                    }
                    
                    // 使用用户的租户ID
                    tenantId = userTenantId;
                }
            }
            
            // 设置租户上下文
            if (StringUtils.isNotBlank(tenantId)) {
                TenantContextHolder.setTenantId(tenantId);
                log.debug("设置租户上下文: {}", tenantId);
            }
            
            return true;
        } catch (Exception e) {
            log.error("租户安全拦截器处理失败", e);
            throw e;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清理租户上下文
        TenantContextHolder.clear();
        log.debug("清理租户上下文");
    }

    /**
     * 从请求中获取租户ID
     * 优先级：Header > Parameter
     *
     * @param request HTTP请求
     * @return 租户ID
     */
    private String getTenantIdFromRequest(HttpServletRequest request) {
        // 1. 从请求头获取
        String tenantId = request.getHeader(TENANT_HEADER);
        if (StringUtils.isNotBlank(tenantId)) {
            return tenantId.trim();
        }
        
        // 2. 从请求参数获取
        tenantId = request.getParameter(TENANT_PARAM);
        if (StringUtils.isNotBlank(tenantId)) {
            return tenantId.trim();
        }
        
        return null;
    }
}
