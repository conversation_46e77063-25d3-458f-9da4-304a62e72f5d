package com.rega.erp.common.security.service;

import java.util.Set;

/**
 * 权限服务接口
 *
 * <AUTHOR>
 */
public interface PermissionService {

    /**
     * 获取角色数据权限
     *
     * @param userId 用户ID
     * @return 角色权限信息
     */
    Set<String> getRolePermission(Long userId);

    /**
     * 获取菜单数据权限
     *
     * @param userId 用户ID
     * @return 菜单权限信息
     */
    Set<String> getMenuPermission(Long userId);

    /**
     * 获取用户角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    Set<String> getUserRoles(Long userId);

    /**
     * 获取用户权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> getUserPermissions(Long userId);

    /**
     * 检查用户是否有指定权限
     *
     * @param userId     用户ID
     * @param permission 权限标识
     * @return 是否有权限
     */
    boolean hasPermission(Long userId, String permission);

    /**
     * 检查用户是否有指定角色
     *
     * @param userId 用户ID
     * @param role   角色标识
     * @return 是否有角色
     */
    boolean hasRole(Long userId, String role);

    /**
     * 检查用户是否有任意权限
     *
     * @param userId      用户ID
     * @param permissions 权限列表
     * @return 是否有权限
     */
    boolean hasAnyPermission(Long userId, String... permissions);

    /**
     * 检查用户是否有任意角色
     *
     * @param userId 用户ID
     * @param roles  角色列表
     * @return 是否有角色
     */
    boolean hasAnyRole(Long userId, String... roles);

    /**
     * 检查用户是否有所有权限
     *
     * @param userId      用户ID
     * @param permissions 权限列表
     * @return 是否有权限
     */
    boolean hasAllPermissions(Long userId, String... permissions);

    /**
     * 检查用户是否有所有角色
     *
     * @param userId 用户ID
     * @param roles  角色列表
     * @return 是否有角色
     */
    boolean hasAllRoles(Long userId, String... roles);

    /**
     * 获取数据权限范围
     *
     * @param userId 用户ID
     * @return 数据权限范围
     */
    String getDataScope(Long userId);

    /**
     * 获取数据权限部门列表
     *
     * @param userId 用户ID
     * @return 部门ID列表
     */
    Set<Long> getDataScopeDeptIds(Long userId);

    /**
     * 刷新用户权限缓存
     *
     * @param userId 用户ID
     */
    void refreshUserPermissions(Long userId);

    /**
     * 清除用户权限缓存
     *
     * @param userId 用户ID
     */
    void clearUserPermissions(Long userId);

    /**
     * 清除所有权限缓存
     */
    void clearAllPermissions();
}
