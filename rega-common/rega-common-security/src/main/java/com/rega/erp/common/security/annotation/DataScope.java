package com.rega.erp.common.security.annotation;

import java.lang.annotation.*;

/**
 * 数据权限过滤注解
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataScope {

    /**
     * 部门表的别名
     */
    String deptAlias() default "";

    /**
     * 用户表的别名
     */
    String userAlias() default "";

    /**
     * 权限字符串
     */
    String permission() default "";

    /**
     * 是否启用数据权限
     */
    boolean enabled() default true;
}
