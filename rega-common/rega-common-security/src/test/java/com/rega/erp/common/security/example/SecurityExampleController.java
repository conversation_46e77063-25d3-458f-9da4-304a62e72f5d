package com.rega.erp.common.security.example;

import com.rega.erp.common.security.annotation.DataScope;
import com.rega.erp.common.security.annotation.RequiresLogin;
import com.rega.erp.common.security.annotation.RequiresPermissions;
import com.rega.erp.common.security.annotation.RequiresRoles;
import com.rega.erp.common.security.domain.LoginUser;
import com.rega.erp.common.security.utils.SecurityUtils;
import lombok.Data;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 安全功能示例控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/example/security")
public class SecurityExampleController {

    @Data
    public static class User {
        private Long id;
        private String username;
        private String nickname;
        private Long deptId;
        private String tenantId;
    }

    /**
     * 需要登录的接口
     */
    @GetMapping("/profile")
    @RequiresLogin
    public LoginUser getProfile() {
        return SecurityUtils.getLoginUser();
    }

    /**
     * 需要特定权限的接口
     */
    @GetMapping("/users")
    @RequiresPermissions("system:user:list")
    public List<User> getUsers() {
        // 模拟返回用户列表
        return List.of(
            createUser(1L, "admin", "管理员", 100L, "123456"),
            createUser(2L, "user", "普通用户", 101L, "123456")
        );
    }

    /**
     * 需要多个权限的接口（AND模式）
     */
    @PostMapping("/users")
    @RequiresPermissions(value = {"system:user:add", "system:user:edit"}, 
                        logical = RequiresPermissions.Logical.AND)
    public User createUser(@RequestBody User user) {
        user.setId(System.currentTimeMillis());
        user.setTenantId(SecurityUtils.getTenantId());
        return user;
    }

    /**
     * 需要任意权限的接口（OR模式）
     */
    @PutMapping("/users/{id}")
    @RequiresPermissions(value = {"system:user:edit", "system:user:admin"}, 
                        logical = RequiresPermissions.Logical.OR)
    public User updateUser(@PathVariable Long id, @RequestBody User user) {
        user.setId(id);
        return user;
    }

    /**
     * 需要特定角色的接口
     */
    @DeleteMapping("/users/{id}")
    @RequiresRoles("admin")
    public String deleteUser(@PathVariable Long id) {
        return "删除成功";
    }

    /**
     * 需要多个角色的接口（AND模式）
     */
    @PostMapping("/users/{id}/reset-password")
    @RequiresRoles(value = {"admin", "hr"}, logical = RequiresRoles.Logical.AND)
    public String resetPassword(@PathVariable Long id) {
        return "密码重置成功";
    }

    /**
     * 需要任意角色的接口（OR模式）
     */
    @GetMapping("/users/{id}/details")
    @RequiresRoles(value = {"admin", "hr", "manager"}, logical = RequiresRoles.Logical.OR)
    public User getUserDetails(@PathVariable Long id) {
        return createUser(id, "user" + id, "用户" + id, 100L, SecurityUtils.getTenantId());
    }

    /**
     * 数据权限示例
     */
    @GetMapping("/users/data-scope")
    @RequiresPermissions("system:user:list")
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<User> getUsersWithDataScope() {
        // 在实际应用中，这里的查询会自动添加数据权限过滤条件
        return List.of(
            createUser(1L, "admin", "管理员", 100L, SecurityUtils.getTenantId()),
            createUser(2L, "user", "普通用户", 101L, SecurityUtils.getTenantId())
        );
    }

    /**
     * 自定义权限验证示例
     */
    @GetMapping("/custom-check")
    public String customPermissionCheck() {
        // 手动检查权限
        if (SecurityUtils.hasPermi("system:user:list")) {
            return "有权限访问用户列表";
        } else {
            return "无权限访问用户列表";
        }
    }

    /**
     * 角色检查示例
     */
    @GetMapping("/role-check")
    public String roleCheck() {
        if (SecurityUtils.hasRole("admin")) {
            return "您是管理员";
        } else if (SecurityUtils.hasRole("user")) {
            return "您是普通用户";
        } else {
            return "未知角色";
        }
    }

    /**
     * 多租户示例
     */
    @GetMapping("/tenant-info")
    @RequiresLogin
    public String getTenantInfo() {
        String tenantId = SecurityUtils.getTenantId();
        Long userId = SecurityUtils.getUserId();
        String username = SecurityUtils.getUsername();
        
        return String.format("租户ID: %s, 用户ID: %d, 用户名: %s", tenantId, userId, username);
    }

    /**
     * 管理员专用接口
     */
    @GetMapping("/admin-only")
    public String adminOnly() {
        if (SecurityUtils.isAdmin()) {
            return "欢迎管理员";
        } else {
            return "仅管理员可访问";
        }
    }

    /**
     * 公开接口（无需认证）
     */
    @GetMapping("/public")
    public String publicEndpoint() {
        return "这是公开接口，无需认证";
    }

    private User createUser(Long id, String username, String nickname, Long deptId, String tenantId) {
        User user = new User();
        user.setId(id);
        user.setUsername(username);
        user.setNickname(nickname);
        user.setDeptId(deptId);
        user.setTenantId(tenantId);
        return user;
    }
}
