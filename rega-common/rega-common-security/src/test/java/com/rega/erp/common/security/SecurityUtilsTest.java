package com.rega.erp.common.security;

import com.rega.erp.common.security.domain.LoginUser;
import com.rega.erp.common.security.utils.SecurityUtils;
import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 安全工具类测试
 *
 * <AUTHOR>
 */
class SecurityUtilsTest {

    @Test
    void testIsAdmin() {
        assertTrue(SecurityUtils.isAdmin(1L));
        assertFalse(SecurityUtils.isAdmin(2L));
        assertFalse(SecurityUtils.isAdmin(null));
    }

    @Test
    void testHasPermi() {
        Set<String> permissions = Set.of("system:user:list", "system:user:add", "*:*:*");
        
        assertTrue(SecurityUtils.hasPermi(permissions, "system:user:list"));
        assertTrue(SecurityUtils.hasPermi(permissions, "system:user:add"));
        assertTrue(SecurityUtils.hasPermi(permissions, "any:permission"));
        assertFalse(SecurityUtils.hasPermi(permissions, "system:role:list"));
    }

    @Test
    void testHasAnyPermi() {
        Set<String> permissions = Set.of("system:user:list", "system:user:add");
        
        assertTrue(SecurityUtils.hasAnyPermi(permissions, "system:user:list,system:role:list"));
        assertTrue(SecurityUtils.hasAnyPermi(permissions, "system:user:add"));
        assertFalse(SecurityUtils.hasAnyPermi(permissions, "system:role:list,system:menu:list"));
    }

    @Test
    void testHasRole() {
        Set<String> roles = Set.of("admin", "user");
        
        assertTrue(SecurityUtils.hasRole(roles, "admin"));
        assertTrue(SecurityUtils.hasRole(roles, "user"));
        assertTrue(SecurityUtils.hasRole(roles, "any_role")); // admin角色拥有所有权限
        
        Set<String> userRoles = Set.of("user");
        assertTrue(SecurityUtils.hasRole(userRoles, "user"));
        assertFalse(SecurityUtils.hasRole(userRoles, "admin"));
    }

    @Test
    void testHasAnyRole() {
        Set<String> roles = Set.of("user", "editor");
        
        assertTrue(SecurityUtils.hasAnyRole(roles, "user,admin"));
        assertTrue(SecurityUtils.hasAnyRole(roles, "editor"));
        assertFalse(SecurityUtils.hasAnyRole(roles, "admin,manager"));
    }

    @Test
    void testLoginUserMethods() {
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(1L);
        loginUser.setUsername("admin");
        loginUser.setNickname("管理员");
        loginUser.setTenantId("123456");
        loginUser.setDeptId(100L);
        loginUser.setUserType("sys_user");
        
        assertTrue(loginUser.isAdmin());
        assertEquals("sys_user:1", loginUser.getLoginId());
        
        LoginUser normalUser = new LoginUser();
        normalUser.setUserId(2L);
        assertFalse(normalUser.isAdmin());
    }

    @Test
    void testPasswordEncryption() {
        String password = "test123456";
        String encrypted = SecurityUtils.encryptPassword(password);
        
        assertNotNull(encrypted);
        assertNotEquals(password, encrypted);
        assertTrue(SecurityUtils.matchesPassword(password, encrypted));
        assertFalse(SecurityUtils.matchesPassword("wrongpassword", encrypted));
    }
}
