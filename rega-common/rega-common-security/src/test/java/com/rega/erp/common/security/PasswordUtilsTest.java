package com.rega.erp.common.security;

import com.rega.erp.common.security.utils.PasswordUtils;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 密码工具类测试
 *
 * <AUTHOR>
 */
class PasswordUtilsTest {

    @Test
    void testEncryptPassword() {
        String password = "test123456";
        String encrypted = PasswordUtils.encryptPassword(password);
        
        assertNotNull(encrypted);
        assertNotEquals(password, encrypted);
        assertTrue(encrypted.startsWith("$2a$"));
    }

    @Test
    void testMatchesPassword() {
        String password = "test123456";
        String encrypted = PasswordUtils.encryptPassword(password);
        
        assertTrue(PasswordUtils.matchesPassword(password, encrypted));
        assertFalse(PasswordUtils.matchesPassword("wrongpassword", encrypted));
    }

    @Test
    void testGenerateRandomPassword() {
        String password1 = PasswordUtils.generateRandomPassword(8);
        String password2 = PasswordUtils.generateRandomPassword(8);
        
        assertNotNull(password1);
        assertNotNull(password2);
        assertEquals(8, password1.length());
        assertEquals(8, password2.length());
        assertNotEquals(password1, password2);
        
        // 验证密码复杂度
        assertEquals(3, PasswordUtils.getPasswordStrength(password1));
    }

    @Test
    void testGenerateSimplePassword() {
        String password = PasswordUtils.generateSimplePassword(10);
        
        assertNotNull(password);
        assertEquals(10, password.length());
        assertTrue(password.matches("[A-Za-z0-9]+"));
    }

    @Test
    void testGetPasswordStrength() {
        // 强密码
        assertEquals(3, PasswordUtils.getPasswordStrength("Test123@"));
        
        // 中等密码
        assertEquals(2, PasswordUtils.getPasswordStrength("test123"));
        
        // 弱密码
        assertEquals(1, PasswordUtils.getPasswordStrength("123456"));
        
        // 不符合要求
        assertEquals(0, PasswordUtils.getPasswordStrength("123"));
    }

    @Test
    void testPasswordComplexity() {
        assertTrue(PasswordUtils.isComplexPassword("Test123@"));
        assertTrue(PasswordUtils.isMediumPassword("test123"));
        assertTrue(PasswordUtils.isValidPassword("123456"));
        
        assertFalse(PasswordUtils.isComplexPassword("test123"));
        assertFalse(PasswordUtils.isMediumPassword("123456"));
        assertFalse(PasswordUtils.isValidPassword("123"));
    }

    @Test
    void testGetPasswordStrengthDesc() {
        assertEquals("强", PasswordUtils.getPasswordStrengthDesc("Test123@"));
        assertEquals("中", PasswordUtils.getPasswordStrengthDesc("test123"));
        assertEquals("弱", PasswordUtils.getPasswordStrengthDesc("123456"));
        assertEquals("密码不符合要求", PasswordUtils.getPasswordStrengthDesc("123"));
    }

    @Test
    void testContainsUsername() {
        assertTrue(PasswordUtils.containsUsername("admin123", "admin"));
        assertTrue(PasswordUtils.containsUsername("123Admin", "admin"));
        assertFalse(PasswordUtils.containsUsername("test123", "admin"));
    }

    @Test
    void testIsWeakPassword() {
        assertTrue(PasswordUtils.isWeakPassword("123456"));
        assertTrue(PasswordUtils.isWeakPassword("password"));
        assertTrue(PasswordUtils.isWeakPassword("admin"));
        assertFalse(PasswordUtils.isWeakPassword("Test123@"));
    }

    @Test
    void testGenerateSalt() {
        String salt1 = PasswordUtils.generateSalt();
        String salt2 = PasswordUtils.generateSalt();
        
        assertNotNull(salt1);
        assertNotNull(salt2);
        assertNotEquals(salt1, salt2);
    }

    @Test
    void testEncryptPasswordWithSalt() {
        String password = "test123";
        String salt = PasswordUtils.generateSalt();
        String encrypted = PasswordUtils.encryptPasswordWithSalt(password, salt);
        
        assertNotNull(encrypted);
        assertNotEquals(password, encrypted);
        
        // 相同密码和盐值应该产生相同的加密结果
        String encrypted2 = PasswordUtils.encryptPasswordWithSalt(password, salt);
        assertEquals(encrypted, encrypted2);
    }
}
