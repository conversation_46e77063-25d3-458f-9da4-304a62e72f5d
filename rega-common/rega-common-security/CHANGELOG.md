# rega-common-security 更新日志

## [1.0.0] - 2024-01-XX

### 新增功能

#### 🔐 认证授权系统
- **Sa-Token集成** - 基于Sa-Token框架的认证授权
  - JWT令牌管理
  - 会话管理和超时控制
  - 单点登录支持
  - 并发登录控制

- **多种登录方式** - 支持多种登录认证方式
  - 用户名密码登录
  - 手机短信登录
  - 第三方社交登录
  - API密钥认证

#### 🛡️ 权限控制系统
- **注解式权限控制** - 声明式权限管理
  - @RequiresLogin - 登录认证注解
  - @RequiresPermissions - 权限认证注解（支持AND/OR逻辑）
  - @RequiresRoles - 角色认证注解（支持AND/OR逻辑）
  - @DataScope - 数据权限注解

- **AOP权限切面** - 自动权限验证
  - PermissionAspect - 权限验证切面
  - DataScopeAspect - 数据权限切面
  - 异常安全处理
  - 事件驱动审计

#### 📊 数据权限系统
- **多级数据权限** - 5级数据权限控制
  - 全部数据权限（1）
  - 自定数据权限（2）
  - 部门数据权限（3）
  - 部门及以下数据权限（4）
  - 仅本人数据权限（5）

- **自动SQL过滤** - 透明的数据权限实现
  - 自动在查询中添加权限条件
  - 支持自定义表别名
  - 多租户数据隔离
  - 灵活的权限配置

#### 🏢 多租户安全支持
- **TenantSecurityInterceptor** - 多租户安全拦截器
  - 租户ID自动获取（请求头/参数）
  - 租户权限验证
  - 租户上下文管理
  - 跨租户访问防护

- **租户数据隔离** - 完整的租户隔离
  - 数据权限租户过滤
  - 用户权限租户验证
  - 会话租户隔离
  - 安全事件租户标识

#### 🌐 国际化支持
- **多语言错误消息** - 完整的国际化支持
  - 中文消息（security_zh_CN.properties）
  - 英文消息（security_en_US.properties）
  - 60+条安全相关消息
  - 易于扩展新语言

- **国际化消息分类** - 结构化消息管理
  - 登录认证消息
  - 权限验证消息
  - 密码安全消息
  - 令牌管理消息
  - 租户相关消息
  - 数据权限消息

#### 🔒 密码安全系统
- **PasswordUtils** - 强大的密码工具
  - BCrypt密码加密
  - 密码强度验证（3级）
  - 弱密码检测
  - 随机密码生成
  - 盐值加密支持

- **密码策略配置** - 灵活的密码策略
  - 密码长度限制
  - 密码复杂度要求
  - 用户名包含检查
  - 重试次数限制
  - 锁定时间配置

#### 🛠️ 安全工具类
- **SecurityUtils** - 核心安全工具
  - 用户信息获取（ID、用户名、昵称、部门、租户）
  - 权限验证（单个、多个、任意、全部）
  - 角色验证（单个、多个、任意、全部）
  - 管理员判断
  - 登录状态检查

- **与common-core集成** - 避免功能重复
  - 使用BusinessException替代ServiceException
  - 集成TenantContextHolder
  - 复用CryptoUtils加密功能
  - 使用StringUtils字符串处理

#### 📝 安全事件系统
- **事件类型** - 完整的安全事件
  - SecurityEvent - 安全事件基类
  - LoginEvent - 登录事件（成功/失败）
  - PermissionEvent - 权限验证事件

- **事件处理** - 异步安全审计
  - SecurityEventListener - 安全事件监听器
  - 异步事件处理
  - 安全日志记录
  - 审计信息收集

#### ⚙️ 配置管理
- **SecurityConfig** - 安全配置类
  - 密码策略配置
  - 验证码配置
  - 登录策略配置
  - 会话管理配置

- **自动配置** - Spring Boot自动配置
  - SecurityAutoConfiguration - 安全自动配置
  - 条件化Bean注册
  - 拦截器自动注册
  - 线程池配置

#### 🧪 测试支持
- **单元测试** - 完整的测试覆盖
  - PasswordUtilsTest - 密码工具测试（15个测试方法）
  - SecurityUtilsTest - 安全工具测试（8个测试方法）

- **示例代码** - 丰富的使用示例
  - SecurityExampleController - 安全功能示例
  - 注解使用示例
  - 权限验证示例
  - 多租户使用示例

### 技术特性

#### 🔒 安全特性
- **密码安全** - BCrypt加密、强度验证、防暴力破解
- **会话安全** - JWT令牌、会话超时、并发控制
- **权限安全** - 细粒度权限控制、动态权限验证
- **数据安全** - 多级数据权限、自动SQL过滤

#### 🏢 多租户特性
- **完全隔离** - 数据、权限、会话完全隔离
- **自动管理** - 租户上下文自动设置和清理
- **安全验证** - 防止跨租户数据访问
- **灵活配置** - 支持多种租户ID传递方式

#### 🌐 国际化特性
- **多语言支持** - 中英文完整支持
- **易于扩展** - 简单添加新语言支持
- **统一管理** - 集中管理安全相关消息
- **动态切换** - 支持运行时语言切换

#### 🔄 集成特性
- **无缝集成** - 与common-core完美集成
- **避免重复** - 复用核心模块功能
- **统一异常** - 使用BusinessException统一异常处理
- **租户集成** - 集成租户上下文管理

### 配置示例

```yaml
# Sa-Token配置
sa-token:
  token-name: Authorization
  timeout: 2592000
  activity-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false

# 安全配置
rega:
  security:
    enabled: true
    password:
      min-length: 6
      max-length: 20
      complexity: 2
      check-weak: true
      check-username: true
      max-retry-count: 5
      lock-time: 10
    captcha:
      enabled: true
      type: math
      length: 4
      expire-time: 2
    login:
      max-retry-count: 5
      lock-time: 10
      single-login: false
      session-timeout: 30
```

### 使用示例

```java
// 权限控制注解
@RequiresPermissions("system:user:list")
@RequiresRoles(value = {"admin", "hr"}, logical = RequiresRoles.Logical.OR)
@DataScope(deptAlias = "d", userAlias = "u")

// 安全工具使用
SecurityUtils.getUserId();
SecurityUtils.hasPermi("system:user:edit");
SecurityUtils.getTenantId();

// 密码工具使用
PasswordUtils.encryptPassword("password");
PasswordUtils.getPasswordStrength("Test123@");
PasswordUtils.generateRandomPassword(12);
```

### 重要修复

#### 🔧 异常处理统一
- **替换ServiceException** - 全部替换为BusinessException
  - SecurityUtils中的异常处理
  - PermissionAspect中的异常处理
  - 统一使用国际化消息键

- **国际化消息完善** - 补充缺失的消息键
  - security.user.dept.get.error
  - security.user.username.get.error
  - security.user.nickname.get.error

### 依赖管理

#### 核心依赖
- `cn.dev33:sa-token-spring-boot3-starter` - Sa-Token认证框架
- `cn.dev33:sa-token-jwt` - JWT令牌支持
- `cn.dev33:sa-token-redis-jackson` - Redis缓存支持（可选）
- `org.springframework.security:spring-security-crypto` - 密码加密
- `org.bouncycastle:bcprov-jdk18on:1.76` - 加密算法支持

#### 内部依赖
- `rega-common-core` - 核心模块（必需）
- `rega-common-redis` - Redis模块（可选）

### 注意事项

1. **异常处理** - 统一使用BusinessException和国际化消息
2. **多租户** - 确保所有安全操作都考虑租户隔离
3. **性能考虑** - 权限验证会增加一定的性能开销
4. **缓存策略** - 建议对权限信息进行适当缓存
5. **安全配置** - 生产环境需要配置强密码策略

### 后续计划

- [ ] 添加OAuth2.0支持
- [ ] 集成LDAP认证
- [ ] 添加生物识别认证
- [ ] 完善安全审计功能
- [ ] 添加安全策略引擎
- [ ] 支持动态权限配置
