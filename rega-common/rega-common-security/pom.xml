<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.rega.erp</groupId>
        <artifactId>rega-common</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>rega-common-security</artifactId>
    <name>RegaWebERP Common Security</name>
    <description>RegaWebERP Common Security Module</description>

    <dependencies>
        <!-- 内部依赖 -->
<!--        <dependency>-->
<!--            <groupId>com.rega.erp</groupId>-->
<!--            <artifactId>rega-common-core</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.rega.erp</groupId>-->
<!--            <artifactId>rega-common-redis</artifactId>-->
<!--        </dependency>-->

        <!-- 安全相关依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-jwt</artifactId>
        </dependency>

        <!-- 其他工具依赖 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project> 