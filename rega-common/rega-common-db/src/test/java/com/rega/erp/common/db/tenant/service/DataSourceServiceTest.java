package com.rega.erp.common.db.tenant.service;

import com.rega.erp.common.db.tenant.domain.SysDataSource;
import com.rega.erp.common.db.tenant.domain.SysDataSourceMonitor;
import com.rega.erp.common.db.tenant.domain.SysTenantDataSource;
import com.rega.erp.common.db.tenant.service.impl.DataSourceServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.anyline.service.AnylineService;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 数据源服务测试
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class DataSourceServiceTest {

    @Mock
    private AnylineService anylineService;

    @InjectMocks
    private DataSourceServiceImpl dataSourceService;

    private SysDataSource testDataSource;

    @BeforeEach
    void setUp() {
        testDataSource = createTestDataSource();
    }

    @Test
    @DisplayName("测试创建数据源配置")
    void testCreateDataSource() {
        // 准备测试数据
        when(anylineService.insert(any(SysDataSource.class))).thenReturn(1L);

        // 执行测试
        Long result = dataSourceService.createDataSource(testDataSource);

        // 验证结果
        assertNotNull(result);
        verify(anylineService, times(1)).insert(any(SysDataSource.class));
    }

    @Test
    @DisplayName("测试创建数据源配置 - 配置为空")
    void testCreateDataSourceWithNullConfig() {
        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            dataSourceService.createDataSource(null);
        });
    }

    @Test
    @DisplayName("测试创建数据源配置 - 配置不完整")
    void testCreateDataSourceWithIncompleteConfig() {
        // 准备不完整的配置
        SysDataSource incompleteConfig = new SysDataSource();
        incompleteConfig.setDatasourceName("测试数据源");
        // 缺少必要字段

        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            dataSourceService.createDataSource(incompleteConfig);
        });
    }

    @Test
    @DisplayName("测试更新数据源配置")
    void testUpdateDataSource() {
        // 准备测试数据
        testDataSource.setId(1L);
        when(anylineService.update(any(SysDataSource.class))).thenReturn(1L);

        // 执行测试
        boolean result = dataSourceService.updateDataSource(testDataSource);

        // 验证结果
        assertTrue(result);
        verify(anylineService, times(1)).update(any(SysDataSource.class));
    }

    @Test
    @DisplayName("测试删除数据源配置")
    void testDeleteDataSource() {
        // 准备测试数据
        Long datasourceId = 1L;
        when(anylineService.update(any(SysDataSource.class))).thenReturn(1L);

        // 执行测试
        boolean result = dataSourceService.deleteDataSource(datasourceId);

        // 验证结果
        assertTrue(result);
        verify(anylineService, times(1)).update(any(SysDataSource.class));
    }

    @Test
    @DisplayName("测试数据源连接测试")
    void testDataSourceConnection() {
        // 执行测试
        boolean result = dataSourceService.testDataSourceConnection(testDataSource);

        // 由于没有真实的数据库连接，这里应该返回 false 或抛出异常
        // 具体行为取决于实现
        assertFalse(result);
    }

    @Test
    @DisplayName("测试创建数据源实例")
    void testCreateDataSourceInstance() {
        // 执行测试
        DataSource result = dataSourceService.createDataSourceInstance(testDataSource);

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof com.zaxxer.hikari.HikariDataSource);
    }

    @Test
    @DisplayName("测试创建数据源实例 - 配置为空")
    void testCreateDataSourceInstanceWithNullConfig() {
        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            dataSourceService.createDataSourceInstance(null);
        });
    }

    @Test
    @DisplayName("测试租户数据源分配")
    void testAssignDataSourceToTenant() {
        // 准备测试数据
        SysTenantDataSource tenantDataSource = createTestTenantDataSource();

        // 执行测试
        boolean result = dataSourceService.assignDataSourceToTenant(tenantDataSource);

        // 由于是临时实现，应该返回 false
        assertFalse(result);
    }

    @Test
    @DisplayName("测试数据源监控记录")
    void testRecordDataSourceMonitor() {
        // 准备测试数据
        SysDataSourceMonitor monitor = createTestMonitor();

        // 执行测试（不应该抛出异常）
        assertDoesNotThrow(() -> {
            dataSourceService.recordDataSourceMonitor(monitor);
        });
    }

    @Test
    @DisplayName("测试数据源统计信息")
    void testGetDataSourceStatistics() {
        // 执行测试
        DataSourceService.DataSourceStatistics statistics = dataSourceService.getDataSourceStatistics();

        // 验证结果
        assertNotNull(statistics);
        assertEquals(0, statistics.getTotalDataSources());
        assertEquals(0, statistics.getActiveDataSources());
    }

    @Test
    @DisplayName("测试数据源配置验证")
    void testDataSourceConfigValidation() {
        // 测试完整配置
        assertTrue(testDataSource.isConfigComplete());

        // 测试不完整配置
        SysDataSource incompleteConfig = new SysDataSource();
        assertFalse(incompleteConfig.isConfigComplete());

        // 测试部分字段缺失
        incompleteConfig.setDatasourceKey("test");
        incompleteConfig.setDatasourceType("postgresql");
        incompleteConfig.setDriverClassName("org.postgresql.Driver");
        incompleteConfig.setUrl("*************************************");
        incompleteConfig.setUsername("test");
        // 缺少密码
        assertFalse(incompleteConfig.isConfigComplete());

        // 补全密码
        incompleteConfig.setPassword("test");
        assertTrue(incompleteConfig.isConfigComplete());
    }

    @Test
    @DisplayName("测试数据源配置辅助方法")
    void testDataSourceConfigHelperMethods() {
        // 测试默认数据源检查
        testDataSource.setIsDefault(true);
        assertTrue(testDataSource.isDefaultDataSource());

        testDataSource.setIsDefault(false);
        assertFalse(testDataSource.isDefaultDataSource());

        // 测试健康检查启用状态
        testDataSource.setHealthCheckEnabled(true);
        assertTrue(testDataSource.isHealthCheckEnabled());

        testDataSource.setHealthCheckEnabled(false);
        assertFalse(testDataSource.isHealthCheckEnabled());

        // 测试显示名称
        String displayName = testDataSource.getDisplayName();
        assertTrue(displayName.contains(testDataSource.getDatasourceName()));
        assertTrue(displayName.contains(testDataSource.getDatasourceKey()));

        // 测试连接池配置摘要
        String poolSummary = testDataSource.getPoolConfigSummary();
        assertNotNull(poolSummary);
        assertTrue(poolSummary.contains("初始"));
        assertTrue(poolSummary.contains("最大"));
    }

    // =====================================================
    // 辅助方法
    // =====================================================

    private SysDataSource createTestDataSource() {
        SysDataSource dataSource = new SysDataSource();
        dataSource.setDatasourceName("测试数据源");
        dataSource.setDatasourceKey("test_datasource");
        dataSource.setDatasourceType("postgresql");
        dataSource.setDriverClassName("org.postgresql.Driver");
        dataSource.setUrl("****************************************");
        dataSource.setUsername("test_user");
        dataSource.setPassword("test_password");
        dataSource.setInitialSize(5);
        dataSource.setMinIdle(5);
        dataSource.setMaxActive(20);
        dataSource.setMaxWait(60000L);
        dataSource.setTestOnBorrow(true);
        dataSource.setTestOnReturn(false);
        dataSource.setTestWhileIdle(true);
        dataSource.setValidationQuery("SELECT 1");
        dataSource.setStatus(1);
        dataSource.setIsDefault(false);
        dataSource.setHealthCheckEnabled(true);
        dataSource.setHealthCheckInterval(300000L);
        dataSource.setMaxRetryCount(3);
        dataSource.setDescription("测试用数据源");
        dataSource.setCreateTime(LocalDateTime.now());
        dataSource.setUpdateTime(LocalDateTime.now());
        return dataSource;
    }

    private SysTenantDataSource createTestTenantDataSource() {
        SysTenantDataSource tenantDataSource = new SysTenantDataSource();
        tenantDataSource.setTenantId(1L);
        tenantDataSource.setDatasourceId(1L);
        tenantDataSource.setIsolationType(1);
        tenantDataSource.setIsPrimary(true);
        tenantDataSource.setPriority(0);
        tenantDataSource.setReadWeight(1);
        tenantDataSource.setWriteWeight(1);
        tenantDataSource.setStatus(1);
        return tenantDataSource;
    }

    private SysDataSourceMonitor createTestMonitor() {
        SysDataSourceMonitor monitor = new SysDataSourceMonitor();
        monitor.setDatasourceId(1L);
        monitor.setCheckTime(LocalDateTime.now());
        monitor.setStatus(1);
        monitor.setResponseTime(50L);
        monitor.setActiveConnections(2);
        monitor.setIdleConnections(3);
        monitor.setTotalConnections(5);
        monitor.setCreateTime(LocalDateTime.now());
        return monitor;
    }
}
