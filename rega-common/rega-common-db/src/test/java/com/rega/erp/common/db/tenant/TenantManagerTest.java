package com.rega.erp.common.db.tenant;

import com.rega.erp.common.db.tenant.core.TenantManager;
import com.rega.erp.common.db.tenant.domain.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 租户管理器测试
 *
 * <AUTHOR>
 */
class TenantManagerTest {
    
    private TenantManager tenantManager;
    
    @BeforeEach
    void setUp() {
        // 这里应该初始化测试环境
        // tenantManager = new TenantManager(...);
    }
    
    @Test
    @DisplayName("创建字段隔离租户")
    void testCreateFieldIsolationTenant() {
        TenantCreationRequest request = TenantCreationRequest.builder()
                .tenantId("test_tenant_001")
                .tenantName("测试租户001")
                .description("字段隔离测试租户")
                .isolationType(TenantIsolationType.FIELD_ISOLATION)
                .contactName("张三")
                .contactEmail("<EMAIL>")
                .maxUsers(100)
                .build();
        
        assertTrue(request.isValid());
        assertFalse(request.needsNewDataSource());
        assertFalse(request.hasSpecifiedDataSource());
        
        // TenantCreationResult result = tenantManager.createTenant(request);
        // assertTrue(result.isSuccess());
        // assertEquals("primary", result.getAssignedDataSourceId());
        // assertFalse(result.isNewDataSourceCreated());
    }
    
    @Test
    @DisplayName("创建数据源隔离租户")
    void testCreateDataSourceIsolationTenant() {
        DataSourceConfig dataSourceConfig = DataSourceConfig.builder()
                .driverClassName("org.postgresql.Driver")
                .url("***********************************************")
                .username("test_user")
                .password("test_password")
                .build();
        
        TenantCreationRequest request = TenantCreationRequest.builder()
                .tenantId("test_tenant_002")
                .tenantName("测试租户002")
                .description("数据源隔离测试租户")
                .isolationType(TenantIsolationType.DATASOURCE_ISOLATION)
                .dataSourceConfig(dataSourceConfig)
                .maxTenantsPerDataSource(5)
                .contactName("李四")
                .contactEmail("<EMAIL>")
                .maxUsers(500)
                .build();
        
        assertTrue(request.isValid());
        assertTrue(request.needsNewDataSource());
        assertFalse(request.hasSpecifiedDataSource());
        
        // TenantCreationResult result = tenantManager.createTenant(request);
        // assertTrue(result.isSuccess());
        // assertNotEquals("primary", result.getAssignedDataSourceId());
        // assertTrue(result.isNewDataSourceCreated());
    }
    
    @Test
    @DisplayName("创建指定数据源的租户")
    void testCreateTenantWithSpecifiedDataSource() {
        TenantCreationRequest request = TenantCreationRequest.builder()
                .tenantId("test_tenant_003")
                .tenantName("测试租户003")
                .description("指定数据源测试租户")
                .isolationType(TenantIsolationType.DATASOURCE_ISOLATION)
                .dataSourceId("existing_datasource_001")
                .contactName("王五")
                .contactEmail("<EMAIL>")
                .maxUsers(200)
                .build();
        
        assertTrue(request.isValid());
        assertFalse(request.needsNewDataSource());
        assertTrue(request.hasSpecifiedDataSource());
        
        // TenantCreationResult result = tenantManager.createTenant(request);
        // assertTrue(result.isSuccess());
        // assertEquals("existing_datasource_001", result.getAssignedDataSourceId());
        // assertFalse(result.isNewDataSourceCreated());
    }
    
    @Test
    @DisplayName("测试无效的租户请求")
    void testInvalidTenantRequest() {
        // 空租户ID
        TenantCreationRequest request1 = TenantCreationRequest.builder()
                .tenantId("")
                .tenantName("测试租户")
                .isolationType(TenantIsolationType.FIELD_ISOLATION)
                .build();
        assertFalse(request1.isValid());
        
        // 空租户名称
        TenantCreationRequest request2 = TenantCreationRequest.builder()
                .tenantId("test_tenant")
                .tenantName("")
                .isolationType(TenantIsolationType.FIELD_ISOLATION)
                .build();
        assertFalse(request2.isValid());
        
        // 空隔离类型
        TenantCreationRequest request3 = TenantCreationRequest.builder()
                .tenantId("test_tenant")
                .tenantName("测试租户")
                .build();
        assertFalse(request3.isValid());
        
        // 无效的数据源配置
        DataSourceConfig invalidConfig = DataSourceConfig.builder()
                .url("")
                .username("test")
                .password("test")
                .build();
        
        TenantCreationRequest request4 = TenantCreationRequest.builder()
                .tenantId("test_tenant")
                .tenantName("测试租户")
                .isolationType(TenantIsolationType.DATASOURCE_ISOLATION)
                .dataSourceConfig(invalidConfig)
                .build();
        assertFalse(request4.isValid());
    }
    
    @Test
    @DisplayName("测试租户信息")
    void testTenantInfo() {
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantId("test_tenant");
        tenantInfo.setTenantName("测试租户");
        tenantInfo.setIsolationType(TenantIsolationType.FIELD_ISOLATION);
        tenantInfo.setStatus(TenantStatus.ACTIVE);
        
        assertTrue(tenantInfo.isActive());
        assertFalse(tenantInfo.isExpired());
        assertTrue(tenantInfo.isFieldIsolation());
        assertFalse(tenantInfo.isDataSourceIsolation());
        
        // 测试过期状态
        tenantInfo.setExpireTime(java.time.LocalDateTime.now().minusDays(1));
        assertTrue(tenantInfo.isExpired());
        
        // 测试数据源隔离
        tenantInfo.setIsolationType(TenantIsolationType.DATASOURCE_ISOLATION);
        assertFalse(tenantInfo.isFieldIsolation());
        assertTrue(tenantInfo.isDataSourceIsolation());
    }
    
    @Test
    @DisplayName("测试数据源配置")
    void testDataSourceConfig() {
        // 有效配置
        DataSourceConfig validConfig = DataSourceConfig.builder()
                .driverClassName("org.postgresql.Driver")
                .url("****************************************")
                .username("test_user")
                .password("test_password")
                .build();
        assertTrue(validConfig.isValid());
        
        // 无效配置 - 空URL
        DataSourceConfig invalidConfig1 = DataSourceConfig.builder()
                .username("test_user")
                .password("test_password")
                .build();
        assertFalse(invalidConfig1.isValid());
        
        // 无效配置 - 空用户名
        DataSourceConfig invalidConfig2 = DataSourceConfig.builder()
                .url("****************************************")
                .password("test_password")
                .build();
        assertFalse(invalidConfig2.isValid());
        
        // 测试JDBC URL生成
        DataSourceConfig config = DataSourceConfig.builder()
                .host("localhost")
                .port(5432)
                .databaseName("test_db")
                .username("test_user")
                .password("test_password")
                .build();
        assertEquals("****************************************", config.getJdbcUrl());
        
        // 测试PostgreSQL默认配置
        DataSourceConfig pgConfig = DataSourceConfig.createPostgreSQLConfig(
                "localhost", 5432, "test_db", "test_user", "test_password");
        assertTrue(pgConfig.isValid());
        assertEquals("org.postgresql.Driver", pgConfig.getDriverClassName());
        assertEquals("****************************************", pgConfig.getJdbcUrl());
    }
}
