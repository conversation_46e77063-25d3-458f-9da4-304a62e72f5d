package com.rega.erp.common.db.tenant.domain;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据源实体类测试
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
class DataSourceEntityTest {

    private SysDataSource sysDataSource;
    private SysTenantDataSource sysTenantDataSource;
    private SysDataSourceMonitor sysDataSourceMonitor;

    @BeforeEach
    void setUp() {
        sysDataSource = createTestDataSource();
        sysTenantDataSource = createTestTenantDataSource();
        sysDataSourceMonitor = createTestMonitor();
    }

    // =====================================================
    // SysDataSource 测试
    // =====================================================

    @Test
    @DisplayName("测试数据源配置完整性检查")
    void testDataSourceConfigComplete() {
        // 测试完整配置
        assertTrue(sysDataSource.isConfigComplete());

        // 测试缺少数据源键
        sysDataSource.setDatasourceKey(null);
        assertFalse(sysDataSource.isConfigComplete());
        sysDataSource.setDatasourceKey("");
        assertFalse(sysDataSource.isConfigComplete());

        // 恢复数据源键，测试缺少数据源类型
        sysDataSource.setDatasourceKey("test");
        sysDataSource.setDatasourceType(null);
        assertFalse(sysDataSource.isConfigComplete());

        // 恢复数据源类型，测试缺少驱动类名
        sysDataSource.setDatasourceType("postgresql");
        sysDataSource.setDriverClassName(null);
        assertFalse(sysDataSource.isConfigComplete());

        // 恢复驱动类名，测试缺少URL
        sysDataSource.setDriverClassName("org.postgresql.Driver");
        sysDataSource.setUrl(null);
        assertFalse(sysDataSource.isConfigComplete());

        // 恢复URL，测试缺少用户名
        sysDataSource.setUrl("*************************************");
        sysDataSource.setUsername(null);
        assertFalse(sysDataSource.isConfigComplete());

        // 恢复用户名，测试缺少密码
        sysDataSource.setUsername("test");
        sysDataSource.setPassword(null);
        assertFalse(sysDataSource.isConfigComplete());

        // 恢复密码，应该完整
        sysDataSource.setPassword("test");
        assertTrue(sysDataSource.isConfigComplete());
    }

    @Test
    @DisplayName("测试数据源布尔属性检查")
    void testDataSourceBooleanProperties() {
        // 测试默认数据源
        sysDataSource.setIsDefault(true);
        assertTrue(sysDataSource.isDefaultDataSource());
        sysDataSource.setIsDefault(false);
        assertFalse(sysDataSource.isDefaultDataSource());
        sysDataSource.setIsDefault(null);
        assertFalse(sysDataSource.isDefaultDataSource());

        // 测试健康检查启用
        sysDataSource.setHealthCheckEnabled(true);
        assertTrue(sysDataSource.isHealthCheckEnabled());
        sysDataSource.setHealthCheckEnabled(false);
        assertFalse(sysDataSource.isHealthCheckEnabled());
        sysDataSource.setHealthCheckEnabled(null);
        assertFalse(sysDataSource.isHealthCheckEnabled());

        // 测试连接测试配置
        sysDataSource.setTestOnBorrow(true);
        assertTrue(sysDataSource.isTestOnBorrow());
        sysDataSource.setTestOnBorrow(null);
        assertFalse(sysDataSource.isTestOnBorrow());

        sysDataSource.setTestOnReturn(true);
        assertTrue(sysDataSource.isTestOnReturn());
        sysDataSource.setTestOnReturn(null);
        assertFalse(sysDataSource.isTestOnReturn());

        sysDataSource.setTestWhileIdle(true);
        assertTrue(sysDataSource.isTestWhileIdle());
        sysDataSource.setTestWhileIdle(null);
        assertFalse(sysDataSource.isTestWhileIdle());

        sysDataSource.setPoolPreparedStatements(true);
        assertTrue(sysDataSource.isPoolPreparedStatements());
        sysDataSource.setPoolPreparedStatements(null);
        assertFalse(sysDataSource.isPoolPreparedStatements());
    }

    @Test
    @DisplayName("测试数据源辅助方法")
    void testDataSourceHelperMethods() {
        // 测试密码掩码
        sysDataSource.setPassword("123456789");
        String maskedPassword = sysDataSource.getMaskedPassword();
        assertTrue(maskedPassword.contains("12"));
        assertTrue(maskedPassword.contains("89"));
        assertTrue(maskedPassword.contains("****"));

        // 测试短密码
        sysDataSource.setPassword("123");
        assertEquals("****", sysDataSource.getMaskedPassword());

        // 测试空密码
        sysDataSource.setPassword(null);
        assertEquals("****", sysDataSource.getMaskedPassword());

        // 测试显示名称
        String displayName = sysDataSource.getDisplayName();
        assertTrue(displayName.contains(sysDataSource.getDatasourceName()));
        assertTrue(displayName.contains(sysDataSource.getDatasourceKey()));

        // 测试连接池配置摘要
        String poolSummary = sysDataSource.getPoolConfigSummary();
        assertTrue(poolSummary.contains("初始"));
        assertTrue(poolSummary.contains("最大"));
        assertTrue(poolSummary.contains("超时"));
    }

    // =====================================================
    // SysTenantDataSource 测试
    // =====================================================

    @Test
    @DisplayName("测试租户数据源关联属性")
    void testTenantDataSourceProperties() {
        // 测试主数据源
        sysTenantDataSource.setIsPrimary(true);
        assertTrue(sysTenantDataSource.isPrimaryDataSource());
        sysTenantDataSource.setIsPrimary(false);
        assertFalse(sysTenantDataSource.isPrimaryDataSource());
        sysTenantDataSource.setIsPrimary(null);
        assertFalse(sysTenantDataSource.isPrimaryDataSource());

        // 测试隔离类型
        sysTenantDataSource.setIsolationType(1);
        assertTrue(sysTenantDataSource.isFieldIsolation());
        assertFalse(sysTenantDataSource.isDataSourceIsolation());

        sysTenantDataSource.setIsolationType(2);
        assertFalse(sysTenantDataSource.isFieldIsolation());
        assertTrue(sysTenantDataSource.isDataSourceIsolation());

        sysTenantDataSource.setIsolationType(null);
        assertFalse(sysTenantDataSource.isFieldIsolation());
        assertFalse(sysTenantDataSource.isDataSourceIsolation());
    }

    @Test
    @DisplayName("测试租户数据源关联描述方法")
    void testTenantDataSourceDescriptions() {
        // 测试隔离类型描述
        sysTenantDataSource.setIsolationType(1);
        assertEquals("字段隔离", sysTenantDataSource.getIsolationTypeDesc());

        sysTenantDataSource.setIsolationType(2);
        assertEquals("数据源隔离", sysTenantDataSource.getIsolationTypeDesc());

        sysTenantDataSource.setIsolationType(3);
        assertEquals("未知类型", sysTenantDataSource.getIsolationTypeDesc());

        sysTenantDataSource.setIsolationType(null);
        assertEquals("未知", sysTenantDataSource.getIsolationTypeDesc());

        // 测试优先级描述
        sysTenantDataSource.setPriority(0);
        assertEquals("最高", sysTenantDataSource.getPriorityDesc());

        sysTenantDataSource.setPriority(1);
        assertEquals("高", sysTenantDataSource.getPriorityDesc());

        sysTenantDataSource.setPriority(2);
        assertEquals("中", sysTenantDataSource.getPriorityDesc());

        sysTenantDataSource.setPriority(3);
        assertEquals("低", sysTenantDataSource.getPriorityDesc());

        sysTenantDataSource.setPriority(5);
        assertEquals("自定义(5)", sysTenantDataSource.getPriorityDesc());

        sysTenantDataSource.setPriority(null);
        assertEquals("默认", sysTenantDataSource.getPriorityDesc());

        // 测试权重摘要
        sysTenantDataSource.setReadWeight(2);
        sysTenantDataSource.setWriteWeight(3);
        String weightSummary = sysTenantDataSource.getWeightSummary();
        assertTrue(weightSummary.contains("读权重:2"));
        assertTrue(weightSummary.contains("写权重:3"));
    }

    @Test
    @DisplayName("测试租户数据源关联配置验证")
    void testTenantDataSourceValidation() {
        // 测试有效配置
        assertTrue(sysTenantDataSource.isValidConfig());

        // 测试无效租户ID
        sysTenantDataSource.setTenantId(null);
        assertFalse(sysTenantDataSource.isValidConfig());
        sysTenantDataSource.setTenantId(0L);
        assertFalse(sysTenantDataSource.isValidConfig());

        // 恢复租户ID，测试无效数据源ID
        sysTenantDataSource.setTenantId(1L);
        sysTenantDataSource.setDatasourceId(null);
        assertFalse(sysTenantDataSource.isValidConfig());

        // 恢复数据源ID，测试无效隔离类型
        sysTenantDataSource.setDatasourceId(1L);
        sysTenantDataSource.setIsolationType(3);
        assertFalse(sysTenantDataSource.isValidConfig());

        // 恢复隔离类型，测试无效优先级
        sysTenantDataSource.setIsolationType(1);
        sysTenantDataSource.setPriority(-1);
        assertFalse(sysTenantDataSource.isValidConfig());

        // 恢复优先级，测试无效权重
        sysTenantDataSource.setPriority(0);
        sysTenantDataSource.setReadWeight(0);
        assertFalse(sysTenantDataSource.isValidConfig());

        sysTenantDataSource.setReadWeight(1);
        sysTenantDataSource.setWriteWeight(0);
        assertFalse(sysTenantDataSource.isValidConfig());

        // 恢复权重，应该有效
        sysTenantDataSource.setWriteWeight(1);
        assertTrue(sysTenantDataSource.isValidConfig());
    }

    @Test
    @DisplayName("测试租户数据源关联静态工厂方法")
    void testTenantDataSourceFactoryMethods() {
        // 测试字段隔离配置创建
        SysTenantDataSource fieldIsolation = SysTenantDataSource.createFieldIsolation(1L, 2L, true);
        assertEquals(1L, fieldIsolation.getTenantId());
        assertEquals(2L, fieldIsolation.getDatasourceId());
        assertEquals(1, fieldIsolation.getIsolationType());
        assertTrue(fieldIsolation.isPrimaryDataSource());
        assertEquals(0, fieldIsolation.getPriority());
        assertEquals(1, fieldIsolation.getStatus());

        // 测试数据源隔离配置创建
        SysTenantDataSource dataSourceIsolation = SysTenantDataSource.createDataSourceIsolation(3L, 4L, false);
        assertEquals(3L, dataSourceIsolation.getTenantId());
        assertEquals(4L, dataSourceIsolation.getDatasourceId());
        assertEquals(2, dataSourceIsolation.getIsolationType());
        assertFalse(dataSourceIsolation.isPrimaryDataSource());
        assertEquals(1, dataSourceIsolation.getPriority());
        assertEquals(1, dataSourceIsolation.getStatus());
    }

    // =====================================================
    // SysDataSourceMonitor 测试
    // =====================================================

    @Test
    @DisplayName("测试数据源监控健康状态")
    void testDataSourceMonitorHealth() {
        // 测试健康状态
        sysDataSourceMonitor.setStatus(1);
        assertTrue(sysDataSourceMonitor.isHealthy());

        sysDataSourceMonitor.setStatus(0);
        assertFalse(sysDataSourceMonitor.isHealthy());

        sysDataSourceMonitor.setStatus(null);
        assertFalse(sysDataSourceMonitor.isHealthy());

        // 测试响应时间正常性
        sysDataSourceMonitor.setResponseTime(500L);
        assertTrue(sysDataSourceMonitor.isResponseTimeNormal());

        sysDataSourceMonitor.setResponseTime(1500L);
        assertFalse(sysDataSourceMonitor.isResponseTimeNormal());

        sysDataSourceMonitor.setResponseTime(null);
        assertFalse(sysDataSourceMonitor.isResponseTimeNormal());
    }

    @Test
    @DisplayName("测试数据源监控连接池健康状态")
    void testDataSourceMonitorConnectionPool() {
        // 测试连接池健康状态
        sysDataSourceMonitor.setTotalConnections(10);
        sysDataSourceMonitor.setActiveConnections(5);
        assertTrue(sysDataSourceMonitor.isConnectionPoolHealthy());

        // 测试活跃连接数过高
        sysDataSourceMonitor.setActiveConnections(9);
        assertFalse(sysDataSourceMonitor.isConnectionPoolHealthy());

        // 测试总连接数为0
        sysDataSourceMonitor.setTotalConnections(0);
        assertFalse(sysDataSourceMonitor.isConnectionPoolHealthy());

        // 测试连接池使用率
        sysDataSourceMonitor.setTotalConnections(10);
        sysDataSourceMonitor.setActiveConnections(3);
        assertEquals(30.0, sysDataSourceMonitor.getConnectionPoolUsage(), 0.01);

        sysDataSourceMonitor.setActiveConnections(null);
        assertEquals(0.0, sysDataSourceMonitor.getConnectionPoolUsage(), 0.01);
    }

    @Test
    @DisplayName("测试数据源监控描述方法")
    void testDataSourceMonitorDescriptions() {
        // 测试状态描述
        sysDataSourceMonitor.setStatus(1);
        assertEquals("正常", sysDataSourceMonitor.getStatusDesc());

        sysDataSourceMonitor.setStatus(0);
        assertEquals("异常", sysDataSourceMonitor.getStatusDesc());

        sysDataSourceMonitor.setStatus(null);
        assertEquals("未知", sysDataSourceMonitor.getStatusDesc());

        // 测试响应时间描述
        sysDataSourceMonitor.setResponseTime(50L);
        assertTrue(sysDataSourceMonitor.getResponseTimeDesc().contains("优秀"));

        sysDataSourceMonitor.setResponseTime(300L);
        assertTrue(sysDataSourceMonitor.getResponseTimeDesc().contains("良好"));

        sysDataSourceMonitor.setResponseTime(800L);
        assertTrue(sysDataSourceMonitor.getResponseTimeDesc().contains("一般"));

        sysDataSourceMonitor.setResponseTime(1500L);
        assertTrue(sysDataSourceMonitor.getResponseTimeDesc().contains("较慢"));

        sysDataSourceMonitor.setResponseTime(null);
        assertEquals("未知", sysDataSourceMonitor.getResponseTimeDesc());
    }

    @Test
    @DisplayName("测试数据源监控告警判断")
    void testDataSourceMonitorAlert() {
        // 正常状态，不需要告警
        sysDataSourceMonitor.setStatus(1);
        sysDataSourceMonitor.setResponseTime(100L);
        sysDataSourceMonitor.setTotalConnections(10);
        sysDataSourceMonitor.setActiveConnections(3);
        sysDataSourceMonitor.setCpuUsage(new BigDecimal("50.0"));
        sysDataSourceMonitor.setMemoryUsage(new BigDecimal("60.0"));
        sysDataSourceMonitor.setDiskUsage(new BigDecimal("70.0"));
        assertFalse(sysDataSourceMonitor.needsAlert());
        assertEquals("正常", sysDataSourceMonitor.getAlertLevel());

        // 状态异常，需要告警
        sysDataSourceMonitor.setStatus(0);
        assertTrue(sysDataSourceMonitor.needsAlert());
        assertEquals("严重", sysDataSourceMonitor.getAlertLevel());

        // 恢复状态，测试响应时间过长
        sysDataSourceMonitor.setStatus(1);
        sysDataSourceMonitor.setResponseTime(6000L);
        assertTrue(sysDataSourceMonitor.needsAlert());
        assertEquals("警告", sysDataSourceMonitor.getAlertLevel());

        // 测试严重响应时间
        sysDataSourceMonitor.setResponseTime(15000L);
        assertTrue(sysDataSourceMonitor.needsAlert());
        assertEquals("严重", sysDataSourceMonitor.getAlertLevel());

        // 恢复响应时间，测试连接池使用率过高
        sysDataSourceMonitor.setResponseTime(100L);
        sysDataSourceMonitor.setActiveConnections(10);
        assertTrue(sysDataSourceMonitor.needsAlert());
        assertEquals("严重", sysDataSourceMonitor.getAlertLevel());

        // 测试CPU使用率过高
        sysDataSourceMonitor.setActiveConnections(3);
        sysDataSourceMonitor.setCpuUsage(new BigDecimal("92.0"));
        assertTrue(sysDataSourceMonitor.needsAlert());
        assertEquals("警告", sysDataSourceMonitor.getAlertLevel());

        // 测试严重CPU使用率
        sysDataSourceMonitor.setCpuUsage(new BigDecimal("97.0"));
        assertTrue(sysDataSourceMonitor.needsAlert());
        assertEquals("严重", sysDataSourceMonitor.getAlertLevel());
    }

    // =====================================================
    // 辅助方法
    // =====================================================

    private SysDataSource createTestDataSource() {
        SysDataSource dataSource = new SysDataSource();
        dataSource.setDatasourceName("测试数据源");
        dataSource.setDatasourceKey("test_datasource");
        dataSource.setDatasourceType("postgresql");
        dataSource.setDriverClassName("org.postgresql.Driver");
        dataSource.setUrl("*************************************_db");
        dataSource.setUsername("test_user");
        dataSource.setPassword("test_password");
        dataSource.setInitialSize(5);
        dataSource.setMinIdle(5);
        dataSource.setMaxActive(20);
        dataSource.setMaxWait(60000L);
        dataSource.setTestOnBorrow(true);
        dataSource.setTestOnReturn(false);
        dataSource.setTestWhileIdle(true);
        dataSource.setValidationQuery("SELECT 1");
        dataSource.setStatus(1);
        dataSource.setIsDefault(false);
        dataSource.setHealthCheckEnabled(true);
        dataSource.setHealthCheckInterval(300000L);
        dataSource.setMaxRetryCount(3);
        dataSource.setDescription("测试用数据源");
        return dataSource;
    }

    private SysTenantDataSource createTestTenantDataSource() {
        SysTenantDataSource tenantDataSource = new SysTenantDataSource();
        tenantDataSource.setTenantId(1L);
        tenantDataSource.setDatasourceId(1L);
        tenantDataSource.setIsolationType(1);
        tenantDataSource.setIsPrimary(true);
        tenantDataSource.setPriority(0);
        tenantDataSource.setReadWeight(1);
        tenantDataSource.setWriteWeight(1);
        tenantDataSource.setStatus(1);
        return tenantDataSource;
    }

    private SysDataSourceMonitor createTestMonitor() {
        SysDataSourceMonitor monitor = new SysDataSourceMonitor();
        monitor.setDatasourceId(1L);
        monitor.setCheckTime(LocalDateTime.now());
        monitor.setStatus(1);
        monitor.setResponseTime(50L);
        monitor.setActiveConnections(2);
        monitor.setIdleConnections(3);
        monitor.setTotalConnections(5);
        monitor.setCpuUsage(new BigDecimal("30.0"));
        monitor.setMemoryUsage(new BigDecimal("40.0"));
        monitor.setDiskUsage(new BigDecimal("50.0"));
        monitor.setCreateTime(LocalDateTime.now());
        return monitor;
    }
}
