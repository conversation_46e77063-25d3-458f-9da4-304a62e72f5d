package com.rega.erp.common.db.tenant.datasource;

import com.rega.erp.common.db.tenant.domain.SysDataSource;
import com.rega.erp.common.db.tenant.service.DataSourceService;
import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 数据源注册表测试
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class DataSourceRegistryTest {

    @Mock
    private DataSourceService dataSourceService;

    @InjectMocks
    private DataSourceRegistry dataSourceRegistry;

    private SysDataSource testDataSource;
    private DataSource testDataSourceInstance;

    @BeforeEach
    void setUp() {
        testDataSource = createTestDataSource();
        testDataSourceInstance = createTestDataSourceInstance();
    }

    @Test
    @DisplayName("测试注册数据源")
    void testRegisterDataSource() {
        // 准备测试数据
        when(dataSourceService.createDataSourceInstance(any(SysDataSource.class)))
                .thenReturn(testDataSourceInstance);

        // 执行测试
        assertDoesNotThrow(() -> {
            dataSourceRegistry.registerDataSource(testDataSource);
        });

        // 验证数据源已注册
        DataSource retrievedDataSource = dataSourceRegistry.getDataSource(testDataSource.getDatasourceKey());
        assertNotNull(retrievedDataSource);
        assertEquals(testDataSourceInstance, retrievedDataSource);

        // 验证配置已缓存
        SysDataSource retrievedConfig = dataSourceRegistry.getDataSourceConfig(testDataSource.getDatasourceKey());
        assertNotNull(retrievedConfig);
        assertEquals(testDataSource, retrievedConfig);
    }

    @Test
    @DisplayName("测试注册数据源 - 配置为空")
    void testRegisterDataSourceWithNullConfig() {
        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            dataSourceRegistry.registerDataSource((SysDataSource) null);
        });
    }

    @Test
    @DisplayName("测试注册数据源 - 数据源键为空")
    void testRegisterDataSourceWithEmptyKey() {
        // 准备测试数据
        testDataSource.setDatasourceKey("");

        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            dataSourceRegistry.registerDataSource(testDataSource);
        });
    }

    @Test
    @DisplayName("测试注册数据源 - 重复注册")
    void testRegisterDataSourceDuplicate() {
        // 准备测试数据
        when(dataSourceService.createDataSourceInstance(any(SysDataSource.class)))
                .thenReturn(testDataSourceInstance);

        // 第一次注册
        assertDoesNotThrow(() -> {
            dataSourceRegistry.registerDataSource(testDataSource);
        });

        // 第二次注册相同的数据源键
        assertThrows(Exception.class, () -> {
            dataSourceRegistry.registerDataSource(testDataSource);
        });
    }

    @Test
    @DisplayName("测试直接注册数据源实例")
    void testRegisterDataSourceWithInstance() {
        // 执行测试
        assertDoesNotThrow(() -> {
            dataSourceRegistry.registerDataSource(
                    testDataSource.getDatasourceKey(),
                    testDataSourceInstance,
                    testDataSource
            );
        });

        // 验证数据源已注册
        DataSource retrievedDataSource = dataSourceRegistry.getDataSource(testDataSource.getDatasourceKey());
        assertNotNull(retrievedDataSource);
        assertEquals(testDataSourceInstance, retrievedDataSource);
    }

    @Test
    @DisplayName("测试获取数据源 - 从数据库加载")
    void testGetDataSourceFromDatabase() {
        // 准备测试数据
        when(dataSourceService.getDataSourceByKey(testDataSource.getDatasourceKey()))
                .thenReturn(testDataSource);
        when(dataSourceService.createDataSourceInstance(testDataSource))
                .thenReturn(testDataSourceInstance);

        // 执行测试
        DataSource result = dataSourceRegistry.getDataSource(testDataSource.getDatasourceKey());

        // 验证结果
        assertNotNull(result);
        assertEquals(testDataSourceInstance, result);

        // 验证服务调用
        verify(dataSourceService, times(1)).getDataSourceByKey(testDataSource.getDatasourceKey());
        verify(dataSourceService, times(1)).createDataSourceInstance(testDataSource);
    }

    @Test
    @DisplayName("测试获取数据源配置")
    void testGetDataSourceConfig() {
        // 准备测试数据
        when(dataSourceService.getDataSourceByKey(testDataSource.getDatasourceKey()))
                .thenReturn(testDataSource);

        // 执行测试
        SysDataSource result = dataSourceRegistry.getDataSourceConfig(testDataSource.getDatasourceKey());

        // 验证结果
        assertNotNull(result);
        assertEquals(testDataSource, result);

        // 验证服务调用
        verify(dataSourceService, times(1)).getDataSourceByKey(testDataSource.getDatasourceKey());
    }

    @Test
    @DisplayName("测试更新数据源配置")
    void testUpdateDataSource() {
        // 先注册数据源
        when(dataSourceService.createDataSourceInstance(any(SysDataSource.class)))
                .thenReturn(testDataSourceInstance);
        dataSourceRegistry.registerDataSource(testDataSource);

        // 准备更新数据
        when(dataSourceService.updateDataSource(any(SysDataSource.class))).thenReturn(true);
        when(dataSourceService.createDataSourceInstance(any(SysDataSource.class)))
                .thenReturn(testDataSourceInstance);

        // 执行更新
        testDataSource.setDescription("更新后的描述");
        assertDoesNotThrow(() -> {
            dataSourceRegistry.updateDataSource(testDataSource);
        });

        // 验证服务调用
        verify(dataSourceService, times(1)).updateDataSource(testDataSource);
    }

    @Test
    @DisplayName("测试删除数据源")
    void testRemoveDataSource() {
        // 先注册数据源
        when(dataSourceService.createDataSourceInstance(any(SysDataSource.class)))
                .thenReturn(testDataSourceInstance);
        dataSourceRegistry.registerDataSource(testDataSource);

        // 准备删除数据
        testDataSource.setId(1L);
        when(dataSourceService.deleteDataSource(1L)).thenReturn(true);

        // 执行删除
        assertDoesNotThrow(() -> {
            dataSourceRegistry.removeDataSource(testDataSource.getDatasourceKey());
        });

        // 验证数据源已删除
        DataSource result = dataSourceRegistry.getDataSource(testDataSource.getDatasourceKey());
        // 由于从数据库加载会返回 null（因为已删除），所以这里应该是 null
        when(dataSourceService.getDataSourceByKey(testDataSource.getDatasourceKey())).thenReturn(null);
        result = dataSourceRegistry.getDataSource(testDataSource.getDatasourceKey());
        assertNull(result);
    }

    @Test
    @DisplayName("测试检查数据源是否存在")
    void testExistsDataSource() {
        // 先注册数据源
        when(dataSourceService.createDataSourceInstance(any(SysDataSource.class)))
                .thenReturn(testDataSourceInstance);
        dataSourceRegistry.registerDataSource(testDataSource);

        // 测试存在的数据源
        assertTrue(dataSourceRegistry.existsDataSource(testDataSource.getDatasourceKey()));

        // 测试不存在的数据源
        when(dataSourceService.getDataSourceByKey("non_existent")).thenReturn(null);
        assertFalse(dataSourceRegistry.existsDataSource("non_existent"));

        // 测试空键
        assertFalse(dataSourceRegistry.existsDataSource(""));
        assertFalse(dataSourceRegistry.existsDataSource(null));
    }

    @Test
    @DisplayName("测试检查数据源是否可用")
    void testIsDataSourceAvailable() {
        // 准备测试数据
        when(dataSourceService.getDataSourceByKey(testDataSource.getDatasourceKey()))
                .thenReturn(testDataSource);

        // 测试可用的数据源
        testDataSource.setStatus(1);
        assertTrue(dataSourceRegistry.isDataSourceAvailable(testDataSource.getDatasourceKey()));

        // 测试不可用的数据源
        testDataSource.setStatus(0);
        assertFalse(dataSourceRegistry.isDataSourceAvailable(testDataSource.getDatasourceKey()));

        // 测试不存在的数据源
        when(dataSourceService.getDataSourceByKey("non_existent")).thenReturn(null);
        assertFalse(dataSourceRegistry.isDataSourceAvailable("non_existent"));
    }

    @Test
    @DisplayName("测试获取主数据源")
    void testGetPrimaryDataSource() {
        // 准备测试数据
        testDataSource.setIsDefault(true);
        when(dataSourceService.getAllDataSources()).thenReturn(List.of(testDataSource));
        when(dataSourceService.createDataSourceInstance(testDataSource)).thenReturn(testDataSourceInstance);

        // 执行测试
        DataSource result = dataSourceRegistry.getPrimaryDataSource();

        // 验证结果
        assertNotNull(result);
        assertEquals(testDataSourceInstance, result);
    }

    @Test
    @DisplayName("测试根据租户查找数据源")
    void testFindDataSourceByTenant() {
        // 准备测试数据
        Long tenantId = 1L;
        when(dataSourceService.getTenantDataSources(tenantId)).thenReturn(List.of());

        // 执行测试
        SysDataSource result = dataSourceRegistry.findDataSourceByTenant(tenantId);

        // 验证结果（空列表应该返回 null）
        assertNull(result);

        // 验证服务调用
        verify(dataSourceService, times(1)).getTenantDataSources(tenantId);
    }

    @Test
    @DisplayName("测试获取数据源数量")
    void testGetDataSourceCount() {
        // 准备测试数据
        when(dataSourceService.getAllDataSources()).thenReturn(List.of(testDataSource));

        // 执行测试
        int count = dataSourceRegistry.getDataSourceCount();

        // 验证结果
        assertEquals(1, count);
    }

    @Test
    @DisplayName("测试获取活跃数据源数量")
    void testGetActiveDataSourceCount() {
        // 准备测试数据
        testDataSource.setStatus(1);
        when(dataSourceService.getAllDataSources()).thenReturn(List.of(testDataSource));

        // 执行测试
        long count = dataSourceRegistry.getActiveDataSourceCount();

        // 验证结果
        assertEquals(1, count);
    }

    @Test
    @DisplayName("测试刷新缓存")
    void testRefreshCache() {
        // 准备测试数据
        when(dataSourceService.getAllDataSources()).thenReturn(List.of(testDataSource));

        // 执行测试
        assertDoesNotThrow(() -> {
            dataSourceRegistry.refreshCache();
        });

        // 验证服务调用
        verify(dataSourceService, times(1)).getAllDataSources();
    }

    @Test
    @DisplayName("测试清空所有数据源")
    void testClearAll() {
        // 先注册一个数据源
        when(dataSourceService.createDataSourceInstance(any(SysDataSource.class)))
                .thenReturn(testDataSourceInstance);
        dataSourceRegistry.registerDataSource(testDataSource);

        // 执行清空
        assertDoesNotThrow(() -> {
            dataSourceRegistry.clearAll();
        });

        // 验证数据源已清空
        DataSource result = dataSourceRegistry.getDataSource(testDataSource.getDatasourceKey());
        // 由于缓存已清空，会尝试从数据库加载
        when(dataSourceService.getDataSourceByKey(testDataSource.getDatasourceKey())).thenReturn(null);
        result = dataSourceRegistry.getDataSource(testDataSource.getDatasourceKey());
        assertNull(result);
    }

    // =====================================================
    // 辅助方法
    // =====================================================

    private SysDataSource createTestDataSource() {
        SysDataSource dataSource = new SysDataSource();
        dataSource.setDatasourceName("测试数据源");
        dataSource.setDatasourceKey("test_datasource");
        dataSource.setDatasourceType("postgresql");
        dataSource.setDriverClassName("org.postgresql.Driver");
        dataSource.setUrl("****************************************");
        dataSource.setUsername("test_user");
        dataSource.setPassword("test_password");
        dataSource.setStatus(1);
        dataSource.setIsDefault(false);
        dataSource.setCreateTime(LocalDateTime.now());
        dataSource.setUpdateTime(LocalDateTime.now());
        return dataSource;
    }

    private DataSource createTestDataSourceInstance() {
        // 创建一个简单的 HikariDataSource 实例用于测试
        return mock(HikariDataSource.class);
    }
}
