package com.rega.erp.common.db.tenant.datasource;

import com.rega.erp.common.db.tenant.core.TenantRegistry;
import com.rega.erp.common.db.tenant.domain.SysDataSource;
import com.rega.erp.common.db.tenant.domain.SysTenantDataSource;
import com.rega.erp.common.db.tenant.service.DataSourceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 租户数据源管理器测试
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class TenantDataSourceManagerTest {

    @Mock
    private DataSourceRegistry dataSourceRegistry;

    @Mock
    private DataSourceService dataSourceService;

    @Mock
    private TenantRegistry tenantRegistry;

    @InjectMocks
    private TenantDataSourceManager tenantDataSourceManager;

    private SysDataSource primaryDataSource;
    private SysDataSource dedicatedDataSource;

    @BeforeEach
    void setUp() {
        primaryDataSource = createPrimaryDataSource();
        dedicatedDataSource = createDedicatedDataSource();
    }

    @Test
    @DisplayName("测试为租户分配字段隔离数据源")
    void testAssignFieldIsolationDataSource() {
        // 准备测试数据
        Long tenantId = 1L;
        Integer isolationType = 1; // 字段隔离

        when(dataSourceService.getAllDataSources()).thenReturn(List.of(primaryDataSource));
        when(dataSourceService.assignDataSourceToTenant(any(SysTenantDataSource.class))).thenReturn(true);

        // 执行测试
        String result = tenantDataSourceManager.assignDataSourceToTenant(tenantId, isolationType, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(primaryDataSource.getDatasourceKey(), result);

        // 验证方法调用
        verify(dataSourceService, times(1)).getAllDataSources();
        verify(dataSourceService, times(1)).assignDataSourceToTenant(any(SysTenantDataSource.class));
    }

    @Test
    @DisplayName("测试为租户分配数据源隔离数据源")
    void testAssignDataSourceIsolationDataSource() {
        // 准备测试数据
        Long tenantId = 2L;
        Integer isolationType = 2; // 数据源隔离

        when(dataSourceService.getAllDataSources()).thenReturn(List.of(primaryDataSource, dedicatedDataSource));
        when(dataSourceService.assignDataSourceToTenant(any(SysTenantDataSource.class))).thenReturn(true);

        // 执行测试
        String result = tenantDataSourceManager.assignDataSourceToTenant(tenantId, isolationType, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(dedicatedDataSource.getDatasourceKey(), result);

        // 验证方法调用
        verify(dataSourceService, times(1)).getAllDataSources();
        verify(dataSourceService, times(1)).assignDataSourceToTenant(any(SysTenantDataSource.class));
    }

    @Test
    @DisplayName("测试分配数据源 - 租户ID为空")
    void testAssignDataSourceWithNullTenantId() {
        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            tenantDataSourceManager.assignDataSourceToTenant(null, 1, null);
        });
    }

    @Test
    @DisplayName("测试分配数据源 - 隔离类型无效")
    void testAssignDataSourceWithInvalidIsolationType() {
        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            tenantDataSourceManager.assignDataSourceToTenant(1L, 3, null);
        });
    }

    @Test
    @DisplayName("测试获取租户数据源")
    void testGetTenantDataSource() {
        // 准备测试数据
        Long tenantId = 1L;
        DataSource mockDataSource = mock(DataSource.class);

        when(dataSourceRegistry.findDataSourceByTenant(tenantId)).thenReturn(primaryDataSource);
        when(dataSourceRegistry.getDataSource(primaryDataSource.getDatasourceKey())).thenReturn(mockDataSource);

        // 执行测试
        DataSource result = tenantDataSourceManager.getTenantDataSource(tenantId);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockDataSource, result);

        // 验证方法调用
        verify(dataSourceRegistry, times(1)).findDataSourceByTenant(tenantId);
        verify(dataSourceRegistry, times(1)).getDataSource(primaryDataSource.getDatasourceKey());
    }

    @Test
    @DisplayName("测试获取租户数据源 - 租户未分配数据源")
    void testGetTenantDataSourceWithUnassignedTenant() {
        // 准备测试数据
        Long tenantId = 999L;
        DataSource primaryMockDataSource = mock(DataSource.class);

        when(dataSourceRegistry.findDataSourceByTenant(tenantId)).thenReturn(null);
        when(dataSourceRegistry.getPrimaryDataSource()).thenReturn(primaryMockDataSource);

        // 执行测试
        DataSource result = tenantDataSourceManager.getTenantDataSource(tenantId);

        // 验证结果
        assertNotNull(result);
        assertEquals(primaryMockDataSource, result);

        // 验证方法调用
        verify(dataSourceRegistry, times(1)).findDataSourceByTenant(tenantId);
        verify(dataSourceRegistry, times(1)).getPrimaryDataSource();
    }

    @Test
    @DisplayName("测试获取租户数据源配置")
    void testGetTenantDataSourceConfig() {
        // 准备测试数据
        Long tenantId = 1L;

        when(dataSourceRegistry.findDataSourceByTenant(tenantId)).thenReturn(primaryDataSource);

        // 执行测试
        SysDataSource result = tenantDataSourceManager.getTenantDataSourceConfig(tenantId);

        // 验证结果
        assertNotNull(result);
        assertEquals(primaryDataSource, result);

        // 验证方法调用
        verify(dataSourceRegistry, times(1)).findDataSourceByTenant(tenantId);
    }

    @Test
    @DisplayName("测试移除租户数据源关联")
    void testRemoveTenantFromDataSource() {
        // 准备测试数据
        Long tenantId = 1L;
        Long datasourceId = 1L;

        when(dataSourceService.removeDataSourceFromTenant(tenantId, datasourceId)).thenReturn(true);

        // 执行测试
        boolean result = tenantDataSourceManager.removeTenantFromDataSource(tenantId, datasourceId);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(dataSourceService, times(1)).removeDataSourceFromTenant(tenantId, datasourceId);
    }

    @Test
    @DisplayName("测试刷新缓存")
    void testRefreshCache() {
        // 执行测试（不应该抛出异常）
        assertDoesNotThrow(() -> {
            tenantDataSourceManager.refreshCache();
        });
    }

    @Test
    @DisplayName("测试获取数据源使用统计")
    void testGetDataSourceUsageStatistics() {
        // 执行测试
        Map<String, Integer> statistics = tenantDataSourceManager.getDataSourceUsageStatistics();

        // 验证结果
        assertNotNull(statistics);
        // 初始状态下应该是空的
        assertTrue(statistics.isEmpty());
    }

    @Test
    @DisplayName("测试检查数据源健康状态")
    void testCheckDataSourceHealth() {
        // 准备测试数据
        String dataSourceKey = "test_datasource";
        DataSource mockDataSource = mock(DataSource.class);

        when(dataSourceRegistry.getDataSource(dataSourceKey)).thenReturn(mockDataSource);

        // 执行测试
        boolean result = tenantDataSourceManager.checkDataSourceHealth(dataSourceKey);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(dataSourceRegistry, times(1)).getDataSource(dataSourceKey);
    }

    @Test
    @DisplayName("测试检查数据源健康状态 - 数据源不存在")
    void testCheckDataSourceHealthWithNonExistentDataSource() {
        // 准备测试数据
        String dataSourceKey = "non_existent";

        when(dataSourceRegistry.getDataSource(dataSourceKey)).thenReturn(null);

        // 执行测试
        boolean result = tenantDataSourceManager.checkDataSourceHealth(dataSourceKey);

        // 验证结果
        assertFalse(result);

        // 验证方法调用
        verify(dataSourceRegistry, times(1)).getDataSource(dataSourceKey);
    }

    @Test
    @DisplayName("测试创建专用数据源的URL生成")
    void testGenerateTenantSpecificUrl() {
        // 这个测试需要访问私有方法，可以通过反射或者将方法改为包可见来测试
        // 这里只是验证逻辑结构
        Long tenantId = 123L;
        String postgresqlUrl = "*****************************************************";
        String mysqlUrl = "************************************************";

        // 验证 PostgreSQL URL 生成逻辑
        assertTrue(postgresqlUrl.contains("postgresql"));
        assertTrue(postgresqlUrl.contains("test_db"));

        // 验证 MySQL URL 生成逻辑
        assertTrue(mysqlUrl.contains("mysql"));
        assertTrue(mysqlUrl.contains("test_db"));
    }

    // =====================================================
    // 辅助方法
    // =====================================================

    private SysDataSource createPrimaryDataSource() {
        SysDataSource dataSource = new SysDataSource();
        dataSource.setId(1L);
        dataSource.setDatasourceName("主数据源");
        dataSource.setDatasourceKey("primary");
        dataSource.setDatasourceType("postgresql");
        dataSource.setDriverClassName("org.postgresql.Driver");
        dataSource.setUrl("*****************************************");
        dataSource.setUsername("rega_user");
        dataSource.setPassword("password");
        dataSource.setStatus(1);
        dataSource.setIsDefault(true);
        dataSource.setHealthCheckEnabled(true);
        dataSource.setCreateTime(LocalDateTime.now());
        dataSource.setUpdateTime(LocalDateTime.now());
        return dataSource;
    }

    private SysDataSource createDedicatedDataSource() {
        SysDataSource dataSource = new SysDataSource();
        dataSource.setId(2L);
        dataSource.setDatasourceName("专用数据源");
        dataSource.setDatasourceKey("dedicated_001");
        dataSource.setDatasourceType("postgresql");
        dataSource.setDriverClassName("org.postgresql.Driver");
        dataSource.setUrl("***************************************************");
        dataSource.setUsername("rega_user");
        dataSource.setPassword("password");
        dataSource.setStatus(1);
        dataSource.setIsDefault(false);
        dataSource.setHealthCheckEnabled(true);
        dataSource.setCreateTime(LocalDateTime.now());
        dataSource.setUpdateTime(LocalDateTime.now());
        return dataSource;
    }
}
