package com.rega.erp.common.db.tenant.domain;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 租户信息实体
 *
 * <AUTHOR>
 */
@Data
public class TenantInfo {
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 租户名称
     */
    private String tenantName;
    
    /**
     * 租户描述
     */
    private String description;
    
    /**
     * 隔离类型
     */
    private TenantIsolationType isolationType;
    
    /**
     * 数据源ID（数据源隔离时使用）
     */
    private String dataSourceId;
    
    /**
     * 租户状态
     */
    private TenantStatus status;
    
    /**
     * 联系人信息
     */
    private String contactName;
    
    /**
     * 联系邮箱
     */
    private String contactEmail;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 最大用户数限制
     */
    private Integer maxUsers;
    
    /**
     * 最大存储空间限制（MB）
     */
    private Long maxStorage;
    
    /**
     * 其他配置信息
     */
    private Map<String, Object> config;
    
    /**
     * 是否为活跃状态
     */
    public boolean isActive() {
        return TenantStatus.ACTIVE.equals(this.status);
    }
    
    /**
     * 是否已过期
     */
    public boolean isExpired() {
        return expireTime != null && LocalDateTime.now().isAfter(expireTime);
    }
    
    /**
     * 是否使用数据源隔离
     */
    public boolean isDataSourceIsolation() {
        return TenantIsolationType.DATASOURCE_ISOLATION.equals(this.isolationType);
    }
    
    /**
     * 是否使用字段隔离
     */
    public boolean isFieldIsolation() {
        return TenantIsolationType.FIELD_ISOLATION.equals(this.isolationType);
    }
}
