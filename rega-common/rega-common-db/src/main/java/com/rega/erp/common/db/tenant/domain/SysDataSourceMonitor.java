package com.rega.erp.common.db.tenant.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 数据源监控实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
public class SysDataSourceMonitor {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 数据源ID
     */
    private Long datasourceId;

    /**
     * 检查时间
     */
    private LocalDateTime checkTime;

    /**
     * 状态：1-正常，0-异常
     */
    private Integer status;

    /**
     * 响应时间（毫秒）
     */
    private Long responseTime;

    /**
     * 活跃连接数
     */
    private Integer activeConnections;

    /**
     * 空闲连接数
     */
    private Integer idleConnections;

    /**
     * 总连接数
     */
    private Integer totalConnections;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * CPU使用率
     */
    private BigDecimal cpuUsage;

    /**
     * 内存使用率
     */
    private BigDecimal memoryUsage;

    /**
     * 磁盘使用率
     */
    private BigDecimal diskUsage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 检查状态是否正常
     */
    public boolean isHealthy() {
        return status != null && status == 1;
    }

    /**
     * 检查响应时间是否正常（小于1秒）
     */
    public boolean isResponseTimeNormal() {
        return responseTime != null && responseTime < 1000;
    }

    /**
     * 检查连接池是否健康
     */
    public boolean isConnectionPoolHealthy() {
        if (totalConnections == null || totalConnections <= 0) {
            return false;
        }
        
        // 活跃连接数不应超过总连接数的80%
        if (activeConnections != null) {
            double activeRatio = (double) activeConnections / totalConnections;
            if (activeRatio > 0.8) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取连接池使用率
     */
    public double getConnectionPoolUsage() {
        if (totalConnections == null || totalConnections <= 0 || activeConnections == null) {
            return 0.0;
        }
        return (double) activeConnections / totalConnections * 100;
    }

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) {
            return "未知";
        }
        return status == 1 ? "正常" : "异常";
    }

    /**
     * 获取响应时间描述
     */
    public String getResponseTimeDesc() {
        if (responseTime == null) {
            return "未知";
        }
        
        if (responseTime < 100) {
            return responseTime + "ms (优秀)";
        } else if (responseTime < 500) {
            return responseTime + "ms (良好)";
        } else if (responseTime < 1000) {
            return responseTime + "ms (一般)";
        } else {
            return responseTime + "ms (较慢)";
        }
    }

    /**
     * 获取连接池状态摘要
     */
    public String getConnectionPoolSummary() {
        if (totalConnections == null) {
            return "连接池信息不可用";
        }
        
        return String.format("总连接:%d, 活跃:%d, 空闲:%d, 使用率:%.1f%%",
                totalConnections,
                activeConnections != null ? activeConnections : 0,
                idleConnections != null ? idleConnections : 0,
                getConnectionPoolUsage());
    }

    /**
     * 获取资源使用摘要
     */
    public String getResourceUsageSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (cpuUsage != null) {
            summary.append("CPU:").append(cpuUsage).append("%");
        }
        
        if (memoryUsage != null) {
            if (summary.length() > 0) summary.append(", ");
            summary.append("内存:").append(memoryUsage).append("%");
        }
        
        if (diskUsage != null) {
            if (summary.length() > 0) summary.append(", ");
            summary.append("磁盘:").append(diskUsage).append("%");
        }
        
        return summary.length() > 0 ? summary.toString() : "资源使用信息不可用";
    }

    /**
     * 检查是否需要告警
     */
    public boolean needsAlert() {
        // 状态异常
        if (!isHealthy()) {
            return true;
        }
        
        // 响应时间过长
        if (responseTime != null && responseTime > 5000) {
            return true;
        }
        
        // 连接池使用率过高
        if (getConnectionPoolUsage() > 90) {
            return true;
        }
        
        // CPU使用率过高
        if (cpuUsage != null && cpuUsage.compareTo(new BigDecimal("90")) > 0) {
            return true;
        }
        
        // 内存使用率过高
        if (memoryUsage != null && memoryUsage.compareTo(new BigDecimal("90")) > 0) {
            return true;
        }
        
        // 磁盘使用率过高
        if (diskUsage != null && diskUsage.compareTo(new BigDecimal("95")) > 0) {
            return true;
        }
        
        return false;
    }

    /**
     * 获取告警级别
     */
    public String getAlertLevel() {
        if (!needsAlert()) {
            return "正常";
        }
        
        // 严重告警条件
        if (!isHealthy() || 
            (responseTime != null && responseTime > 10000) ||
            getConnectionPoolUsage() > 95 ||
            (cpuUsage != null && cpuUsage.compareTo(new BigDecimal("95")) > 0) ||
            (memoryUsage != null && memoryUsage.compareTo(new BigDecimal("95")) > 0) ||
            (diskUsage != null && diskUsage.compareTo(new BigDecimal("98")) > 0)) {
            return "严重";
        }
        
        return "警告";
    }
}
