package com.rega.erp.common.db.tenant.service;

import com.rega.erp.common.core.model.PageResult;
import com.rega.erp.common.db.tenant.domain.SysDataSource;
import com.rega.erp.common.db.tenant.domain.SysDataSourceMonitor;
import com.rega.erp.common.db.tenant.domain.SysTenantDataSource;

import javax.sql.DataSource;
import java.util.List;

/**
 * 数据源服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface DataSourceService {

    // =====================================================
    // 数据源配置管理
    // =====================================================

    /**
     * 创建数据源配置
     *
     * @param sysDataSource 数据源配置
     * @return 数据源ID
     */
    Long createDataSource(SysDataSource sysDataSource);

    /**
     * 更新数据源配置
     *
     * @param sysDataSource 数据源配置
     * @return 是否更新成功
     */
    boolean updateDataSource(SysDataSource sysDataSource);

    /**
     * 删除数据源配置
     *
     * @param datasourceId 数据源ID
     * @return 是否删除成功
     */
    boolean deleteDataSource(Long datasourceId);

    /**
     * 根据ID查询数据源配置
     *
     * @param datasourceId 数据源ID
     * @return 数据源配置
     */
    SysDataSource getDataSourceById(Long datasourceId);

    /**
     * 根据数据源键查询数据源配置
     *
     * @param datasourceKey 数据源键
     * @return 数据源配置
     */
    SysDataSource getDataSourceByKey(String datasourceKey);

    /**
     * 查询所有数据源配置
     *
     * @return 数据源配置列表
     */
    List<SysDataSource> getAllDataSources();

    /**
     * 分页查询数据源配置
     *
     * @param datasourceName 数据源名称（模糊查询）
     * @param datasourceType 数据源类型
     * @param status 状态
     * @param current 当前页码
     * @param size 每页大小
     * @return 分页结果
     */
    PageResult<SysDataSource> queryDataSources(String datasourceName, String datasourceType, 
                                               Integer status, long current, long size);

    /**
     * 测试数据源连接
     *
     * @param datasourceId 数据源ID
     * @return 测试结果
     */
    boolean testDataSourceConnection(Long datasourceId);

    /**
     * 测试数据源连接（使用配置）
     *
     * @param sysDataSource 数据源配置
     * @return 测试结果
     */
    boolean testDataSourceConnection(SysDataSource sysDataSource);

    // =====================================================
    // 租户数据源关联管理
    // =====================================================

    /**
     * 为租户分配数据源
     *
     * @param tenantDataSource 租户数据源关联配置
     * @return 是否分配成功
     */
    boolean assignDataSourceToTenant(SysTenantDataSource tenantDataSource);

    /**
     * 移除租户数据源关联
     *
     * @param tenantId 租户ID
     * @param datasourceId 数据源ID
     * @return 是否移除成功
     */
    boolean removeDataSourceFromTenant(Long tenantId, Long datasourceId);

    /**
     * 查询租户的数据源配置
     *
     * @param tenantId 租户ID
     * @return 租户数据源关联列表
     */
    List<SysTenantDataSource> getTenantDataSources(Long tenantId);

    /**
     * 查询租户的主数据源
     *
     * @param tenantId 租户ID
     * @return 主数据源配置
     */
    SysTenantDataSource getTenantPrimaryDataSource(Long tenantId);

    /**
     * 更新租户数据源配置
     *
     * @param tenantDataSource 租户数据源关联配置
     * @return 是否更新成功
     */
    boolean updateTenantDataSource(SysTenantDataSource tenantDataSource);

    // =====================================================
    // 数据源实例管理
    // =====================================================

    /**
     * 获取数据源实例
     *
     * @param datasourceId 数据源ID
     * @return 数据源实例
     */
    DataSource getDataSourceInstance(Long datasourceId);

    /**
     * 获取数据源实例（根据键）
     *
     * @param datasourceKey 数据源键
     * @return 数据源实例
     */
    DataSource getDataSourceInstance(String datasourceKey);

    /**
     * 创建数据源实例
     *
     * @param sysDataSource 数据源配置
     * @return 数据源实例
     */
    DataSource createDataSourceInstance(SysDataSource sysDataSource);

    /**
     * 销毁数据源实例
     *
     * @param datasourceId 数据源ID
     */
    void destroyDataSourceInstance(Long datasourceId);

    /**
     * 重新加载数据源实例
     *
     * @param datasourceId 数据源ID
     * @return 新的数据源实例
     */
    DataSource reloadDataSourceInstance(Long datasourceId);

    // =====================================================
    // 数据源监控管理
    // =====================================================

    /**
     * 记录数据源监控信息
     *
     * @param monitor 监控信息
     */
    void recordDataSourceMonitor(SysDataSourceMonitor monitor);

    /**
     * 查询数据源监控历史
     *
     * @param datasourceId 数据源ID
     * @param hours 查询最近几小时的数据
     * @return 监控历史列表
     */
    List<SysDataSourceMonitor> getDataSourceMonitorHistory(Long datasourceId, int hours);

    /**
     * 查询数据源最新监控信息
     *
     * @param datasourceId 数据源ID
     * @return 最新监控信息
     */
    SysDataSourceMonitor getLatestDataSourceMonitor(Long datasourceId);

    /**
     * 执行数据源健康检查
     *
     * @param datasourceId 数据源ID
     * @return 健康检查结果
     */
    SysDataSourceMonitor performHealthCheck(Long datasourceId);

    /**
     * 执行所有数据源健康检查
     *
     * @return 健康检查结果列表
     */
    List<SysDataSourceMonitor> performAllHealthChecks();

    // =====================================================
    // 数据源统计信息
    // =====================================================

    /**
     * 获取数据源统计信息
     *
     * @return 统计信息
     */
    DataSourceStatistics getDataSourceStatistics();

    /**
     * 数据源统计信息
     */
    class DataSourceStatistics {
        private long totalDataSources;
        private long activeDataSources;
        private long inactiveDataSources;
        private long totalTenants;
        private long fieldIsolationTenants;
        private long dataSourceIsolationTenants;
        private long healthyDataSources;
        private long unhealthyDataSources;

        // Getters and Setters
        public long getTotalDataSources() { return totalDataSources; }
        public void setTotalDataSources(long totalDataSources) { this.totalDataSources = totalDataSources; }

        public long getActiveDataSources() { return activeDataSources; }
        public void setActiveDataSources(long activeDataSources) { this.activeDataSources = activeDataSources; }

        public long getInactiveDataSources() { return inactiveDataSources; }
        public void setInactiveDataSources(long inactiveDataSources) { this.inactiveDataSources = inactiveDataSources; }

        public long getTotalTenants() { return totalTenants; }
        public void setTotalTenants(long totalTenants) { this.totalTenants = totalTenants; }

        public long getFieldIsolationTenants() { return fieldIsolationTenants; }
        public void setFieldIsolationTenants(long fieldIsolationTenants) { this.fieldIsolationTenants = fieldIsolationTenants; }

        public long getDataSourceIsolationTenants() { return dataSourceIsolationTenants; }
        public void setDataSourceIsolationTenants(long dataSourceIsolationTenants) { this.dataSourceIsolationTenants = dataSourceIsolationTenants; }

        public long getHealthyDataSources() { return healthyDataSources; }
        public void setHealthyDataSources(long healthyDataSources) { this.healthyDataSources = healthyDataSources; }

        public long getUnhealthyDataSources() { return unhealthyDataSources; }
        public void setUnhealthyDataSources(long unhealthyDataSources) { this.unhealthyDataSources = unhealthyDataSources; }
    }
}
