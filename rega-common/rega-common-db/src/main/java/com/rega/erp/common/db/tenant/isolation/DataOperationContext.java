package com.rega.erp.common.db.tenant.isolation;

import lombok.Data;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据操作上下文
 * 用于在数据操作过程中传递租户相关信息
 *
 * <AUTHOR>
 */
@Data
public class DataOperationContext {
    
    /**
     * 数据源
     */
    private DataSource dataSource;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 操作类型
     */
    private OperationType operationType;
    
    /**
     * 表名
     */
    private String tableName;
    
    /**
     * 数据字段
     */
    private Map<String, Object> data = new HashMap<>();
    
    /**
     * 条件字段
     */
    private Map<String, Object> conditions = new HashMap<>();
    
    /**
     * 批量数据
     */
    private List<Map<String, Object>> batchData;
    
    /**
     * 是否批量操作
     */
    private boolean batchOperation = false;
    
    /**
     * 事务信息
     */
    private TransactionInfo transactionInfo;
    
    /**
     * 操作选项
     */
    private Map<String, Object> options = new HashMap<>();
    
    /**
     * 审计信息
     */
    private AuditInfo auditInfo;
    
    /**
     * 设置字段值
     */
    public void setFieldValue(String field, Object value) {
        data.put(field, value);
    }
    
    /**
     * 设置租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        // 自动设置到数据字段中
        setFieldValue("tenant_id", tenantId);
    }
    
    /**
     * 添加条件
     */
    public void addCondition(String field, Object value) {
        conditions.put(field, value);
    }
    
    /**
     * 添加选项
     */
    public void addOption(String key, Object value) {
        options.put(key, value);
    }
    
    /**
     * 获取字段值
     */
    public Object getFieldValue(String field) {
        return data.get(field);
    }
    
    /**
     * 是否包含字段
     */
    public boolean hasField(String field) {
        return data.containsKey(field);
    }
    
    /**
     * 是否有条件
     */
    public boolean hasConditions() {
        return conditions != null && !conditions.isEmpty();
    }
    
    /**
     * 是否有审计信息
     */
    public boolean hasAuditInfo() {
        return auditInfo != null;
    }
    
    /**
     * 是否在事务中
     */
    public boolean isInTransaction() {
        return transactionInfo != null && transactionInfo.isActive();
    }
    
    /**
     * 操作类型枚举
     */
    public enum OperationType {
        INSERT("INSERT", "插入"),
        UPDATE("UPDATE", "更新"),
        DELETE("DELETE", "删除"),
        BATCH_INSERT("BATCH_INSERT", "批量插入"),
        BATCH_UPDATE("BATCH_UPDATE", "批量更新"),
        BATCH_DELETE("BATCH_DELETE", "批量删除");
        
        private final String code;
        private final String name;
        
        OperationType(String code, String name) {
            this.code = code;
            this.name = name;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getName() {
            return name;
        }
        
        public boolean isBatch() {
            return this == BATCH_INSERT || this == BATCH_UPDATE || this == BATCH_DELETE;
        }
    }
    
    /**
     * 事务信息
     */
    @Data
    public static class TransactionInfo {
        /**
         * 事务ID
         */
        private String transactionId;
        
        /**
         * 是否活跃
         */
        private boolean active = false;
        
        /**
         * 隔离级别
         */
        private IsolationLevel isolationLevel = IsolationLevel.READ_COMMITTED;
        
        /**
         * 是否只读
         */
        private boolean readOnly = false;
        
        /**
         * 超时时间（秒）
         */
        private int timeout = 30;
        
        /**
         * 传播行为
         */
        private PropagationBehavior propagationBehavior = PropagationBehavior.REQUIRED;
    }
    
    /**
     * 隔离级别
     */
    public enum IsolationLevel {
        READ_UNCOMMITTED(1),
        READ_COMMITTED(2),
        REPEATABLE_READ(4),
        SERIALIZABLE(8);
        
        private final int value;
        
        IsolationLevel(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
    }
    
    /**
     * 传播行为
     */
    public enum PropagationBehavior {
        REQUIRED,
        REQUIRES_NEW,
        SUPPORTS,
        NOT_SUPPORTED,
        MANDATORY,
        NEVER,
        NESTED
    }
    
    /**
     * 审计信息
     */
    @Data
    public static class AuditInfo {
        /**
         * 操作用户ID
         */
        private String userId;
        
        /**
         * 操作用户名
         */
        private String username;
        
        /**
         * 操作时间
         */
        private java.time.LocalDateTime operationTime;
        
        /**
         * 操作IP
         */
        private String clientIp;
        
        /**
         * 操作描述
         */
        private String description;
        
        /**
         * 扩展信息
         */
        private Map<String, Object> extra = new HashMap<>();
    }
}
