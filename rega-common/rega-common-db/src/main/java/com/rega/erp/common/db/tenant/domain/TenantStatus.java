package com.rega.erp.common.db.tenant.domain;

/**
 * 租户状态枚举
 *
 * <AUTHOR>
 */
public enum TenantStatus {
    
    /**
     * 活跃状态 - 正常使用
     */
    ACTIVE("active", "活跃", "租户正常使用中"),
    
    /**
     * 暂停状态 - 临时停用
     */
    SUSPENDED("suspended", "暂停", "租户被临时暂停使用"),
    
    /**
     * 禁用状态 - 被管理员禁用
     */
    DISABLED("disabled", "禁用", "租户被管理员禁用"),
    
    /**
     * 过期状态 - 超过有效期
     */
    EXPIRED("expired", "过期", "租户已过期"),
    
    /**
     * 删除状态 - 标记删除
     */
    DELETED("deleted", "已删除", "租户已被删除");
    
    /**
     * 状态代码
     */
    private final String code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;
    
    TenantStatus(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取状态
     */
    public static TenantStatus fromCode(String code) {
        for (TenantStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown tenant status code: " + code);
    }
    
    /**
     * 是否为可用状态
     */
    public boolean isAvailable() {
        return this == ACTIVE;
    }
    
    /**
     * 是否为不可用状态
     */
    public boolean isUnavailable() {
        return this == SUSPENDED || this == DISABLED || this == EXPIRED || this == DELETED;
    }
}
