package com.rega.erp.common.db.tenant.domain;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 租户创建请求类
 *
 * <AUTHOR>
 */
@Data
@Builder
public class TenantCreationRequest {
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 租户名称
     */
    private String tenantName;
    
    /**
     * 租户描述
     */
    private String description;
    
    /**
     * 隔离类型
     */
    private TenantIsolationType isolationType;
    
    /**
     * 指定的数据源ID（可选，用于数据源隔离）
     */
    private String dataSourceId;
    
    /**
     * 数据源配置（用于创建新的专用数据源）
     */
    private DataSourceConfig dataSourceConfig;
    
    /**
     * 每个数据源最大租户数（用于数据源隔离）
     */
    private Integer maxTenantsPerDataSource;
    
    /**
     * 联系人信息
     */
    private String contactName;
    
    /**
     * 联系邮箱
     */
    private String contactEmail;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 最大用户数限制
     */
    private Integer maxUsers;
    
    /**
     * 最大存储空间限制（MB）
     */
    private Long maxStorage;
    
    /**
     * 是否自动初始化数据库结构
     */
    private Boolean autoInitSchema;
    
    /**
     * 是否自动创建数据库
     */
    private Boolean autoCreateDatabase;
    
    /**
     * 其他配置信息
     */
    private Map<String, Object> config;
    
    /**
     * 验证请求是否有效
     */
    public boolean isValid() {
        if (tenantId == null || tenantId.trim().isEmpty()) {
            return false;
        }
        
        if (tenantName == null || tenantName.trim().isEmpty()) {
            return false;
        }
        
        if (isolationType == null) {
            return false;
        }
        
        // 如果是数据源隔离且指定了数据源配置，验证配置是否有效
        if (isolationType == TenantIsolationType.DATASOURCE_ISOLATION && 
            dataSourceConfig != null && !dataSourceConfig.isValid()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 是否需要创建新的数据源
     */
    public boolean needsNewDataSource() {
        return isolationType == TenantIsolationType.DATASOURCE_ISOLATION && 
               dataSourceId == null && 
               dataSourceConfig != null;
    }
    
    /**
     * 是否指定了现有数据源
     */
    public boolean hasSpecifiedDataSource() {
        return isolationType == TenantIsolationType.DATASOURCE_ISOLATION && 
               dataSourceId != null;
    }
    
    /**
     * 获取默认的每数据源最大租户数
     */
    public int getMaxTenantsPerDataSourceOrDefault() {
        return maxTenantsPerDataSource != null ? maxTenantsPerDataSource : 10;
    }
}
