package com.rega.erp.common.db.tenant.core;

import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.util.StringUtils;
import com.rega.erp.common.db.tenant.domain.TenantInfo;
import com.rega.erp.common.db.tenant.domain.TenantStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 租户注册表
 * 负责租户信息的注册、查询、更新和删除
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TenantRegistry {
    
    /**
     * 租户信息缓存
     * Key: tenantId, Value: TenantInfo
     */
    private final Map<String, TenantInfo> tenantCache = new ConcurrentHashMap<>();
    
    /**
     * 注册租户
     */
    public void registerTenant(TenantInfo tenantInfo) {
        if (tenantInfo == null) {
            throw new BusinessException("tenant.info.null");
        }
        
        if (StringUtils.isBlank(tenantInfo.getTenantId())) {
            throw new BusinessException("tenant.id.required");
        }
        
        // 检查租户是否已存在
        if (tenantCache.containsKey(tenantInfo.getTenantId())) {
            throw new BusinessException("业务异常");
        }
        
        // 设置创建时间
        if (tenantInfo.getCreateTime() == null) {
            tenantInfo.setCreateTime(LocalDateTime.now());
        }
        
        // 设置更新时间
        tenantInfo.setUpdateTime(LocalDateTime.now());
        
        // 设置默认状态
        if (tenantInfo.getStatus() == null) {
            tenantInfo.setStatus(TenantStatus.ACTIVE);
        }
        
        // 注册到缓存
        tenantCache.put(tenantInfo.getTenantId(), tenantInfo);
        
        log.info("租户注册成功: tenantId={}, tenantName={}, isolationType={}", 
                tenantInfo.getTenantId(), tenantInfo.getTenantName(), tenantInfo.getIsolationType());
    }
    
    /**
     * 获取租户信息
     */
    public TenantInfo getTenantInfo(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return null;
        }
        
        TenantInfo tenantInfo = tenantCache.get(tenantId);
        
        // 检查租户是否过期
        if (tenantInfo != null && tenantInfo.isExpired()) {
            // 自动更新状态为过期
            tenantInfo.setStatus(TenantStatus.EXPIRED);
            tenantInfo.setUpdateTime(LocalDateTime.now());
            log.warn("租户已过期: tenantId={}, expireTime={}", tenantId, tenantInfo.getExpireTime());
        }
        
        return tenantInfo;
    }
    
    /**
     * 更新租户信息
     */
    public void updateTenant(TenantInfo tenantInfo) {
        if (tenantInfo == null || StringUtils.isBlank(tenantInfo.getTenantId())) {
            throw new BusinessException("tenant.info.invalid");
        }
        
        String tenantId = tenantInfo.getTenantId();
        TenantInfo existingTenant = tenantCache.get(tenantId);
        
        if (existingTenant == null) {
            throw new BusinessException("业务异常");
        }
        
        // 更新时间
        tenantInfo.setUpdateTime(LocalDateTime.now());
        
        // 保留创建时间
        if (tenantInfo.getCreateTime() == null) {
            tenantInfo.setCreateTime(existingTenant.getCreateTime());
        }
        
        // 更新缓存
        tenantCache.put(tenantId, tenantInfo);
        
        log.info("租户信息更新成功: tenantId={}", tenantId);
    }
    
    /**
     * 删除租户
     */
    public void deleteTenant(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            throw new BusinessException("tenant.id.required");
        }
        
        TenantInfo tenantInfo = tenantCache.get(tenantId);
        if (tenantInfo == null) {
            throw new BusinessException("业务异常");
        }
        
        // 标记为删除状态而不是直接删除
        tenantInfo.setStatus(TenantStatus.DELETED);
        tenantInfo.setUpdateTime(LocalDateTime.now());
        
        log.info("租户标记删除成功: tenantId={}", tenantId);
    }
    
    /**
     * 物理删除租户
     */
    public void removeTenant(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            throw new BusinessException("tenant.id.required");
        }
        
        TenantInfo removed = tenantCache.remove(tenantId);
        if (removed != null) {
            log.info("租户物理删除成功: tenantId={}", tenantId);
        }
    }
    
    /**
     * 检查租户是否存在
     */
    public boolean existsTenant(String tenantId) {
        return StringUtils.isNotBlank(tenantId) && tenantCache.containsKey(tenantId);
    }
    
    /**
     * 检查租户是否可用
     */
    public boolean isTenantAvailable(String tenantId) {
        TenantInfo tenantInfo = getTenantInfo(tenantId);
        return tenantInfo != null && tenantInfo.isActive() && !tenantInfo.isExpired();
    }
    
    /**
     * 获取所有租户信息
     */
    public Collection<TenantInfo> getAllTenants() {
        return tenantCache.values();
    }
    
    /**
     * 获取活跃租户数量
     */
    public long getActiveTenantCount() {
        return tenantCache.values().stream()
                .filter(tenant -> tenant.isActive() && !tenant.isExpired())
                .count();
    }
    
    /**
     * 根据数据源ID获取租户列表
     */
    public Collection<TenantInfo> getTenantsByDataSource(String dataSourceId) {
        if (StringUtils.isBlank(dataSourceId)) {
            return java.util.Collections.emptyList();
        }
        
        return tenantCache.values().stream()
                .filter(tenant -> dataSourceId.equals(tenant.getDataSourceId()))
                .toList();
    }
    
    /**
     * 清空所有租户信息（谨慎使用）
     */
    public void clearAll() {
        tenantCache.clear();
        log.warn("所有租户信息已清空");
    }
}
