package com.rega.erp.common.db.tenant.domain;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据源信息实体
 *
 * <AUTHOR>
 */
@Data
public class DataSourceInfo {
    
    /**
     * 数据源ID
     */
    private String dataSourceId;
    
    /**
     * 数据源名称
     */
    private String dataSourceName;
    
    /**
     * 数据源描述
     */
    private String description;
    
    /**
     * 数据库连接配置
     */
    private DataSourceConfig config;
    
    /**
     * 使用此数据源的租户列表
     */
    private Set<String> tenantIds = ConcurrentHashMap.newKeySet();
    
    /**
     * 数据源类型
     */
    private DataSourceType type;
    
    /**
     * 数据源状态
     */
    private DataSourceStatus status;
    
    /**
     * 最大租户数量限制
     */
    private Integer maxTenants;
    
    /**
     * 当前租户数量
     */
    private Integer currentTenants;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 最后健康检查时间
     */
    private LocalDateTime lastHealthCheckTime;
    
    /**
     * 连接池配置
     */
    private PoolConfig poolConfig;
    
    /**
     * 是否为主数据源
     */
    public boolean isPrimary() {
        return DataSourceType.PRIMARY.equals(this.type);
    }
    
    /**
     * 是否为专用数据源
     */
    public boolean isDedicated() {
        return DataSourceType.DEDICATED.equals(this.type);
    }
    
    /**
     * 是否可以添加更多租户
     */
    public boolean canAddMoreTenants() {
        return maxTenants == null || currentTenants < maxTenants;
    }
    
    /**
     * 是否为活跃状态
     */
    public boolean isActive() {
        return DataSourceStatus.ACTIVE.equals(this.status);
    }
    
    /**
     * 添加租户
     */
    public boolean addTenant(String tenantId) {
        if (canAddMoreTenants()) {
            boolean added = tenantIds.add(tenantId);
            if (added) {
                currentTenants = tenantIds.size();
            }
            return added;
        }
        return false;
    }
    
    /**
     * 移除租户
     */
    public boolean removeTenant(String tenantId) {
        boolean removed = tenantIds.remove(tenantId);
        if (removed) {
            currentTenants = tenantIds.size();
        }
        return removed;
    }
    
    /**
     * 获取租户数量
     */
    public int getTenantCount() {
        return tenantIds.size();
    }
}
