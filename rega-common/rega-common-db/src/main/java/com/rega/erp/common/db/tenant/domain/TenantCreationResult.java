package com.rega.erp.common.db.tenant.domain;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 租户创建结果类
 *
 * <AUTHOR>
 */
@Data
public class TenantCreationResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 租户信息
     */
    private TenantInfo tenantInfo;
    
    /**
     * 分配的数据源ID
     */
    private String assignedDataSourceId;
    
    /**
     * 是否创建了新的数据源
     */
    private boolean newDataSourceCreated;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 警告消息列表
     */
    private List<String> warnings = new ArrayList<>();
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 处理耗时（毫秒）
     */
    private Long processingTime;
    
    /**
     * 创建成功的结果
     */
    public static TenantCreationResult success(TenantInfo tenantInfo) {
        TenantCreationResult result = new TenantCreationResult();
        result.setSuccess(true);
        result.setTenantInfo(tenantInfo);
        result.setAssignedDataSourceId(tenantInfo.getDataSourceId());
        result.setCreateTime(LocalDateTime.now());
        return result;
    }
    
    /**
     * 创建成功的结果（包含数据源信息）
     */
    public static TenantCreationResult success(TenantInfo tenantInfo, String dataSourceId, boolean newDataSourceCreated) {
        TenantCreationResult result = success(tenantInfo);
        result.setAssignedDataSourceId(dataSourceId);
        result.setNewDataSourceCreated(newDataSourceCreated);
        return result;
    }
    
    /**
     * 创建失败的结果
     */
    public static TenantCreationResult failure(String errorCode, String errorMessage) {
        TenantCreationResult result = new TenantCreationResult();
        result.setSuccess(false);
        result.setErrorCode(errorCode);
        result.setErrorMessage(errorMessage);
        result.setCreateTime(LocalDateTime.now());
        return result;
    }
    
    /**
     * 创建失败的结果（仅错误消息）
     */
    public static TenantCreationResult failure(String errorMessage) {
        return failure("TENANT_CREATION_FAILED", errorMessage);
    }
    
    /**
     * 添加警告消息
     */
    public void addWarning(String warning) {
        if (warnings == null) {
            warnings = new ArrayList<>();
        }
        warnings.add(warning);
    }
    
    /**
     * 是否有警告
     */
    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }
    
    /**
     * 设置处理耗时
     */
    public void setProcessingTime(long startTime) {
        this.processingTime = System.currentTimeMillis() - startTime;
    }
}
