package com.rega.erp.common.db.tenant.domain;

import com.rega.erp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户数据源关联实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysTenantDataSource extends BaseEntity {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 数据源ID
     */
    private Long datasourceId;

    /**
     * 隔离类型：1-字段隔离，2-数据源隔离
     */
    private Integer isolationType;

    /**
     * 是否主数据源
     */
    private Boolean isPrimary;

    /**
     * 优先级，数字越小优先级越高
     */
    private Integer priority;

    /**
     * 读权重
     */
    private Integer readWeight;

    /**
     * 写权重
     */
    private Integer writeWeight;

    /**
     * 检查是否为主数据源
     */
    public boolean isPrimaryDataSource() {
        return Boolean.TRUE.equals(isPrimary);
    }

    /**
     * 检查是否为字段隔离
     */
    public boolean isFieldIsolation() {
        return isolationType != null && isolationType == 1;
    }

    /**
     * 检查是否为数据源隔离
     */
    public boolean isDataSourceIsolation() {
        return isolationType != null && isolationType == 2;
    }

    /**
     * 获取隔离类型描述
     */
    public String getIsolationTypeDesc() {
        if (isolationType == null) {
            return "未知";
        }
        return switch (isolationType) {
            case 1 -> "字段隔离";
            case 2 -> "数据源隔离";
            default -> "未知类型";
        };
    }

    /**
     * 获取优先级描述
     */
    public String getPriorityDesc() {
        if (priority == null) {
            return "默认";
        }
        return switch (priority) {
            case 0 -> "最高";
            case 1 -> "高";
            case 2 -> "中";
            case 3 -> "低";
            default -> "自定义(" + priority + ")";
        };
    }

    /**
     * 获取权重配置摘要
     */
    public String getWeightSummary() {
        return String.format("读权重:%d, 写权重:%d",
                readWeight != null ? readWeight : 1,
                writeWeight != null ? writeWeight : 1);
    }

    /**
     * 检查配置是否有效
     */
    public boolean isValidConfig() {
        return tenantId != null && tenantId > 0
                && datasourceId != null && datasourceId > 0
                && isolationType != null && (isolationType == 1 || isolationType == 2)
                && priority != null && priority >= 0
                && readWeight != null && readWeight > 0
                && writeWeight != null && writeWeight > 0;
    }

    /**
     * 创建字段隔离配置
     */
    public static SysTenantDataSource createFieldIsolation(Long tenantId, Long datasourceId, boolean isPrimary) {
        SysTenantDataSource config = new SysTenantDataSource();
        config.setTenantId(tenantId);
        config.setDatasourceId(datasourceId);
        config.setIsolationType(1);
        config.setIsPrimary(isPrimary);
        config.setPriority(isPrimary ? 0 : 1);
        config.setReadWeight(1);
        config.setWriteWeight(1);
        config.setStatus(1);
        return config;
    }

    /**
     * 创建数据源隔离配置
     */
    public static SysTenantDataSource createDataSourceIsolation(Long tenantId, Long datasourceId, boolean isPrimary) {
        SysTenantDataSource config = new SysTenantDataSource();
        config.setTenantId(tenantId);
        config.setDatasourceId(datasourceId);
        config.setIsolationType(2);
        config.setIsPrimary(isPrimary);
        config.setPriority(isPrimary ? 0 : 1);
        config.setReadWeight(1);
        config.setWriteWeight(1);
        config.setStatus(1);
        return config;
    }
}
