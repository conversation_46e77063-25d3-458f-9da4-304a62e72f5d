package com.rega.erp.common.db.tenant.datasource;

import com.rega.erp.common.core.context.TenantContextHolder;
import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.util.StringUtils;
import com.rega.erp.common.db.tenant.core.TenantRegistry;
import com.rega.erp.common.db.tenant.domain.DataSourceInfo;
import com.rega.erp.common.db.tenant.domain.TenantInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

/**
 * 数据源路由器
 * 负责根据租户信息路由到正确的数据源
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataSourceRouter {
    
    private final DataSourceRegistry dataSourceRegistry;
    private final TenantRegistry tenantRegistry;
    
    /**
     * 获取当前租户的数据源
     */
    public DataSource getCurrentDataSource() {
        String tenantId = TenantContextHolder.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            // 没有租户上下文，返回主数据源
            return getPrimaryDataSource();
        }
        
        return getTenantDataSource(tenantId);
    }
    
    /**
     * 获取指定租户的数据源
     */
    public DataSource getTenantDataSource(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            throw new BusinessException("tenant.id.required");
        }
        
        TenantInfo tenantInfo = tenantRegistry.getTenantInfo(tenantId);
        if (tenantInfo == null) {
            throw new BusinessException("租户不存在: " + tenantId);
        }

        if (!tenantInfo.isActive()) {
            throw new BusinessException("租户未激活: " + tenantId);
        }
        
        String dataSourceId = tenantInfo.getDataSourceId();
        DataSource dataSource = getDataSource(dataSourceId);
        
        if (dataSource == null) {
            log.error("租户数据源不存在: tenantId={}, dataSourceId={}", tenantId, dataSourceId);
            throw new BusinessException("数据源不存在: " + dataSourceId);
        }
        
        return dataSource;
    }
    
    /**
     * 根据数据源ID获取数据源
     */
    public DataSource getDataSource(String dataSourceId) {
        if (StringUtils.isBlank(dataSourceId)) {
            return null;
        }
        
        DataSource dataSource = dataSourceRegistry.getDataSource(dataSourceId);
        
        // 验证数据源是否可用
        if (dataSource != null && !isDataSourceAvailable(dataSourceId)) {
            log.warn("数据源不可用: dataSourceId={}", dataSourceId);
            return null;
        }
        
        return dataSource;
    }
    
    /**
     * 获取主数据源
     */
    public DataSource getPrimaryDataSource() {
        DataSource primaryDataSource = dataSourceRegistry.getPrimaryDataSource();
        
        if (primaryDataSource == null) {
            throw new BusinessException("主数据源不存在");
        }
        
        return primaryDataSource;
    }
    
    /**
     * 检查数据源是否可用
     */
    public boolean isDataSourceAvailable(String dataSourceId) {
        if (StringUtils.isBlank(dataSourceId)) {
            return false;
        }
        
        DataSourceInfo dataSourceInfo = dataSourceRegistry.getDataSourceInfo(dataSourceId);
        if (dataSourceInfo == null) {
            return false;
        }
        
        if (!dataSourceInfo.isActive()) {
            return false;
        }
        
        // 可以添加更多的健康检查逻辑
        return testDataSourceConnection(dataSourceId);
    }
    
    /**
     * 测试数据源连接
     */
    private boolean testDataSourceConnection(String dataSourceId) {
        try {
            DataSource dataSource = dataSourceRegistry.getDataSource(dataSourceId);
            if (dataSource == null) {
                return false;
            }
            
            // 简单的连接测试
            try (var connection = dataSource.getConnection()) {
                return connection.isValid(5);
            }
            
        } catch (Exception e) {
            log.warn("数据源连接测试失败: dataSourceId={}", dataSourceId, e);
            return false;
        }
    }
    
    /**
     * 根据租户ID获取数据源ID
     */
    public String getDataSourceId(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return "primary";
        }
        
        TenantInfo tenantInfo = tenantRegistry.getTenantInfo(tenantId);
        if (tenantInfo == null) {
            return "primary";
        }
        
        return tenantInfo.getDataSourceId();
    }
    
    /**
     * 切换到指定数据源
     */
    public <T> T executeWithDataSource(String dataSourceId, DataSourceCallback<T> callback) {
        DataSource dataSource = getDataSource(dataSourceId);
        if (dataSource == null) {
            throw new BusinessException("数据源不存在: " + dataSourceId);
        }
        
        try {
            return callback.execute(dataSource);
        } catch (Exception e) {
            log.error("执行数据源回调失败: dataSourceId={}", dataSourceId, e);
            throw new BusinessException("数据源回调执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 在租户上下文中执行
     */
    public <T> T executeWithTenant(String tenantId, DataSourceCallback<T> callback) {
        DataSource dataSource = getTenantDataSource(tenantId);
        
        try {
            return callback.execute(dataSource);
        } catch (Exception e) {
            log.error("执行租户数据源回调失败: tenantId={}", tenantId, e);
            throw new BusinessException("租户数据源回调执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取数据源统计信息
     */
    public DataSourceStats getDataSourceStats(String dataSourceId) {
        DataSourceInfo dataSourceInfo = dataSourceRegistry.getDataSourceInfo(dataSourceId);
        if (dataSourceInfo == null) {
            return null;
        }
        
        DataSourceStats stats = new DataSourceStats();
        stats.setDataSourceId(dataSourceId);
        stats.setDataSourceName(dataSourceInfo.getDataSourceName());
        stats.setType(dataSourceInfo.getType());
        stats.setStatus(dataSourceInfo.getStatus());
        stats.setTenantCount(dataSourceInfo.getTenantCount());
        stats.setMaxTenants(dataSourceInfo.getMaxTenants());
        stats.setAvailable(isDataSourceAvailable(dataSourceId));
        
        return stats;
    }
    
    /**
     * 数据源回调接口
     */
    @FunctionalInterface
    public interface DataSourceCallback<T> {
        T execute(DataSource dataSource) throws Exception;
    }
    
    /**
     * 数据源统计信息
     */
    public static class DataSourceStats {
        private String dataSourceId;
        private String dataSourceName;
        private com.rega.erp.common.db.tenant.domain.DataSourceType type;
        private com.rega.erp.common.db.tenant.domain.DataSourceStatus status;
        private int tenantCount;
        private Integer maxTenants;
        private boolean available;
        
        // Getters and Setters
        public String getDataSourceId() { return dataSourceId; }
        public void setDataSourceId(String dataSourceId) { this.dataSourceId = dataSourceId; }
        
        public String getDataSourceName() { return dataSourceName; }
        public void setDataSourceName(String dataSourceName) { this.dataSourceName = dataSourceName; }
        
        public com.rega.erp.common.db.tenant.domain.DataSourceType getType() { return type; }
        public void setType(com.rega.erp.common.db.tenant.domain.DataSourceType type) { this.type = type; }
        
        public com.rega.erp.common.db.tenant.domain.DataSourceStatus getStatus() { return status; }
        public void setStatus(com.rega.erp.common.db.tenant.domain.DataSourceStatus status) { this.status = status; }
        
        public int getTenantCount() { return tenantCount; }
        public void setTenantCount(int tenantCount) { this.tenantCount = tenantCount; }
        
        public Integer getMaxTenants() { return maxTenants; }
        public void setMaxTenants(Integer maxTenants) { this.maxTenants = maxTenants; }
        
        public boolean isAvailable() { return available; }
        public void setAvailable(boolean available) { this.available = available; }
        
        public double getUsageRate() {
            if (maxTenants == null || maxTenants == 0) {
                return 0.0;
            }
            return (double) tenantCount / maxTenants;
        }
    }
}
