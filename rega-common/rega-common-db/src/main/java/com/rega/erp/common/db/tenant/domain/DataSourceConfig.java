package com.rega.erp.common.db.tenant.domain;

import lombok.Builder;
import lombok.Data;

/**
 * 数据源配置类
 *
 * <AUTHOR>
 */
@Data
@Builder
public class DataSourceConfig {
    
    /**
     * 数据库驱动类名
     */
    private String driverClassName;
    
    /**
     * 数据库连接URL
     */
    private String url;
    
    /**
     * 数据库用户名
     */
    private String username;
    
    /**
     * 数据库密码
     */
    private String password;
    
    /**
     * 数据库名称
     */
    private String databaseName;
    
    /**
     * 数据库主机
     */
    private String host;
    
    /**
     * 数据库端口
     */
    private Integer port;
    
    /**
     * 连接超时时间（毫秒）
     */
    private Long connectionTimeout;
    
    /**
     * 空闲超时时间（毫秒）
     */
    private Long idleTimeout;
    
    /**
     * 最大生命周期（毫秒）
     */
    private Long maxLifetime;
    
    /**
     * 连接测试查询
     */
    private String connectionTestQuery;
    
    /**
     * 是否自动提交
     */
    private Boolean autoCommit;
    
    /**
     * 是否只读
     */
    private Boolean readOnly;
    
    /**
     * 事务隔离级别
     */
    private String transactionIsolation;
    
    /**
     * 验证配置是否完整
     */
    public boolean isValid() {
        return url != null && !url.trim().isEmpty() &&
               username != null && !username.trim().isEmpty() &&
               password != null;
    }
    
    /**
     * 获取JDBC URL
     */
    public String getJdbcUrl() {
        if (url != null) {
            return url;
        }
        
        if (host != null && port != null && databaseName != null) {
            return String.format("jdbc:postgresql://%s:%d/%s", host, port, databaseName);
        }
        
        return null;
    }
    
    /**
     * 创建默认的PostgreSQL配置
     */
    public static DataSourceConfig createPostgreSQLConfig(String host, int port, String database, String username, String password) {
        return DataSourceConfig.builder()
                .driverClassName("org.postgresql.Driver")
                .host(host)
                .port(port)
                .databaseName(database)
                .username(username)
                .password(password)
                .connectionTimeout(30000L)
                .idleTimeout(600000L)
                .maxLifetime(1800000L)
                .connectionTestQuery("SELECT 1")
                .autoCommit(true)
                .readOnly(false)
                .build();
    }
}
