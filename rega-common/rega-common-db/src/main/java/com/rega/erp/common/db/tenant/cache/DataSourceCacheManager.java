package com.rega.erp.common.db.tenant.cache;

import com.rega.erp.common.cache.core.CacheService;
import com.rega.erp.common.cache.util.CacheUtils;
import com.rega.erp.common.core.util.StringUtils;
import com.rega.erp.common.db.tenant.domain.SysDataSource;
import com.rega.erp.common.db.tenant.domain.SysTenantDataSource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 数据源缓存管理器
 * 基于 common-cache 模块提供数据源相关的缓存功能
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataSourceCacheManager {

    private final CacheService cacheService;

    // =====================================================
    // 缓存键常量定义
    // =====================================================

    private static final String DATASOURCE_PREFIX = "datasource";
    private static final String TENANT_DATASOURCE_PREFIX = "tenant_datasource";
    private static final String DATASOURCE_CONFIG_PREFIX = "datasource_config";
    private static final String DATASOURCE_USAGE_PREFIX = "datasource_usage";
    private static final String DATASOURCE_HEALTH_PREFIX = "datasource_health";

    // 缓存过期时间配置
    private static final Duration DATASOURCE_CONFIG_TTL = Duration.ofHours(1);      // 数据源配置缓存1小时
    private static final Duration TENANT_DATASOURCE_TTL = Duration.ofMinutes(30);   // 租户数据源关联缓存30分钟
    private static final Duration DATASOURCE_USAGE_TTL = Duration.ofMinutes(10);    // 数据源使用统计缓存10分钟
    private static final Duration DATASOURCE_HEALTH_TTL = Duration.ofMinutes(5);    // 数据源健康状态缓存5分钟

    // =====================================================
    // 数据源配置缓存
    // =====================================================

    /**
     * 缓存数据源配置
     *
     * @param datasourceId 数据源ID
     * @param dataSource 数据源配置
     */
    public void cacheDataSourceConfig(Long datasourceId, SysDataSource dataSource) {
        if (datasourceId == null || dataSource == null) {
            return;
        }

        String key = buildDataSourceConfigKey(datasourceId);
        cacheService.set(key, dataSource, DATASOURCE_CONFIG_TTL);
        
        // 同时缓存按键查询的映射
        if (StringUtils.isNotBlank(dataSource.getDatasourceKey())) {
            String keyByDsKey = buildDataSourceConfigKeyByKey(dataSource.getDatasourceKey());
            cacheService.set(keyByDsKey, dataSource, DATASOURCE_CONFIG_TTL);
        }

        log.debug("缓存数据源配置: datasourceId={}, key={}", datasourceId, dataSource.getDatasourceKey());
    }

    /**
     * 获取缓存的数据源配置
     *
     * @param datasourceId 数据源ID
     * @param loader 数据加载器（缓存未命中时使用）
     * @return 数据源配置
     */
    public SysDataSource getDataSourceConfig(Long datasourceId, Function<Long, SysDataSource> loader) {
        if (datasourceId == null) {
            return null;
        }

        String key = buildDataSourceConfigKey(datasourceId);
        return cacheService.get(key, k -> {
            SysDataSource dataSource = loader.apply(datasourceId);
            if (dataSource != null) {
                // 同时缓存按键查询的映射
                if (StringUtils.isNotBlank(dataSource.getDatasourceKey())) {
                    String keyByDsKey = buildDataSourceConfigKeyByKey(dataSource.getDatasourceKey());
                    cacheService.set(keyByDsKey, dataSource, DATASOURCE_CONFIG_TTL);
                }
            }
            return dataSource;
        }, DATASOURCE_CONFIG_TTL.getSeconds(), TimeUnit.SECONDS);
    }

    /**
     * 根据数据源键获取缓存的数据源配置
     *
     * @param datasourceKey 数据源键
     * @param loader 数据加载器（缓存未命中时使用）
     * @return 数据源配置
     */
    public SysDataSource getDataSourceConfigByKey(String datasourceKey, Function<String, SysDataSource> loader) {
        if (StringUtils.isBlank(datasourceKey)) {
            return null;
        }

        String key = buildDataSourceConfigKeyByKey(datasourceKey);
        return cacheService.get(key, k -> {
            SysDataSource dataSource = loader.apply(datasourceKey);
            if (dataSource != null) {
                // 同时缓存按ID查询的映射
                String keyById = buildDataSourceConfigKey(dataSource.getId());
                cacheService.set(keyById, dataSource, DATASOURCE_CONFIG_TTL);
            }
            return dataSource;
        }, DATASOURCE_CONFIG_TTL.getSeconds(), TimeUnit.SECONDS);
    }

    /**
     * 删除数据源配置缓存
     *
     * @param datasourceId 数据源ID
     * @param datasourceKey 数据源键
     */
    public void evictDataSourceConfig(Long datasourceId, String datasourceKey) {
        if (datasourceId != null) {
            String key = buildDataSourceConfigKey(datasourceId);
            cacheService.delete(key);
        }
        
        if (StringUtils.isNotBlank(datasourceKey)) {
            String keyByDsKey = buildDataSourceConfigKeyByKey(datasourceKey);
            cacheService.delete(keyByDsKey);
        }

        log.debug("清除数据源配置缓存: datasourceId={}, key={}", datasourceId, datasourceKey);
    }

    // =====================================================
    // 租户数据源关联缓存
    // =====================================================

    /**
     * 缓存租户数据源关联
     *
     * @param tenantId 租户ID
     * @param tenantDataSources 租户数据源关联列表
     */
    public void cacheTenantDataSources(Long tenantId, List<SysTenantDataSource> tenantDataSources) {
        if (tenantId == null || tenantDataSources == null) {
            return;
        }

        String key = buildTenantDataSourceKey(tenantId);
        cacheService.set(key, tenantDataSources, TENANT_DATASOURCE_TTL);

        log.debug("缓存租户数据源关联: tenantId={}, count={}", tenantId, tenantDataSources.size());
    }

    /**
     * 获取缓存的租户数据源关联
     *
     * @param tenantId 租户ID
     * @param loader 数据加载器（缓存未命中时使用）
     * @return 租户数据源关联列表
     */
    @SuppressWarnings("unchecked")
    public List<SysTenantDataSource> getTenantDataSources(Long tenantId, Function<Long, List<SysTenantDataSource>> loader) {
        if (tenantId == null) {
            return List.of();
        }

        String key = buildTenantDataSourceKey(tenantId);
        return cacheService.get(key, k -> loader.apply(tenantId), 
                TENANT_DATASOURCE_TTL.getSeconds(), TimeUnit.SECONDS);
    }

    /**
     * 删除租户数据源关联缓存
     *
     * @param tenantId 租户ID
     */
    public void evictTenantDataSources(Long tenantId) {
        if (tenantId == null) {
            return;
        }

        String key = buildTenantDataSourceKey(tenantId);
        cacheService.delete(key);

        log.debug("清除租户数据源关联缓存: tenantId={}", tenantId);
    }

    // =====================================================
    // 数据源使用统计缓存
    // =====================================================

    /**
     * 缓存数据源使用统计
     *
     * @param datasourceKey 数据源键
     * @param usageCount 使用数量
     */
    public void cacheDataSourceUsage(String datasourceKey, Integer usageCount) {
        if (StringUtils.isBlank(datasourceKey) || usageCount == null) {
            return;
        }

        String key = buildDataSourceUsageKey(datasourceKey);
        cacheService.set(key, usageCount, DATASOURCE_USAGE_TTL);

        log.debug("缓存数据源使用统计: datasourceKey={}, usage={}", datasourceKey, usageCount);
    }

    /**
     * 获取缓存的数据源使用统计
     *
     * @param datasourceKey 数据源键
     * @param loader 数据加载器（缓存未命中时使用）
     * @return 使用数量
     */
    public Integer getDataSourceUsage(String datasourceKey, Function<String, Integer> loader) {
        if (StringUtils.isBlank(datasourceKey)) {
            return 0;
        }

        String key = buildDataSourceUsageKey(datasourceKey);
        Integer usage = cacheService.get(key, k -> loader.apply(datasourceKey), 
                DATASOURCE_USAGE_TTL.getSeconds(), TimeUnit.SECONDS);
        return usage != null ? usage : 0;
    }

    /**
     * 原子性增加数据源使用计数
     *
     * @param datasourceKey 数据源键
     * @param delta 增量
     * @return 增加后的值
     */
    public long incrementDataSourceUsage(String datasourceKey, long delta) {
        if (StringUtils.isBlank(datasourceKey)) {
            return 0;
        }

        String key = buildDataSourceUsageKey(datasourceKey);
        return cacheService.increment(key, delta);
    }

    /**
     * 原子性减少数据源使用计数
     *
     * @param datasourceKey 数据源键
     * @param delta 减量
     * @return 减少后的值
     */
    public long decrementDataSourceUsage(String datasourceKey, long delta) {
        if (StringUtils.isBlank(datasourceKey)) {
            return 0;
        }

        String key = buildDataSourceUsageKey(datasourceKey);
        return cacheService.decrement(key, delta);
    }

    /**
     * 获取所有数据源使用统计
     *
     * @return 数据源使用统计映射
     */
    public Map<String, Integer> getAllDataSourceUsage() {
        String pattern = buildDataSourceUsageKey("*");
        Set<String> keys = cacheService.keys(pattern);
        
        Map<String, Integer> usageMap = cacheService.multiGet(keys);
        
        // 提取数据源键（移除前缀）
        return usageMap.entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(
                        entry -> extractDataSourceKeyFromUsageKey(entry.getKey()),
                        Map.Entry::getValue
                ));
    }

    // =====================================================
    // 数据源健康状态缓存
    // =====================================================

    /**
     * 缓存数据源健康状态
     *
     * @param datasourceKey 数据源键
     * @param isHealthy 是否健康
     */
    public void cacheDataSourceHealth(String datasourceKey, Boolean isHealthy) {
        if (StringUtils.isBlank(datasourceKey) || isHealthy == null) {
            return;
        }

        String key = buildDataSourceHealthKey(datasourceKey);
        cacheService.set(key, isHealthy, DATASOURCE_HEALTH_TTL);

        log.debug("缓存数据源健康状态: datasourceKey={}, healthy={}", datasourceKey, isHealthy);
    }

    /**
     * 获取缓存的数据源健康状态
     *
     * @param datasourceKey 数据源键
     * @return 是否健康，null表示未缓存
     */
    public Boolean getDataSourceHealth(String datasourceKey) {
        if (StringUtils.isBlank(datasourceKey)) {
            return null;
        }

        String key = buildDataSourceHealthKey(datasourceKey);
        return cacheService.get(key, Boolean.class);
    }

    // =====================================================
    // 缓存管理方法
    // =====================================================

    /**
     * 清空所有数据源相关缓存
     */
    public void clearAllDataSourceCache() {
        // 使用分布式锁确保操作的原子性
        String lockKey = "datasource_cache_clear_lock";
        cacheService.executeWithLock(lockKey, 30, TimeUnit.SECONDS, () -> {
            cacheService.deleteByPattern(DATASOURCE_PREFIX + ":*");
            cacheService.deleteByPattern(TENANT_DATASOURCE_PREFIX + ":*");
            cacheService.deleteByPattern(DATASOURCE_CONFIG_PREFIX + ":*");
            cacheService.deleteByPattern(DATASOURCE_USAGE_PREFIX + ":*");
            cacheService.deleteByPattern(DATASOURCE_HEALTH_PREFIX + ":*");
            
            log.info("已清空所有数据源相关缓存");
        });
    }

    /**
     * 清空指定租户的数据源缓存
     *
     * @param tenantId 租户ID
     */
    public void clearTenantDataSourceCache(Long tenantId) {
        if (tenantId == null) {
            return;
        }

        evictTenantDataSources(tenantId);
        log.info("已清空租户数据源缓存: tenantId={}", tenantId);
    }

    // =====================================================
    // 私有辅助方法
    // =====================================================

    private String buildDataSourceConfigKey(Long datasourceId) {
        return CacheUtils.buildKey(DATASOURCE_CONFIG_PREFIX, "id", String.valueOf(datasourceId));
    }

    private String buildDataSourceConfigKeyByKey(String datasourceKey) {
        return CacheUtils.buildKey(DATASOURCE_CONFIG_PREFIX, "key", datasourceKey);
    }

    private String buildTenantDataSourceKey(Long tenantId) {
        return CacheUtils.buildKey(TENANT_DATASOURCE_PREFIX, String.valueOf(tenantId));
    }

    private String buildDataSourceUsageKey(String datasourceKey) {
        return CacheUtils.buildKey(DATASOURCE_USAGE_PREFIX, datasourceKey);
    }

    private String buildDataSourceHealthKey(String datasourceKey) {
        return CacheUtils.buildKey(DATASOURCE_HEALTH_PREFIX, datasourceKey);
    }

    private String extractDataSourceKeyFromUsageKey(String usageKey) {
        String prefix = DATASOURCE_USAGE_PREFIX + ":";
        return usageKey.startsWith(prefix) ? usageKey.substring(prefix.length()) : usageKey;
    }
}
