package com.rega.erp.common.db.tenant.isolation;

import lombok.Data;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 查询上下文
 * 用于在查询过程中传递租户相关信息
 *
 * <AUTHOR>
 */
@Data
public class QueryContext {
    
    /**
     * 数据源
     */
    private DataSource dataSource;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 表名
     */
    private String tableName;
    
    /**
     * 查询条件
     */
    private Map<String, Object> conditions = new HashMap<>();
    
    /**
     * 租户过滤条件
     */
    private Map<String, Object> tenantConditions = new HashMap<>();
    
    /**
     * 排序条件
     */
    private Map<String, String> orderBy = new HashMap<>();
    
    /**
     * 分页信息
     */
    private PageInfo pageInfo;
    
    /**
     * 查询字段
     */
    private String[] selectFields;
    
    /**
     * 连接信息
     */
    private Map<String, JoinInfo> joins = new HashMap<>();
    
    /**
     * 分组字段
     */
    private String[] groupBy;
    
    /**
     * Having条件
     */
    private Map<String, Object> having = new HashMap<>();
    
    /**
     * 是否去重
     */
    private boolean distinct = false;
    
    /**
     * 查询提示
     */
    private Map<String, Object> hints = new HashMap<>();
    
    /**
     * 添加查询条件
     */
    public void addCondition(String field, Object value) {
        conditions.put(field, value);
    }
    
    /**
     * 添加租户条件
     */
    public void addTenantCondition(String field, Object value) {
        tenantConditions.put(field, value);
    }
    
    /**
     * 添加排序条件
     */
    public void addOrderBy(String field, String direction) {
        orderBy.put(field, direction);
    }
    
    /**
     * 添加连接
     */
    public void addJoin(String alias, JoinInfo joinInfo) {
        joins.put(alias, joinInfo);
    }
    
    /**
     * 添加Having条件
     */
    public void addHaving(String field, Object value) {
        having.put(field, value);
    }
    
    /**
     * 添加查询提示
     */
    public void addHint(String key, Object value) {
        hints.put(key, value);
    }
    
    /**
     * 获取所有条件（包含租户条件）
     */
    public Map<String, Object> getAllConditions() {
        Map<String, Object> allConditions = new HashMap<>(conditions);
        allConditions.putAll(tenantConditions);
        return allConditions;
    }
    
    /**
     * 是否有分页
     */
    public boolean hasPagination() {
        return pageInfo != null && pageInfo.getPageSize() > 0;
    }
    
    /**
     * 是否有排序
     */
    public boolean hasOrderBy() {
        return orderBy != null && !orderBy.isEmpty();
    }
    
    /**
     * 是否有连接
     */
    public boolean hasJoins() {
        return joins != null && !joins.isEmpty();
    }
    
    /**
     * 是否有分组
     */
    public boolean hasGroupBy() {
        return groupBy != null && groupBy.length > 0;
    }
    
    /**
     * 是否有Having条件
     */
    public boolean hasHaving() {
        return having != null && !having.isEmpty();
    }
    
    /**
     * 分页信息
     */
    @Data
    public static class PageInfo {
        /**
         * 页码（从1开始）
         */
        private int pageNum = 1;
        
        /**
         * 页大小
         */
        private int pageSize = 10;
        
        /**
         * 偏移量
         */
        public int getOffset() {
            return (pageNum - 1) * pageSize;
        }
    }
    
    /**
     * 连接信息
     */
    @Data
    public static class JoinInfo {
        /**
         * 连接类型
         */
        private JoinType joinType = JoinType.INNER;
        
        /**
         * 连接表名
         */
        private String tableName;
        
        /**
         * 连接条件
         */
        private String onCondition;
        
        /**
         * 连接字段映射
         */
        private Map<String, String> fieldMapping = new HashMap<>();
    }
    
    /**
     * 连接类型
     */
    public enum JoinType {
        INNER("INNER JOIN"),
        LEFT("LEFT JOIN"),
        RIGHT("RIGHT JOIN"),
        FULL("FULL JOIN");
        
        private final String sql;
        
        JoinType(String sql) {
            this.sql = sql;
        }
        
        public String getSql() {
            return sql;
        }
    }
}
