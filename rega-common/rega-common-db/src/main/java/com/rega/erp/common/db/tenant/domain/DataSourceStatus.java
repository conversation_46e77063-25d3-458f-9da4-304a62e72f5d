package com.rega.erp.common.db.tenant.domain;

/**
 * 数据源状态枚举
 *
 * <AUTHOR>
 */
public enum DataSourceStatus {
    
    /**
     * 活跃状态 - 正常使用
     */
    ACTIVE("active", "活跃", "数据源正常使用中"),
    
    /**
     * 维护状态 - 正在维护
     */
    MAINTENANCE("maintenance", "维护中", "数据源正在维护"),
    
    /**
     * 故障状态 - 连接异常
     */
    ERROR("error", "故障", "数据源连接异常"),
    
    /**
     * 禁用状态 - 被管理员禁用
     */
    DISABLED("disabled", "禁用", "数据源被管理员禁用"),
    
    /**
     * 删除状态 - 标记删除
     */
    DELETED("deleted", "已删除", "数据源已被删除");
    
    /**
     * 状态代码
     */
    private final String code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;
    
    DataSourceStatus(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取状态
     */
    public static DataSourceStatus fromCode(String code) {
        for (DataSourceStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown data source status code: " + code);
    }
    
    /**
     * 是否为可用状态
     */
    public boolean isAvailable() {
        return this == ACTIVE;
    }
    
    /**
     * 是否为不可用状态
     */
    public boolean isUnavailable() {
        return this == MAINTENANCE || this == ERROR || this == DISABLED || this == DELETED;
    }
}
