package com.rega.erp.common.db.tenant.core;

import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.util.StringUtils;
import com.rega.erp.common.db.tenant.datasource.TenantDataSourceManager;
import com.rega.erp.common.db.tenant.domain.*;
import com.rega.erp.common.db.tenant.isolation.TenantIsolationManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 租户管理器
 * 负责租户的创建、更新、删除等核心操作
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TenantManager {
    
    private final TenantRegistry tenantRegistry;
    private final TenantDataSourceManager dataSourceManager;
    private final TenantIsolationManager isolationManager;
    
    /**
     * 创建租户
     */
    public TenantCreationResult createTenant(TenantCreationRequest request) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 验证请求
            validateTenantRequest(request);
            
            // 2. 检查租户是否已存在
            if (tenantRegistry.existsTenant(request.getTenantId())) {
                return TenantCreationResult.failure("TENANT_EXISTS", 
                    "租户已存在: " + request.getTenantId());
            }
            
            // 3. 创建租户信息对象
            TenantInfo tenantInfo = buildTenantInfo(request);
            
            // 4. 处理数据源分配
            String dataSourceId = handleDataSourceAssignment(request);
            tenantInfo.setDataSourceId(dataSourceId);
            
            // 5. 注册租户
            tenantRegistry.registerTenant(tenantInfo);
            
            // 6. 初始化租户环境
            isolationManager.initializeTenant(tenantInfo);
            
            // 7. 创建成功结果
            TenantCreationResult result = TenantCreationResult.success(tenantInfo, dataSourceId, 
                request.needsNewDataSource());
            result.setProcessingTime(startTime);
            
            log.info("租户创建成功: tenantId={}, isolationType={}, dataSourceId={}", 
                    tenantInfo.getTenantId(), tenantInfo.getIsolationType(), dataSourceId);
            
            return result;
            
        } catch (BusinessException e) {
            log.error("租户创建失败: tenantId={}, error={}", request.getTenantId(), e.getMessage());
            TenantCreationResult result = TenantCreationResult.failure(e.getMessage());
            result.setProcessingTime(startTime);
            return result;
        } catch (Exception e) {
            log.error("租户创建异常: tenantId={}", request.getTenantId(), e);
            TenantCreationResult result = TenantCreationResult.failure("INTERNAL_ERROR", 
                "系统内部错误: " + e.getMessage());
            result.setProcessingTime(startTime);
            return result;
        }
    }
    
    /**
     * 更新租户信息
     */
    public void updateTenant(String tenantId, TenantInfo tenantInfo) {
        if (StringUtils.isBlank(tenantId)) {
            throw new BusinessException("tenant.id.required");
        }
        
        if (tenantInfo == null) {
            throw new BusinessException("tenant.info.null");
        }
        
        // 确保租户ID一致
        tenantInfo.setTenantId(tenantId);
        
        // 更新租户信息
        tenantRegistry.updateTenant(tenantInfo);
        
        log.info("租户信息更新成功: tenantId={}", tenantId);
    }
    
    /**
     * 删除租户
     */
    public void deleteTenant(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            throw new BusinessException("tenant.id.required");
        }
        
        TenantInfo tenantInfo = tenantRegistry.getTenantInfo(tenantId);
        if (tenantInfo == null) {
            throw new BusinessException("tenant.not.found", tenantId);
        }
        
        try {
            // 1. 标记租户为删除状态
            tenantRegistry.deleteTenant(tenantId);
            
            // 2. 清理租户环境
            isolationManager.cleanupTenant(tenantInfo);
            
            // 3. 处理数据源清理
            if (tenantInfo.isDataSourceIsolation()) {
                dataSourceManager.removeTenantFromDataSource(tenantId, tenantInfo.getDataSourceId());
            }
            
            log.info("租户删除成功: tenantId={}", tenantId);
            
        } catch (Exception e) {
            log.error("租户删除失败: tenantId={}", tenantId, e);
            throw new BusinessException("tenant.delete.failed", e.getMessage());
        }
    }
    
    /**
     * 获取租户信息
     */
    public TenantInfo getTenantInfo(String tenantId) {
        return tenantRegistry.getTenantInfo(tenantId);
    }
    
    /**
     * 检查租户是否可用
     */
    public boolean isTenantAvailable(String tenantId) {
        return tenantRegistry.isTenantAvailable(tenantId);
    }
    
    /**
     * 暂停租户
     */
    public void suspendTenant(String tenantId) {
        TenantInfo tenantInfo = getTenantInfo(tenantId);
        if (tenantInfo == null) {
            throw new BusinessException("tenant.not.found", tenantId);
        }
        
        tenantInfo.setStatus(TenantStatus.SUSPENDED);
        tenantRegistry.updateTenant(tenantInfo);
        
        log.info("租户暂停成功: tenantId={}", tenantId);
    }
    
    /**
     * 恢复租户
     */
    public void resumeTenant(String tenantId) {
        TenantInfo tenantInfo = getTenantInfo(tenantId);
        if (tenantInfo == null) {
            throw new BusinessException("tenant.not.found", tenantId);
        }
        
        tenantInfo.setStatus(TenantStatus.ACTIVE);
        tenantRegistry.updateTenant(tenantInfo);
        
        log.info("租户恢复成功: tenantId={}", tenantId);
    }
    
    /**
     * 验证租户请求
     */
    private void validateTenantRequest(TenantCreationRequest request) {
        if (request == null) {
            throw new BusinessException("tenant.request.null");
        }
        
        if (!request.isValid()) {
            throw new BusinessException("tenant.request.invalid");
        }
        
        // 验证租户ID格式
        if (!isValidTenantId(request.getTenantId())) {
            throw new BusinessException("tenant.id.invalid.format");
        }
    }
    
    /**
     * 构建租户信息对象
     */
    private TenantInfo buildTenantInfo(TenantCreationRequest request) {
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantId(request.getTenantId());
        tenantInfo.setTenantName(request.getTenantName());
        tenantInfo.setDescription(request.getDescription());
        tenantInfo.setIsolationType(request.getIsolationType());
        tenantInfo.setContactName(request.getContactName());
        tenantInfo.setContactEmail(request.getContactEmail());
        tenantInfo.setContactPhone(request.getContactPhone());
        tenantInfo.setExpireTime(request.getExpireTime());
        tenantInfo.setMaxUsers(request.getMaxUsers());
        tenantInfo.setMaxStorage(request.getMaxStorage());
        tenantInfo.setConfig(request.getConfig());
        tenantInfo.setStatus(TenantStatus.ACTIVE);
        tenantInfo.setCreateTime(LocalDateTime.now());
        tenantInfo.setUpdateTime(LocalDateTime.now());
        
        return tenantInfo;
    }
    
    /**
     * 处理数据源分配
     */
    private String handleDataSourceAssignment(TenantCreationRequest request) {
        if (request.getIsolationType() == TenantIsolationType.FIELD_ISOLATION) {
            // 字段隔离：使用主数据源
            return "primary";
        } else {
            // 数据源隔离：分配或创建专用数据源
            return dataSourceManager.assignOrCreateDataSource(request);
        }
    }
    
    /**
     * 验证租户ID格式
     */
    private boolean isValidTenantId(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return false;
        }
        
        // 租户ID规则：3-64位，只能包含字母、数字、下划线、连字符
        return tenantId.matches("^[a-zA-Z0-9_-]{3,64}$");
    }
}
