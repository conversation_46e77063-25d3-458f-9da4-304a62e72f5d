package com.rega.erp.common.db.tenant.datasource;

import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.db.tenant.domain.DataSourceConfig;
import com.rega.erp.common.db.tenant.domain.PoolConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

/**
 * 数据源工厂
 * 负责创建和配置数据源实例
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DataSourceFactory {
    
    /**
     * 创建数据源
     */
    public DataSource createDataSource(DataSourceConfig config) {
        if (config == null || !config.isValid()) {
            throw new BusinessException("datasource.config.invalid");
        }
        
        try {
            HikariConfig hikariConfig = buildHikariConfig(config);
            HikariDataSource dataSource = new HikariDataSource(hikariConfig);
            
            // 测试连接
            testConnection(dataSource);
            
            log.info("数据源创建成功: url={}, username={}", 
                    config.getJdbcUrl(), config.getUsername());
            
            return dataSource;
            
        } catch (Exception e) {
            log.error("数据源创建失败: url={}, username={}", 
                    config.getJdbcUrl(), config.getUsername(), e);
            throw new BusinessException("datasource.create.failed", e.getMessage());
        }
    }
    
    /**
     * 创建数据源（带连接池配置）
     */
    public DataSource createDataSource(DataSourceConfig config, PoolConfig poolConfig) {
        if (config == null || !config.isValid()) {
            throw new BusinessException("datasource.config.invalid");
        }
        
        try {
            HikariConfig hikariConfig = buildHikariConfig(config, poolConfig);
            HikariDataSource dataSource = new HikariDataSource(hikariConfig);
            
            // 测试连接
            testConnection(dataSource);
            
            log.info("数据源创建成功: url={}, username={}, poolName={}", 
                    config.getJdbcUrl(), config.getUsername(), poolConfig.getPoolName());
            
            return dataSource;
            
        } catch (Exception e) {
            log.error("数据源创建失败: url={}, username={}", 
                    config.getJdbcUrl(), config.getUsername(), e);
            throw new BusinessException("datasource.create.failed", e.getMessage());
        }
    }
    
    /**
     * 构建HikariCP配置
     */
    private HikariConfig buildHikariConfig(DataSourceConfig config) {
        return buildHikariConfig(config, PoolConfig.createDefault());
    }
    
    /**
     * 构建HikariCP配置（带连接池配置）
     */
    private HikariConfig buildHikariConfig(DataSourceConfig config, PoolConfig poolConfig) {
        HikariConfig hikariConfig = new HikariConfig();
        
        // 基本连接配置
        hikariConfig.setDriverClassName(config.getDriverClassName());
        hikariConfig.setJdbcUrl(config.getJdbcUrl());
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        
        // 连接池配置
        if (poolConfig.getMaximumPoolSize() != null) {
            hikariConfig.setMaximumPoolSize(poolConfig.getMaximumPoolSize());
        }
        
        if (poolConfig.getMinimumIdle() != null) {
            hikariConfig.setMinimumIdle(poolConfig.getMinimumIdle());
        }
        
        if (poolConfig.getConnectionTimeout() != null) {
            hikariConfig.setConnectionTimeout(poolConfig.getConnectionTimeout());
        }
        
        if (poolConfig.getIdleTimeout() != null) {
            hikariConfig.setIdleTimeout(poolConfig.getIdleTimeout());
        }
        
        if (poolConfig.getMaxLifetime() != null) {
            hikariConfig.setMaxLifetime(poolConfig.getMaxLifetime());
        }
        
        if (poolConfig.getLeakDetectionThreshold() != null) {
            hikariConfig.setLeakDetectionThreshold(poolConfig.getLeakDetectionThreshold());
        }
        
        if (poolConfig.getValidationTimeout() != null) {
            hikariConfig.setValidationTimeout(poolConfig.getValidationTimeout());
        }
        
        if (poolConfig.getPoolName() != null) {
            hikariConfig.setPoolName(poolConfig.getPoolName());
        }
        
        if (poolConfig.getAllowPoolSuspension() != null) {
            hikariConfig.setAllowPoolSuspension(poolConfig.getAllowPoolSuspension());
        }
        
        if (poolConfig.getRegisterMbeans() != null) {
            hikariConfig.setRegisterMbeans(poolConfig.getRegisterMbeans());
        }
        
        // 数据库特定配置
        if (config.getConnectionTestQuery() != null) {
            hikariConfig.setConnectionTestQuery(config.getConnectionTestQuery());
        }
        
        if (config.getAutoCommit() != null) {
            hikariConfig.setAutoCommit(config.getAutoCommit());
        }
        
        if (config.getReadOnly() != null) {
            hikariConfig.setReadOnly(config.getReadOnly());
        }
        
        if (config.getTransactionIsolation() != null) {
            hikariConfig.setTransactionIsolation(config.getTransactionIsolation());
        }
        
        // PostgreSQL特定配置
        configurePostgreSQL(hikariConfig);
        
        return hikariConfig;
    }
    
    /**
     * 配置PostgreSQL特定参数
     */
    private void configurePostgreSQL(HikariConfig hikariConfig) {
        // PostgreSQL推荐配置
        hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
        hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
        hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        hikariConfig.addDataSourceProperty("useServerPrepStmts", "true");
        hikariConfig.addDataSourceProperty("useLocalSessionState", "true");
        hikariConfig.addDataSourceProperty("rewriteBatchedStatements", "true");
        hikariConfig.addDataSourceProperty("cacheResultSetMetadata", "true");
        hikariConfig.addDataSourceProperty("cacheServerConfiguration", "true");
        hikariConfig.addDataSourceProperty("elideSetAutoCommits", "true");
        hikariConfig.addDataSourceProperty("maintainTimeStats", "false");
        
        // PostgreSQL连接参数
        hikariConfig.addDataSourceProperty("ApplicationName", "RegaWebERP");
        hikariConfig.addDataSourceProperty("stringtype", "unspecified");
    }
    
    /**
     * 测试数据源连接
     */
    private void testConnection(DataSource dataSource) {
        try (var connection = dataSource.getConnection()) {
            if (!connection.isValid(5)) {
                throw new BusinessException("datasource.connection.invalid");
            }
            log.debug("数据源连接测试成功");
        } catch (Exception e) {
            throw new BusinessException("datasource.connection.test.failed", e.getMessage());
        }
    }
    
    /**
     * 创建PostgreSQL数据源
     */
    public DataSource createPostgreSQLDataSource(String host, int port, String database, 
                                                String username, String password) {
        DataSourceConfig config = DataSourceConfig.createPostgreSQLConfig(
                host, port, database, username, password);
        return createDataSource(config);
    }
    
    /**
     * 创建PostgreSQL数据源（带连接池配置）
     */
    public DataSource createPostgreSQLDataSource(String host, int port, String database, 
                                                String username, String password, PoolConfig poolConfig) {
        DataSourceConfig config = DataSourceConfig.createPostgreSQLConfig(
                host, port, database, username, password);
        return createDataSource(config, poolConfig);
    }
    
    /**
     * 关闭数据源
     */
    public void closeDataSource(DataSource dataSource) {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
            if (!hikariDataSource.isClosed()) {
                hikariDataSource.close();
                log.info("数据源已关闭: poolName={}", hikariDataSource.getPoolName());
            }
        } else if (dataSource instanceof AutoCloseable) {
            try {
                ((AutoCloseable) dataSource).close();
                log.info("数据源已关闭");
            } catch (Exception e) {
                log.warn("关闭数据源失败", e);
            }
        }
    }
}
