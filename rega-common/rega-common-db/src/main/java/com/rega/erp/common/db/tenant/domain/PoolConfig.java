package com.rega.erp.common.db.tenant.domain;

import lombok.Builder;
import lombok.Data;

/**
 * 连接池配置类
 *
 * <AUTHOR>
 */
@Data
@Builder
public class PoolConfig {
    
    /**
     * 最大连接数
     */
    private Integer maximumPoolSize;
    
    /**
     * 最小空闲连接数
     */
    private Integer minimumIdle;
    
    /**
     * 连接超时时间（毫秒）
     */
    private Long connectionTimeout;
    
    /**
     * 空闲超时时间（毫秒）
     */
    private Long idleTimeout;
    
    /**
     * 最大生命周期（毫秒）
     */
    private Long maxLifetime;
    
    /**
     * 连接泄漏检测阈值（毫秒）
     */
    private Long leakDetectionThreshold;
    
    /**
     * 验证超时时间（毫秒）
     */
    private Long validationTimeout;
    
    /**
     * 连接池名称
     */
    private String poolName;
    
    /**
     * 是否允许连接池暂停
     */
    private Boolean allowPoolSuspension;
    
    /**
     * 是否注册JMX
     */
    private Boolean registerMbeans;
    
    /**
     * 创建默认配置
     */
    public static PoolConfig createDefault() {
        return PoolConfig.builder()
                .maximumPoolSize(10)
                .minimumIdle(2)
                .connectionTimeout(30000L)
                .idleTimeout(600000L)
                .maxLifetime(1800000L)
                .leakDetectionThreshold(60000L)
                .validationTimeout(5000L)
                .allowPoolSuspension(false)
                .registerMbeans(true)
                .build();
    }
    
    /**
     * 创建小型连接池配置
     */
    public static PoolConfig createSmall() {
        return PoolConfig.builder()
                .maximumPoolSize(5)
                .minimumIdle(1)
                .connectionTimeout(30000L)
                .idleTimeout(300000L)
                .maxLifetime(1800000L)
                .leakDetectionThreshold(60000L)
                .validationTimeout(5000L)
                .allowPoolSuspension(false)
                .registerMbeans(true)
                .build();
    }
    
    /**
     * 创建大型连接池配置
     */
    public static PoolConfig createLarge() {
        return PoolConfig.builder()
                .maximumPoolSize(20)
                .minimumIdle(5)
                .connectionTimeout(30000L)
                .idleTimeout(600000L)
                .maxLifetime(1800000L)
                .leakDetectionThreshold(60000L)
                .validationTimeout(5000L)
                .allowPoolSuspension(false)
                .registerMbeans(true)
                .build();
    }
}
