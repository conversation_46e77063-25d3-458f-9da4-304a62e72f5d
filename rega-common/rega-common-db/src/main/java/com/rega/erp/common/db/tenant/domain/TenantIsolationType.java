package com.rega.erp.common.db.tenant.domain;

/**
 * 租户隔离类型枚举
 *
 * <AUTHOR>
 */
public enum TenantIsolationType {
    
    /**
     * 字段隔离
     * - 多个租户共享同一个数据源
     * - 通过 tenant_id 字段区分数据
     * - 成本低，适合小型客户
     */
    FIELD_ISOLATION("field", "字段隔离", "通过tenant_id字段隔离数据，多租户共享数据源"),
    
    /**
     * 数据源隔离
     * - 租户使用专用数据源
     * - 表结构相同，仍有 tenant_id 字段
     * - 安全性高，适合大型客户
     */
    DATASOURCE_ISOLATION("datasource", "数据源隔离", "使用专用数据源，提供更高的数据隔离性");
    
    /**
     * 隔离类型代码
     */
    private final String code;
    
    /**
     * 隔离类型名称
     */
    private final String name;
    
    /**
     * 隔离类型描述
     */
    private final String description;
    
    TenantIsolationType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取隔离类型
     */
    public static TenantIsolationType fromCode(String code) {
        for (TenantIsolationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown tenant isolation type code: " + code);
    }
    
    /**
     * 是否为字段隔离
     */
    public boolean isFieldIsolation() {
        return this == FIELD_ISOLATION;
    }
    
    /**
     * 是否为数据源隔离
     */
    public boolean isDataSourceIsolation() {
        return this == DATASOURCE_ISOLATION;
    }
}
