package com.rega.erp.common.db.tenant.isolation;

import com.rega.erp.common.core.context.TenantContextHolder;
import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.util.StringUtils;
import com.rega.erp.common.db.tenant.core.TenantRegistry;
import com.rega.erp.common.db.tenant.datasource.DataSourceRouter;
import com.rega.erp.common.db.tenant.domain.TenantInfo;
import com.rega.erp.common.db.tenant.domain.TenantIsolationType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 租户隔离管理器
 * 负责处理租户数据隔离逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TenantIsolationManager {
    
    private final TenantRegistry tenantRegistry;
    private final DataSourceRouter dataSourceRouter;
    
    /**
     * 初始化租户环境
     */
    public void initializeTenant(TenantInfo tenantInfo) {
        if (tenantInfo == null) {
            throw new BusinessException("tenant.info.null");
        }
        
        try {
            if (tenantInfo.isDataSourceIsolation()) {
                // 数据源隔离：初始化专用数据源环境
                initializeDataSourceIsolation(tenantInfo);
            } else {
                // 字段隔离：初始化字段隔离环境
                initializeFieldIsolation(tenantInfo);
            }
            
            log.info("租户环境初始化成功: tenantId={}, isolationType={}", 
                    tenantInfo.getTenantId(), tenantInfo.getIsolationType());
            
        } catch (Exception e) {
            log.error("租户环境初始化失败: tenantId={}", tenantInfo.getTenantId(), e);
            throw new BusinessException("业务异常");
        }
    }
    
    /**
     * 处理查询请求
     */
    public void processQuery(QueryContext context) {
        String tenantId = TenantContextHolder.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            throw new BusinessException("tenant.id.required");
        }
        
        TenantInfo tenantInfo = tenantRegistry.getTenantInfo(tenantId);
        if (tenantInfo == null) {
            throw new BusinessException("业务异常");
        }
        
        if (!tenantInfo.isActive()) {
            throw new BusinessException("业务异常");
        }
        
        // 根据隔离类型处理
        if (tenantInfo.isDataSourceIsolation()) {
            // 数据源隔离：切换到租户专用数据源
            processDataSourceIsolationQuery(context, tenantInfo);
        } else {
            // 字段隔离：使用默认数据源
            processFieldIsolationQuery(context, tenantInfo);
        }
        
        // 无论哪种隔离类型，都需要添加租户过滤条件
        addTenantFilter(context, tenantId);
    }
    
    /**
     * 处理数据操作
     */
    public void processDataOperation(DataOperationContext context) {
        String tenantId = TenantContextHolder.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            throw new BusinessException("tenant.id.required");
        }
        
        TenantInfo tenantInfo = tenantRegistry.getTenantInfo(tenantId);
        if (tenantInfo == null) {
            throw new BusinessException("业务异常");
        }
        
        if (!tenantInfo.isActive()) {
            throw new BusinessException("业务异常");
        }
        
        // 根据隔离类型处理
        if (tenantInfo.isDataSourceIsolation()) {
            // 数据源隔离：切换到租户专用数据源
            processDataSourceIsolationOperation(context, tenantInfo);
        }
        // 字段隔离使用默认数据源，无需特殊处理
        
        // 无论哪种隔离类型，都需要设置租户ID
        setTenantId(context, tenantId);
    }
    
    /**
     * 清理租户环境
     */
    public void cleanupTenant(TenantInfo tenantInfo) {
        if (tenantInfo == null) {
            return;
        }
        
        try {
            if (tenantInfo.isDataSourceIsolation()) {
                // 数据源隔离：清理专用数据源环境
                cleanupDataSourceIsolation(tenantInfo);
            } else {
                // 字段隔离：清理字段隔离环境
                cleanupFieldIsolation(tenantInfo);
            }
            
            log.info("租户环境清理成功: tenantId={}", tenantInfo.getTenantId());
            
        } catch (Exception e) {
            log.error("租户环境清理失败: tenantId={}", tenantInfo.getTenantId(), e);
        }
    }
    
    /**
     * 初始化数据源隔离环境
     */
    private void initializeDataSourceIsolation(TenantInfo tenantInfo) {
        // 验证数据源是否可用
        if (!dataSourceRouter.isDataSourceAvailable(tenantInfo.getDataSourceId())) {
            throw new BusinessException("业务异常");
        }
        
        // 可以在这里执行数据库初始化脚本
        // 例如：创建表、索引、初始数据等
        
        log.debug("数据源隔离环境初始化: tenantId={}, dataSourceId={}", 
                tenantInfo.getTenantId(), tenantInfo.getDataSourceId());
    }
    
    /**
     * 初始化字段隔离环境
     */
    private void initializeFieldIsolation(TenantInfo tenantInfo) {
        // 字段隔离通常不需要特殊的初始化
        // 可以在这里执行一些验证或准备工作
        
        log.debug("字段隔离环境初始化: tenantId={}", tenantInfo.getTenantId());
    }
    
    /**
     * 处理数据源隔离查询
     */
    private void processDataSourceIsolationQuery(QueryContext context, TenantInfo tenantInfo) {
        // 切换到租户专用数据源
        context.setDataSource(dataSourceRouter.getDataSource(tenantInfo.getDataSourceId()));
        
        log.debug("切换到租户数据源: tenantId={}, dataSourceId={}", 
                tenantInfo.getTenantId(), tenantInfo.getDataSourceId());
    }
    
    /**
     * 处理字段隔离查询
     */
    private void processFieldIsolationQuery(QueryContext context, TenantInfo tenantInfo) {
        // 字段隔离使用默认数据源，无需切换
        log.debug("使用字段隔离查询: tenantId={}", tenantInfo.getTenantId());
    }
    
    /**
     * 处理数据源隔离操作
     */
    private void processDataSourceIsolationOperation(DataOperationContext context, TenantInfo tenantInfo) {
        // 切换到租户专用数据源
        context.setDataSource(dataSourceRouter.getDataSource(tenantInfo.getDataSourceId()));
        
        log.debug("切换到租户数据源: tenantId={}, dataSourceId={}", 
                tenantInfo.getTenantId(), tenantInfo.getDataSourceId());
    }
    
    /**
     * 添加租户过滤条件
     */
    private void addTenantFilter(QueryContext context, String tenantId) {
        // 添加 tenant_id 过滤条件
        context.addTenantCondition("tenant_id", tenantId);
        
        log.debug("添加租户过滤条件: tenantId={}", tenantId);
    }
    
    /**
     * 设置租户ID
     */
    private void setTenantId(DataOperationContext context, String tenantId) {
        // 自动设置 tenant_id 字段值
        context.setTenantId(tenantId);
        
        log.debug("设置租户ID: tenantId={}", tenantId);
    }
    
    /**
     * 清理数据源隔离环境
     */
    private void cleanupDataSourceIsolation(TenantInfo tenantInfo) {
        // 可以在这里执行数据清理、备份等操作
        log.debug("清理数据源隔离环境: tenantId={}, dataSourceId={}", 
                tenantInfo.getTenantId(), tenantInfo.getDataSourceId());
    }
    
    /**
     * 清理字段隔离环境
     */
    private void cleanupFieldIsolation(TenantInfo tenantInfo) {
        // 可以在这里执行数据清理操作
        log.debug("清理字段隔离环境: tenantId={}", tenantInfo.getTenantId());
    }
    
    /**
     * 验证租户权限
     */
    public boolean validateTenantAccess(String tenantId, String resourceId) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(resourceId)) {
            return false;
        }
        
        TenantInfo tenantInfo = tenantRegistry.getTenantInfo(tenantId);
        if (tenantInfo == null || !tenantInfo.isActive()) {
            return false;
        }
        
        // 可以在这里添加更复杂的权限验证逻辑
        
        return true;
    }
}
