package com.rega.erp.common.db.tenant.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 租户配置属性
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "rega.tenant")
public class TenantProperties {
    
    /**
     * 是否启用多租户
     */
    private boolean enabled = true;
    
    /**
     * 租户字段配置
     */
    private Field field = new Field();
    
    /**
     * 数据源配置
     */
    private DataSource dataSource = new DataSource();
    
    /**
     * 租户管理配置
     */
    private Management management = new Management();
    
    /**
     * 租户字段配置
     */
    @Data
    public static class Field {
        /**
         * 租户字段名称
         */
        private String name = "tenant_id";
        
        /**
         * 租户字段类型
         */
        private String type = "VARCHAR(64)";
        
        /**
         * 是否可为空
         */
        private boolean nullable = false;
        
        /**
         * 是否创建索引
         */
        private boolean indexed = true;
        
        /**
         * 默认值
         */
        private String defaultValue;
        
        /**
         * 字段注释
         */
        private String comment = "租户ID";
    }
    
    /**
     * 数据源配置
     */
    @Data
    public static class DataSource {
        /**
         * 主数据源配置
         */
        private Primary primary = new Primary();
        
        /**
         * 专用数据源配置
         */
        private Dedicated dedicated = new Dedicated();
        
        /**
         * 主数据源配置
         */
        @Data
        public static class Primary {
            /**
             * 数据库URL
             */
            private String url = "*****************************************";
            
            /**
             * 用户名
             */
            private String username = "rega_user";
            
            /**
             * 密码
             */
            private String password;
            
            /**
             * 驱动类名
             */
            private String driverClassName = "org.postgresql.Driver";
        }
        
        /**
         * 专用数据源配置
         */
        @Data
        public static class Dedicated {
            /**
             * 数据库URL模板
             */
            private String urlTemplate = "********************************************_{index}";
            
            /**
             * 用户名模板
             */
            private String usernameTemplate = "tenant_user_{index}";
            
            /**
             * 密码模板
             */
            private String passwordTemplate;
            
            /**
             * 驱动类名
             */
            private String driverClassName = "org.postgresql.Driver";
            
            /**
             * 每个数据源最大租户数
             */
            private int maxTenantsPerDataSource = 10;
            
            /**
             * 连接池配置
             */
            private Hikari hikari = new Hikari();
            
            /**
             * HikariCP连接池配置
             */
            @Data
            public static class Hikari {
                /**
                 * 最大连接数
                 */
                private int maximumPoolSize = 10;
                
                /**
                 * 最小空闲连接数
                 */
                private int minimumIdle = 2;
                
                /**
                 * 连接超时时间（毫秒）
                 */
                private long connectionTimeout = 30000;
                
                /**
                 * 空闲超时时间（毫秒）
                 */
                private long idleTimeout = 600000;
                
                /**
                 * 最大生命周期（毫秒）
                 */
                private long maxLifetime = 1800000;
                
                /**
                 * 连接泄漏检测阈值（毫秒）
                 */
                private long leakDetectionThreshold = 60000;
                
                /**
                 * 验证超时时间（毫秒）
                 */
                private long validationTimeout = 5000;
            }
        }
    }
    
    /**
     * 租户管理配置
     */
    @Data
    public static class Management {
        /**
         * 租户信息缓存时间（秒）
         */
        private long cacheTimeout = 3600;
        
        /**
         * 是否自动创建数据库
         */
        private boolean autoCreateDatabase = true;
        
        /**
         * 是否自动初始化表结构
         */
        private boolean autoInitSchema = true;
        
        /**
         * 是否启用数据源预热
         */
        private boolean datasourceWarmup = true;
        
        /**
         * 健康检查间隔（秒）
         */
        private long healthCheckInterval = 300;
        
        /**
         * 是否启用性能监控
         */
        private boolean performanceMonitoring = true;
        
        /**
         * 最大租户数量限制
         */
        private int maxTenants = 1000;
        
        /**
         * 租户ID最小长度
         */
        private int tenantIdMinLength = 3;
        
        /**
         * 租户ID最大长度
         */
        private int tenantIdMaxLength = 64;
        
        /**
         * 租户ID正则表达式
         */
        private String tenantIdPattern = "^[a-zA-Z0-9_-]+$";
        
        /**
         * 是否启用审计日志
         */
        private boolean auditLog = true;
        
        /**
         * 审计日志保留天数
         */
        private int auditLogRetentionDays = 90;
    }
}
