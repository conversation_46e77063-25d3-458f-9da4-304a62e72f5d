package com.rega.erp.common.db.tenant.domain;

/**
 * 数据源类型枚举
 *
 * <AUTHOR>
 */
public enum DataSourceType {
    
    /**
     * 主数据源 - 默认共享数据源
     */
    PRIMARY("primary", "主数据源", "系统默认的共享数据源"),
    
    /**
     * 专用数据源 - 为特定租户群体创建的数据源
     */
    DEDICATED("dedicated", "专用数据源", "为特定租户群体创建的专用数据源");
    
    /**
     * 类型代码
     */
    private final String code;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 类型描述
     */
    private final String description;
    
    DataSourceType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取类型
     */
    public static DataSourceType fromCode(String code) {
        for (DataSourceType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown data source type code: " + code);
    }
}
