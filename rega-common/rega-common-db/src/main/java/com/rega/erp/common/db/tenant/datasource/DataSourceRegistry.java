package com.rega.erp.common.db.tenant.datasource;

import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.util.StringUtils;
import com.rega.erp.common.db.tenant.domain.DataSourceInfo;
import com.rega.erp.common.db.tenant.domain.DataSourceStatus;
import com.rega.erp.common.db.tenant.domain.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据源注册表
 * 负责数据源的注册、查询、更新和删除
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DataSourceRegistry {
    
    /**
     * 数据源实例缓存
     * Key: dataSourceId, Value: DataSource
     */
    private final Map<String, DataSource> dataSourceCache = new ConcurrentHashMap<>();
    
    /**
     * 数据源信息缓存
     * Key: dataSourceId, Value: DataSourceInfo
     */
    private final Map<String, DataSourceInfo> dataSourceInfoCache = new ConcurrentHashMap<>();
    
    /**
     * 注册数据源
     */
    public void registerDataSource(String dataSourceId, DataSource dataSource, DataSourceInfo dataSourceInfo) {
        if (StringUtils.isBlank(dataSourceId)) {
            throw new BusinessException("datasource.id.required");
        }
        
        if (dataSource == null) {
            throw new BusinessException("datasource.instance.null");
        }
        
        if (dataSourceInfo == null) {
            throw new BusinessException("datasource.info.null");
        }
        
        // 检查数据源是否已存在
        if (dataSourceCache.containsKey(dataSourceId)) {
            throw new BusinessException("业务异常");
        }
        
        // 设置数据源ID
        dataSourceInfo.setDataSourceId(dataSourceId);
        
        // 设置创建时间
        if (dataSourceInfo.getCreateTime() == null) {
            dataSourceInfo.setCreateTime(LocalDateTime.now());
        }
        
        // 设置更新时间
        dataSourceInfo.setUpdateTime(LocalDateTime.now());
        
        // 设置默认状态
        if (dataSourceInfo.getStatus() == null) {
            dataSourceInfo.setStatus(DataSourceStatus.ACTIVE);
        }
        
        // 初始化租户数量
        if (dataSourceInfo.getCurrentTenants() == null) {
            dataSourceInfo.setCurrentTenants(dataSourceInfo.getTenantCount());
        }
        
        // 注册到缓存
        dataSourceCache.put(dataSourceId, dataSource);
        dataSourceInfoCache.put(dataSourceId, dataSourceInfo);
        
        log.info("数据源注册成功: dataSourceId={}, type={}, maxTenants={}", 
                dataSourceId, dataSourceInfo.getType(), dataSourceInfo.getMaxTenants());
    }
    
    /**
     * 获取数据源实例
     */
    public DataSource getDataSource(String dataSourceId) {
        if (StringUtils.isBlank(dataSourceId)) {
            return null;
        }
        
        return dataSourceCache.get(dataSourceId);
    }
    
    /**
     * 获取数据源信息
     */
    public DataSourceInfo getDataSourceInfo(String dataSourceId) {
        if (StringUtils.isBlank(dataSourceId)) {
            return null;
        }
        
        return dataSourceInfoCache.get(dataSourceId);
    }
    
    /**
     * 更新数据源信息
     */
    public void updateDataSource(DataSourceInfo dataSourceInfo) {
        if (dataSourceInfo == null || StringUtils.isBlank(dataSourceInfo.getDataSourceId())) {
            throw new BusinessException("datasource.info.invalid");
        }
        
        String dataSourceId = dataSourceInfo.getDataSourceId();
        DataSourceInfo existingInfo = dataSourceInfoCache.get(dataSourceId);
        
        if (existingInfo == null) {
            throw new BusinessException("业务异常");
        }
        
        // 更新时间
        dataSourceInfo.setUpdateTime(LocalDateTime.now());
        
        // 保留创建时间
        if (dataSourceInfo.getCreateTime() == null) {
            dataSourceInfo.setCreateTime(existingInfo.getCreateTime());
        }
        
        // 更新租户数量
        dataSourceInfo.setCurrentTenants(dataSourceInfo.getTenantCount());
        
        // 更新缓存
        dataSourceInfoCache.put(dataSourceId, dataSourceInfo);
        
        log.info("数据源信息更新成功: dataSourceId={}", dataSourceId);
    }
    
    /**
     * 删除数据源
     */
    public void removeDataSource(String dataSourceId) {
        if (StringUtils.isBlank(dataSourceId)) {
            throw new BusinessException("datasource.id.required");
        }
        
        DataSource dataSource = dataSourceCache.remove(dataSourceId);
        DataSourceInfo dataSourceInfo = dataSourceInfoCache.remove(dataSourceId);
        
        if (dataSource != null) {
            // 关闭数据源连接
            try {
                if (dataSource instanceof AutoCloseable) {
                    ((AutoCloseable) dataSource).close();
                }
            } catch (Exception e) {
                log.warn("关闭数据源失败: dataSourceId={}", dataSourceId, e);
            }
            
            log.info("数据源删除成功: dataSourceId={}", dataSourceId);
        }
    }
    
    /**
     * 检查数据源是否存在
     */
    public boolean existsDataSource(String dataSourceId) {
        return StringUtils.isNotBlank(dataSourceId) && dataSourceCache.containsKey(dataSourceId);
    }
    
    /**
     * 检查数据源是否可用
     */
    public boolean isDataSourceAvailable(String dataSourceId) {
        DataSourceInfo info = getDataSourceInfo(dataSourceId);
        return info != null && info.isActive();
    }
    
    /**
     * 获取所有数据源信息
     */
    public Collection<DataSourceInfo> getAllDataSources() {
        return dataSourceInfoCache.values();
    }
    
    /**
     * 获取专用数据源列表
     */
    public List<DataSourceInfo> getDedicatedDataSources() {
        return dataSourceInfoCache.values().stream()
                .filter(info -> info.getType() == DataSourceType.DEDICATED)
                .filter(info -> info.getStatus() == DataSourceStatus.ACTIVE)
                .toList();
    }
    
    /**
     * 获取主数据源
     */
    public DataSource getPrimaryDataSource() {
        return dataSourceInfoCache.values().stream()
                .filter(info -> info.getType() == DataSourceType.PRIMARY)
                .filter(info -> info.getStatus() == DataSourceStatus.ACTIVE)
                .findFirst()
                .map(info -> dataSourceCache.get(info.getDataSourceId()))
                .orElse(null);
    }
    
    /**
     * 获取可用的专用数据源（还能添加租户的）
     */
    public List<DataSourceInfo> getAvailableDedicatedDataSources() {
        return getDedicatedDataSources().stream()
                .filter(DataSourceInfo::canAddMoreTenants)
                .toList();
    }
    
    /**
     * 根据租户ID查找数据源
     */
    public DataSourceInfo findDataSourceByTenant(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return null;
        }
        
        return dataSourceInfoCache.values().stream()
                .filter(info -> info.getTenantIds().contains(tenantId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取数据源数量
     */
    public int getDataSourceCount() {
        return dataSourceCache.size();
    }
    
    /**
     * 获取活跃数据源数量
     */
    public long getActiveDataSourceCount() {
        return dataSourceInfoCache.values().stream()
                .filter(info -> info.getStatus() == DataSourceStatus.ACTIVE)
                .count();
    }
    
    /**
     * 清空所有数据源（谨慎使用）
     */
    public void clearAll() {
        // 关闭所有数据源连接
        dataSourceCache.values().forEach(dataSource -> {
            try {
                if (dataSource instanceof AutoCloseable) {
                    ((AutoCloseable) dataSource).close();
                }
            } catch (Exception e) {
                log.warn("关闭数据源失败", e);
            }
        });
        
        dataSourceCache.clear();
        dataSourceInfoCache.clear();
        log.warn("所有数据源已清空");
    }
}
