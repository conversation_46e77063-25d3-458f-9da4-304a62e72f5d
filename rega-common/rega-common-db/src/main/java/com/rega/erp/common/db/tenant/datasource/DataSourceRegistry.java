package com.rega.erp.common.db.tenant.datasource;

import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.util.StringUtils;
import com.rega.erp.common.db.tenant.domain.SysDataSource;
import com.rega.erp.common.db.tenant.domain.SysTenantDataSource;
import com.rega.erp.common.db.tenant.service.DataSourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据源注册表
 * 负责数据源的注册、查询、更新和删除（基于数据库存储）
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class DataSourceRegistry {

    @Autowired
    private DataSourceService dataSourceService;

    /**
     * 数据源实例缓存
     * Key: datasourceKey, Value: DataSource
     */
    private final Map<String, DataSource> dataSourceCache = new ConcurrentHashMap<>();

    /**
     * 数据源配置缓存
     * Key: datasourceKey, Value: SysDataSource
     */
    private final Map<String, SysDataSource> dataSourceConfigCache = new ConcurrentHashMap<>();
    
    /**
     * 注册数据源（基于数据库配置）
     */
    public void registerDataSource(SysDataSource sysDataSource) {
        if (sysDataSource == null) {
            throw new BusinessException("数据源配置不能为空");
        }

        if (StringUtils.isBlank(sysDataSource.getDatasourceKey())) {
            throw new BusinessException("数据源标识键不能为空");
        }

        // 检查数据源是否已存在
        if (dataSourceCache.containsKey(sysDataSource.getDatasourceKey())) {
            throw new BusinessException("数据源已存在: " + sysDataSource.getDatasourceKey());
        }

        try {
            // 创建数据源实例
            DataSource dataSource = dataSourceService.createDataSourceInstance(sysDataSource);

            // 注册到缓存
            dataSourceCache.put(sysDataSource.getDatasourceKey(), dataSource);
            dataSourceConfigCache.put(sysDataSource.getDatasourceKey(), sysDataSource);

            log.info("数据源注册成功: key={}, name={}, type={}",
                    sysDataSource.getDatasourceKey(),
                    sysDataSource.getDatasourceName(),
                    sysDataSource.getDatasourceType());
        } catch (Exception e) {
            log.error("数据源注册失败: key={}", sysDataSource.getDatasourceKey(), e);
            throw new BusinessException("数据源注册失败: " + e.getMessage());
        }
    }

    /**
     * 注册数据源（直接提供实例）
     */
    public void registerDataSource(String datasourceKey, DataSource dataSource, SysDataSource sysDataSource) {
        if (StringUtils.isBlank(datasourceKey)) {
            throw new BusinessException("数据源标识键不能为空");
        }

        if (dataSource == null) {
            throw new BusinessException("数据源实例不能为空");
        }

        if (sysDataSource == null) {
            throw new BusinessException("数据源配置不能为空");
        }

        // 检查数据源是否已存在
        if (dataSourceCache.containsKey(datasourceKey)) {
            throw new BusinessException("数据源已存在: " + datasourceKey);
        }

        // 注册到缓存
        dataSourceCache.put(datasourceKey, dataSource);
        dataSourceConfigCache.put(datasourceKey, sysDataSource);

        log.info("数据源注册成功: key={}, name={}", datasourceKey, sysDataSource.getDatasourceName());
    }
    
    /**
     * 获取数据源实例
     */
    public DataSource getDataSource(String datasourceKey) {
        if (StringUtils.isBlank(datasourceKey)) {
            return null;
        }

        // 先从缓存获取
        DataSource dataSource = dataSourceCache.get(datasourceKey);
        if (dataSource != null) {
            return dataSource;
        }

        // 缓存中没有，从数据库加载
        SysDataSource sysDataSource = dataSourceService.getDataSourceByKey(datasourceKey);
        if (sysDataSource != null && sysDataSource.getStatus() == 1) {
            try {
                dataSource = dataSourceService.createDataSourceInstance(sysDataSource);
                dataSourceCache.put(datasourceKey, dataSource);
                dataSourceConfigCache.put(datasourceKey, sysDataSource);
                return dataSource;
            } catch (Exception e) {
                log.error("创建数据源实例失败: key={}", datasourceKey, e);
            }
        }

        return null;
    }

    /**
     * 获取数据源配置
     */
    public SysDataSource getDataSourceConfig(String datasourceKey) {
        if (StringUtils.isBlank(datasourceKey)) {
            return null;
        }

        // 先从缓存获取
        SysDataSource config = dataSourceConfigCache.get(datasourceKey);
        if (config != null) {
            return config;
        }

        // 缓存中没有，从数据库加载
        config = dataSourceService.getDataSourceByKey(datasourceKey);
        if (config != null) {
            dataSourceConfigCache.put(datasourceKey, config);
        }

        return config;
    }

    /**
     * 更新数据源配置
     */
    public void updateDataSource(SysDataSource sysDataSource) {
        if (sysDataSource == null || StringUtils.isBlank(sysDataSource.getDatasourceKey())) {
            throw new BusinessException("数据源配置无效");
        }

        String datasourceKey = sysDataSource.getDatasourceKey();

        try {
            // 更新数据库
            boolean success = dataSourceService.updateDataSource(sysDataSource);
            if (!success) {
                throw new BusinessException("更新数据源配置失败");
            }

            // 更新缓存
            dataSourceConfigCache.put(datasourceKey, sysDataSource);

            // 重新创建数据源实例
            DataSource oldDataSource = dataSourceCache.get(datasourceKey);
            if (oldDataSource != null) {
                DataSource newDataSource = dataSourceService.createDataSourceInstance(sysDataSource);
                dataSourceCache.put(datasourceKey, newDataSource);

                // 关闭旧的数据源
                closeDataSource(oldDataSource);
            }

            log.info("数据源配置更新成功: key={}", datasourceKey);
        } catch (Exception e) {
            log.error("更新数据源配置失败: key={}", datasourceKey, e);
            throw new BusinessException("更新数据源配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除数据源
     */
    public void removeDataSource(String datasourceKey) {
        if (StringUtils.isBlank(datasourceKey)) {
            throw new BusinessException("数据源标识键不能为空");
        }

        try {
            // 从数据库删除配置
            SysDataSource config = dataSourceConfigCache.get(datasourceKey);
            if (config != null) {
                boolean success = dataSourceService.deleteDataSource(config.getId());
                if (!success) {
                    throw new BusinessException("删除数据源配置失败");
                }
            }

            // 从缓存移除
            DataSource dataSource = dataSourceCache.remove(datasourceKey);
            dataSourceConfigCache.remove(datasourceKey);

            // 关闭数据源连接
            if (dataSource != null) {
                closeDataSource(dataSource);
            }

            log.info("数据源删除成功: key={}", datasourceKey);
        } catch (Exception e) {
            log.error("删除数据源失败: key={}", datasourceKey, e);
            throw new BusinessException("删除数据源失败: " + e.getMessage());
        }
    }

    /**
     * 关闭数据源连接
     */
    private void closeDataSource(DataSource dataSource) {
        try {
            if (dataSource instanceof AutoCloseable) {
                ((AutoCloseable) dataSource).close();
            }
        } catch (Exception e) {
            log.warn("关闭数据源失败", e);
        }
    }
    
    /**
     * 检查数据源是否存在
     */
    public boolean existsDataSource(String datasourceKey) {
        if (StringUtils.isBlank(datasourceKey)) {
            return false;
        }

        // 先检查缓存
        if (dataSourceCache.containsKey(datasourceKey)) {
            return true;
        }

        // 检查数据库
        SysDataSource config = dataSourceService.getDataSourceByKey(datasourceKey);
        return config != null;
    }

    /**
     * 检查数据源是否可用
     */
    public boolean isDataSourceAvailable(String datasourceKey) {
        SysDataSource config = getDataSourceConfig(datasourceKey);
        return config != null && config.getStatus() == 1;
    }

    /**
     * 获取所有数据源配置
     */
    public List<SysDataSource> getAllDataSources() {
        return dataSourceService.getAllDataSources();
    }

    /**
     * 获取主数据源
     */
    public DataSource getPrimaryDataSource() {
        List<SysDataSource> allDataSources = dataSourceService.getAllDataSources();
        return allDataSources.stream()
                .filter(ds -> ds.isDefaultDataSource() && ds.getStatus() == 1)
                .findFirst()
                .map(ds -> getDataSource(ds.getDatasourceKey()))
                .orElse(null);
    }
    
    /**
     * 根据租户ID查找数据源
     */
    public SysDataSource findDataSourceByTenant(Long tenantId) {
        if (tenantId == null) {
            return null;
        }

        List<SysTenantDataSource> tenantDataSources = dataSourceService.getTenantDataSources(tenantId);
        if (tenantDataSources.isEmpty()) {
            return null;
        }

        // 优先返回主数据源
        SysTenantDataSource primaryTenantDs = tenantDataSources.stream()
                .filter(SysTenantDataSource::isPrimaryDataSource)
                .findFirst()
                .orElse(tenantDataSources.get(0));

        return dataSourceService.getDataSourceById(primaryTenantDs.getDatasourceId());
    }

    /**
     * 获取租户的数据源实例
     */
    public DataSource getTenantDataSource(Long tenantId) {
        SysDataSource config = findDataSourceByTenant(tenantId);
        if (config != null) {
            return getDataSource(config.getDatasourceKey());
        }
        return null;
    }

    /**
     * 获取数据源数量
     */
    public int getDataSourceCount() {
        return dataSourceService.getAllDataSources().size();
    }

    /**
     * 获取活跃数据源数量
     */
    public long getActiveDataSourceCount() {
        return dataSourceService.getAllDataSources().stream()
                .filter(ds -> ds.getStatus() == 1)
                .count();
    }

    /**
     * 刷新缓存
     */
    public void refreshCache() {
        log.info("开始刷新数据源缓存");

        // 清空当前缓存
        dataSourceConfigCache.clear();

        // 重新加载所有数据源配置
        List<SysDataSource> allDataSources = dataSourceService.getAllDataSources();
        for (SysDataSource config : allDataSources) {
            if (config.getStatus() == 1) {
                dataSourceConfigCache.put(config.getDatasourceKey(), config);

                // 如果缓存中有实例，重新创建
                if (dataSourceCache.containsKey(config.getDatasourceKey())) {
                    try {
                        DataSource oldDataSource = dataSourceCache.get(config.getDatasourceKey());
                        DataSource newDataSource = dataSourceService.createDataSourceInstance(config);
                        dataSourceCache.put(config.getDatasourceKey(), newDataSource);
                        closeDataSource(oldDataSource);
                    } catch (Exception e) {
                        log.error("重新创建数据源实例失败: key={}", config.getDatasourceKey(), e);
                    }
                }
            }
        }

        log.info("数据源缓存刷新完成，共加载 {} 个数据源配置", dataSourceConfigCache.size());
    }

    /**
     * 清空所有数据源（谨慎使用）
     */
    public void clearAll() {
        // 关闭所有数据源连接
        dataSourceCache.values().forEach(this::closeDataSource);

        dataSourceCache.clear();
        dataSourceConfigCache.clear();
        log.warn("所有数据源缓存已清空");
    }
}
