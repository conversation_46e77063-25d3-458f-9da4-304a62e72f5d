package com.rega.erp.common.db.tenant.domain;

import com.rega.erp.common.core.domain.TenantBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据源配置实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysDataSource extends TenantBaseEntity {

    /**
     * 数据源名称
     */
    private String datasourceName;

    /**
     * 数据源标识键
     */
    private String datasourceKey;

    /**
     * 数据源类型：postgresql, mysql, oracle等
     */
    private String datasourceType;

    /**
     * 驱动类名
     */
    private String driverClassName;

    /**
     * 数据库连接URL
     */
    private String url;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码（加密存储）
     */
    private String password;

    /**
     * 初始连接数
     */
    private Integer initialSize;

    /**
     * 最小空闲连接数
     */
    private Integer minIdle;

    /**
     * 最大活跃连接数
     */
    private Integer maxActive;

    /**
     * 获取连接最大等待时间（毫秒）
     */
    private Long maxWait;

    /**
     * 获取连接时是否测试
     */
    private Boolean testOnBorrow;

    /**
     * 归还连接时是否测试
     */
    private Boolean testOnReturn;

    /**
     * 空闲时是否测试连接
     */
    private Boolean testWhileIdle;

    /**
     * 验证查询SQL
     */
    private String validationQuery;

    /**
     * 空闲连接回收器运行间隔（毫秒）
     */
    private Long timeBetweenEvictionRunsMillis;

    /**
     * 连接在池中保持空闲的最小时间（毫秒）
     */
    private Long minEvictableIdleTimeMillis;

    /**
     * 是否缓存PreparedStatement
     */
    private Boolean poolPreparedStatements;

    /**
     * 每个连接上PSCache的大小
     */
    private Integer maxPoolPreparedStatementPerConnectionSize;

    /**
     * 连接属性（JSON格式）
     */
    private String connectionProperties;

    /**
     * 是否默认数据源
     */
    private Boolean isDefault;

    /**
     * 是否启用健康检查
     */
    private Boolean healthCheckEnabled;

    /**
     * 健康检查间隔（毫秒）
     */
    private Long healthCheckInterval;

    /**
     * 连接失败最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 数据源描述
     */
    private String description;

    /**
     * 检查是否为默认数据源
     */
    public boolean isDefaultDataSource() {
        return Boolean.TRUE.equals(isDefault);
    }

    /**
     * 检查是否启用健康检查
     */
    public boolean isHealthCheckEnabled() {
        return Boolean.TRUE.equals(healthCheckEnabled);
    }

    /**
     * 检查是否启用连接测试
     */
    public boolean isTestOnBorrow() {
        return Boolean.TRUE.equals(testOnBorrow);
    }

    /**
     * 检查是否启用归还测试
     */
    public boolean isTestOnReturn() {
        return Boolean.TRUE.equals(testOnReturn);
    }

    /**
     * 检查是否启用空闲测试
     */
    public boolean isTestWhileIdle() {
        return Boolean.TRUE.equals(testWhileIdle);
    }

    /**
     * 检查是否启用PreparedStatement缓存
     */
    public boolean isPoolPreparedStatements() {
        return Boolean.TRUE.equals(poolPreparedStatements);
    }

    /**
     * 获取安全的密码显示（用于日志等）
     */
    public String getMaskedPassword() {
        if (password == null || password.length() <= 4) {
            return "****";
        }
        return password.substring(0, 2) + "****" + password.substring(password.length() - 2);
    }

    /**
     * 获取数据源显示名称
     */
    public String getDisplayName() {
        return datasourceName + " (" + datasourceKey + ")";
    }

    /**
     * 检查数据源配置是否完整
     */
    public boolean isConfigComplete() {
        return datasourceKey != null && !datasourceKey.trim().isEmpty()
                && datasourceType != null && !datasourceType.trim().isEmpty()
                && driverClassName != null && !driverClassName.trim().isEmpty()
                && url != null && !url.trim().isEmpty()
                && username != null && !username.trim().isEmpty()
                && password != null && !password.trim().isEmpty();
    }

    /**
     * 获取连接池配置摘要
     */
    public String getPoolConfigSummary() {
        return String.format("初始:%d, 最小:%d, 最大:%d, 超时:%dms",
                initialSize != null ? initialSize : 0,
                minIdle != null ? minIdle : 0,
                maxActive != null ? maxActive : 0,
                maxWait != null ? maxWait : 0);
    }
}
