package com.rega.erp.common.db.tenant.datasource;

import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.util.StringUtils;
import com.rega.erp.common.db.tenant.domain.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 租户数据源管理器
 * 负责租户数据源的分配、创建和管理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TenantDataSourceManager {
    
    private final DataSourceRegistry dataSourceRegistry;
    private final DataSourceFactory dataSourceFactory;
    
    /**
     * 分配或创建数据源
     */
    public String assignOrCreateDataSource(TenantCreationRequest request) {
        if (request.hasSpecifiedDataSource()) {
            // 使用指定的数据源
            return assignSpecifiedDataSource(request);
        } else if (request.needsNewDataSource()) {
            // 创建新的专用数据源
            return createDedicatedDataSource(request);
        } else {
            // 查找可用的专用数据源
            Optional<DataSourceInfo> availableDataSource = findAvailableDataSource(request);
            
            if (availableDataSource.isPresent()) {
                return assignExistingDataSource(request, availableDataSource.get());
            } else {
                // 没有可用的数据源，创建新的
                return createDedicatedDataSource(request);
            }
        }
    }
    
    /**
     * 分配指定的数据源
     */
    private String assignSpecifiedDataSource(TenantCreationRequest request) {
        String dataSourceId = request.getDataSourceId();
        DataSourceInfo dataSourceInfo = dataSourceRegistry.getDataSourceInfo(dataSourceId);
        
        if (dataSourceInfo == null) {
            throw new BusinessException("datasource.not.found", dataSourceId);
        }
        
        if (!dataSourceInfo.isActive()) {
            throw new BusinessException("datasource.not.active", dataSourceId);
        }
        
        if (!dataSourceInfo.canAddMoreTenants()) {
            throw new BusinessException("datasource.tenant.limit.exceeded", dataSourceId);
        }
        
        // 添加租户到数据源
        dataSourceInfo.addTenant(request.getTenantId());
        dataSourceRegistry.updateDataSource(dataSourceInfo);
        
        log.info("租户分配到指定数据源: tenantId={}, dataSourceId={}", 
                request.getTenantId(), dataSourceId);
        
        return dataSourceId;
    }
    
    /**
     * 查找可用的数据源
     */
    private Optional<DataSourceInfo> findAvailableDataSource(TenantCreationRequest request) {
        return dataSourceRegistry.getAvailableDedicatedDataSources().stream()
                .filter(ds -> matchesRequirements(ds, request))
                .findFirst();
    }
    
    /**
     * 分配现有数据源
     */
    private String assignExistingDataSource(TenantCreationRequest request, DataSourceInfo dataSourceInfo) {
        String dataSourceId = dataSourceInfo.getDataSourceId();
        
        // 添加租户到数据源
        dataSourceInfo.addTenant(request.getTenantId());
        dataSourceRegistry.updateDataSource(dataSourceInfo);
        
        log.info("租户分配到现有数据源: tenantId={}, dataSourceId={}, currentTenants={}", 
                request.getTenantId(), dataSourceId, dataSourceInfo.getCurrentTenants());
        
        return dataSourceId;
    }
    
    /**
     * 创建专用数据源
     */
    private String createDedicatedDataSource(TenantCreationRequest request) {
        String dataSourceId = generateDataSourceId();
        
        // 使用请求中的配置或生成默认配置
        DataSourceConfig config = request.getDataSourceConfig();
        if (config == null) {
            config = generateDefaultDataSourceConfig(request);
        }
        
        // 创建数据源实例
        DataSource dataSource = dataSourceFactory.createDataSource(config);
        
        // 创建数据源信息
        DataSourceInfo dataSourceInfo = buildDataSourceInfo(dataSourceId, request, config);
        dataSourceInfo.addTenant(request.getTenantId());
        
        // 注册数据源
        dataSourceRegistry.registerDataSource(dataSourceId, dataSource, dataSourceInfo);
        
        log.info("创建专用数据源成功: tenantId={}, dataSourceId={}, maxTenants={}", 
                request.getTenantId(), dataSourceId, dataSourceInfo.getMaxTenants());
        
        return dataSourceId;
    }
    
    /**
     * 从数据源中移除租户
     */
    public void removeTenantFromDataSource(String tenantId, String dataSourceId) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(dataSourceId)) {
            return;
        }
        
        DataSourceInfo dataSourceInfo = dataSourceRegistry.getDataSourceInfo(dataSourceId);
        if (dataSourceInfo == null) {
            log.warn("数据源不存在: dataSourceId={}", dataSourceId);
            return;
        }
        
        // 从数据源中移除租户
        boolean removed = dataSourceInfo.removeTenant(tenantId);
        if (removed) {
            dataSourceRegistry.updateDataSource(dataSourceInfo);
            
            log.info("租户从数据源移除: tenantId={}, dataSourceId={}, remainingTenants={}", 
                    tenantId, dataSourceId, dataSourceInfo.getCurrentTenants());
            
            // 如果是专用数据源且没有租户了，考虑删除数据源
            if (dataSourceInfo.isDedicated() && dataSourceInfo.getTenantCount() == 0) {
                log.info("专用数据源无租户使用，标记删除: dataSourceId={}", dataSourceId);
                dataSourceInfo.setStatus(DataSourceStatus.DELETED);
                dataSourceRegistry.updateDataSource(dataSourceInfo);
            }
        }
    }
    
    /**
     * 检查数据源是否满足要求
     */
    private boolean matchesRequirements(DataSourceInfo dataSourceInfo, TenantCreationRequest request) {
        // 检查租户数量限制
        if (!dataSourceInfo.canAddMoreTenants()) {
            return false;
        }
        
        // 检查最大租户数配置
        int requestMaxTenants = request.getMaxTenantsPerDataSourceOrDefault();
        if (dataSourceInfo.getMaxTenants() != null && 
            dataSourceInfo.getMaxTenants() > requestMaxTenants) {
            return false;
        }
        
        // 可以添加更多的匹配条件，如地理位置、性能要求等
        
        return true;
    }
    
    /**
     * 生成数据源ID
     */
    private String generateDataSourceId() {
        return "ds_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString((int) (Math.random() * 0x10000));
    }
    
    /**
     * 生成默认数据源配置
     */
    private DataSourceConfig generateDefaultDataSourceConfig(TenantCreationRequest request) {
        // 这里应该从配置文件或配置服务获取模板配置
        String databaseName = "rega_tenant_" + request.getTenantId();
        String username = "tenant_" + request.getTenantId();
        String password = generatePassword();
        
        return DataSourceConfig.createPostgreSQLConfig(
                "localhost", 5432, databaseName, username, password);
    }
    
    /**
     * 构建数据源信息
     */
    private DataSourceInfo buildDataSourceInfo(String dataSourceId, TenantCreationRequest request, DataSourceConfig config) {
        DataSourceInfo dataSourceInfo = new DataSourceInfo();
        dataSourceInfo.setDataSourceId(dataSourceId);
        dataSourceInfo.setDataSourceName("Dedicated DataSource for " + request.getTenantName());
        dataSourceInfo.setDescription("专用数据源 - " + request.getDescription());
        dataSourceInfo.setConfig(config);
        dataSourceInfo.setType(DataSourceType.DEDICATED);
        dataSourceInfo.setStatus(DataSourceStatus.ACTIVE);
        dataSourceInfo.setMaxTenants(request.getMaxTenantsPerDataSourceOrDefault());
        dataSourceInfo.setCurrentTenants(0);
        dataSourceInfo.setCreateTime(LocalDateTime.now());
        dataSourceInfo.setUpdateTime(LocalDateTime.now());
        dataSourceInfo.setPoolConfig(PoolConfig.createDefault());
        
        return dataSourceInfo;
    }
    
    /**
     * 生成随机密码
     */
    private String generatePassword() {
        // 简单的密码生成，实际应用中应该使用更安全的方式
        return "pwd_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString((int) (Math.random() * 0x10000));
    }
    
    /**
     * 获取租户的数据源
     */
    public DataSource getTenantDataSource(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return null;
        }
        
        DataSourceInfo dataSourceInfo = dataSourceRegistry.findDataSourceByTenant(tenantId);
        if (dataSourceInfo == null) {
            return null;
        }
        
        return dataSourceRegistry.getDataSource(dataSourceInfo.getDataSourceId());
    }
    
    /**
     * 健康检查数据源
     */
    public boolean healthCheck(String dataSourceId) {
        DataSource dataSource = dataSourceRegistry.getDataSource(dataSourceId);
        if (dataSource == null) {
            return false;
        }
        
        try {
            // 简单的连接测试
            dataSource.getConnection().close();
            
            // 更新健康检查时间
            DataSourceInfo dataSourceInfo = dataSourceRegistry.getDataSourceInfo(dataSourceId);
            if (dataSourceInfo != null) {
                dataSourceInfo.setLastHealthCheckTime(LocalDateTime.now());
                dataSourceRegistry.updateDataSource(dataSourceInfo);
            }
            
            return true;
        } catch (Exception e) {
            log.error("数据源健康检查失败: dataSourceId={}", dataSourceId, e);
            return false;
        }
    }
}
