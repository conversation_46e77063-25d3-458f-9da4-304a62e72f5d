package com.rega.erp.common.db.tenant.datasource;

import com.rega.erp.common.core.exception.BusinessException;
import com.rega.erp.common.core.util.StringUtils;
import com.rega.erp.common.db.tenant.cache.DataSourceCacheManager;
import com.rega.erp.common.db.tenant.core.TenantRegistry;
import com.rega.erp.common.db.tenant.domain.SysDataSource;
import com.rega.erp.common.db.tenant.domain.SysTenantDataSource;
import com.rega.erp.common.db.tenant.domain.TenantInfo;
import com.rega.erp.common.db.tenant.service.DataSourceService;
// import com.rega.erp.common.db.util.DbI18nUtils; // 暂时注释掉，等 i18n 模块可用后再启用
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 租户数据源管理器
 * 负责管理租户与数据源的关联关系，支持字段隔离和数据源隔离两种模式
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TenantDataSourceManager {

    private final DataSourceRegistry dataSourceRegistry;
    private final DataSourceService dataSourceService;
    private final TenantRegistry tenantRegistry;
    private final DataSourceCacheManager cacheManager;

    // =====================================================
    // 租户数据源分配管理
    // =====================================================

    /**
     * 为租户分配数据源
     *
     * @param tenantId 租户ID
     * @param isolationType 隔离类型：1-字段隔离，2-数据源隔离
     * @param datasourceId 指定的数据源ID（可选）
     * @return 分配的数据源键
     */
    public String assignDataSourceToTenant(Long tenantId, Integer isolationType, Long datasourceId) {
        if (tenantId == null) {
            throw new BusinessException("租户ID不能为空");
        }

        if (isolationType == null || (isolationType != 1 && isolationType != 2)) {
            throw new BusinessException("隔离类型无效，必须是1（字段隔离）或2（数据源隔离）");
        }

        try {
            String assignedDataSourceKey;

            if (isolationType == 1) {
                // 字段隔离：使用主数据源
                assignedDataSourceKey = assignFieldIsolationDataSource(tenantId, datasourceId);
            } else {
                // 数据源隔离：分配专用数据源
                assignedDataSourceKey = assignDataSourceIsolationDataSource(tenantId, datasourceId);
            }

            // 更新缓存
            cacheManager.incrementDataSourceUsage(assignedDataSourceKey, 1);
            // 清除租户数据源缓存，下次查询时会重新加载
            cacheManager.evictTenantDataSources(tenantId);

            log.info("租户数据源分配成功: tenantId={}, isolationType={}, dataSourceKey={}", 
                    tenantId, isolationType, assignedDataSourceKey);

            return assignedDataSourceKey;

        } catch (Exception e) {
            log.error("租户数据源分配失败: tenantId={}, isolationType={}", tenantId, isolationType, e);
            throw new BusinessException("租户数据源分配失败: " + e.getMessage());
        }
    }

    /**
     * 为租户分配字段隔离数据源
     */
    private String assignFieldIsolationDataSource(Long tenantId, Long datasourceId) {
        SysDataSource targetDataSource;

        if (datasourceId != null) {
            // 使用指定的数据源
            targetDataSource = dataSourceService.getDataSourceById(datasourceId);
            if (targetDataSource == null) {
                throw new BusinessException("指定的数据源不存在: " + datasourceId);
            }
        } else {
            // 使用主数据源
            targetDataSource = getPrimaryDataSource();
            if (targetDataSource == null) {
                throw new BusinessException("未找到可用的主数据源");
            }
        }

        // 创建租户数据源关联
        SysTenantDataSource tenantDataSource = SysTenantDataSource.createFieldIsolation(
                tenantId, targetDataSource.getId(), true);

        boolean success = dataSourceService.assignDataSourceToTenant(tenantDataSource);
        if (!success) {
            throw new BusinessException("创建租户数据源关联失败");
        }

        return targetDataSource.getDatasourceKey();
    }

    /**
     * 为租户分配数据源隔离数据源
     */
    private String assignDataSourceIsolationDataSource(Long tenantId, Long datasourceId) {
        SysDataSource targetDataSource;

        if (datasourceId != null) {
            // 使用指定的数据源
            targetDataSource = dataSourceService.getDataSourceById(datasourceId);
            if (targetDataSource == null) {
                throw new BusinessException("指定的数据源不存在: " + datasourceId);
            }
        } else {
            // 查找可用的专用数据源或创建新的
            targetDataSource = findOrCreateDedicatedDataSource(tenantId);
        }

        // 创建租户数据源关联
        SysTenantDataSource tenantDataSource = SysTenantDataSource.createDataSourceIsolation(
                tenantId, targetDataSource.getId(), true);

        boolean success = dataSourceService.assignDataSourceToTenant(tenantDataSource);
        if (!success) {
            throw new BusinessException("创建租户数据源关联失败");
        }

        return targetDataSource.getDatasourceKey();
    }

    /**
     * 查找或创建专用数据源
     */
    private SysDataSource findOrCreateDedicatedDataSource(Long tenantId) {
        // 首先尝试查找可用的专用数据源
        List<SysDataSource> availableDataSources = findAvailableDedicatedDataSources();
        
        if (!availableDataSources.isEmpty()) {
            // 选择使用率最低的数据源
            SysDataSource selectedDataSource = selectLeastUsedDataSource(availableDataSources);
            log.info("为租户分配现有专用数据源: tenantId={}, dataSourceKey={}", 
                    tenantId, selectedDataSource.getDatasourceKey());
            return selectedDataSource;
        }

        // 如果没有可用的专用数据源，创建新的
        return createDedicatedDataSourceForTenant(tenantId);
    }

    /**
     * 查找可用的专用数据源
     */
    private List<SysDataSource> findAvailableDedicatedDataSources() {
        List<SysDataSource> allDataSources = dataSourceService.getAllDataSources();
        return allDataSources.stream()
                .filter(ds -> ds.getStatus() == 1) // 启用状态
                .filter(ds -> !ds.isDefaultDataSource()) // 非主数据源
                .filter(this::isDataSourceAvailableForNewTenant) // 有容量接受新租户
                .toList();
    }

    /**
     * 检查数据源是否有容量接受新租户（使用缓存）
     */
    private boolean isDataSourceAvailableForNewTenant(SysDataSource dataSource) {
        String dataSourceKey = dataSource.getDatasourceKey();
        Integer currentUsage = cacheManager.getDataSourceUsage(dataSourceKey, key -> 0);

        // 假设每个数据源最多支持100个租户（可配置）
        int maxTenantsPerDataSource = 100;
        return currentUsage < maxTenantsPerDataSource;
    }

    /**
     * 选择使用率最低的数据源（使用缓存）
     */
    private SysDataSource selectLeastUsedDataSource(List<SysDataSource> dataSources) {
        return dataSources.stream()
                .min((ds1, ds2) -> {
                    int usage1 = cacheManager.getDataSourceUsage(ds1.getDatasourceKey(), key -> 0);
                    int usage2 = cacheManager.getDataSourceUsage(ds2.getDatasourceKey(), key -> 0);
                    return Integer.compare(usage1, usage2);
                })
                .orElse(dataSources.get(0));
    }

    /**
     * 为租户创建专用数据源
     */
    private SysDataSource createDedicatedDataSourceForTenant(Long tenantId) {
        // 基于主数据源配置创建新的专用数据源
        SysDataSource primaryDataSource = getPrimaryDataSource();
        if (primaryDataSource == null) {
            throw new BusinessException("未找到主数据源配置模板");
        }

        SysDataSource dedicatedDataSource = createDataSourceFromTemplate(primaryDataSource, tenantId);
        
        // 保存到数据库
        Long dataSourceId = dataSourceService.createDataSource(dedicatedDataSource);
        dedicatedDataSource.setId(dataSourceId);

        // 注册到数据源注册表
        dataSourceRegistry.registerDataSource(dedicatedDataSource);

        log.info("为租户创建专用数据源: tenantId={}, dataSourceKey={}", 
                tenantId, dedicatedDataSource.getDatasourceKey());

        return dedicatedDataSource;
    }

    /**
     * 基于模板创建数据源配置
     */
    private SysDataSource createDataSourceFromTemplate(SysDataSource template, Long tenantId) {
        SysDataSource newDataSource = new SysDataSource();
        
        // 复制基本配置
        newDataSource.setDatasourceName("租户专用数据源-" + tenantId);
        newDataSource.setDatasourceKey("tenant_" + tenantId + "_" + System.currentTimeMillis());
        newDataSource.setDatasourceType(template.getDatasourceType());
        newDataSource.setDriverClassName(template.getDriverClassName());
        
        // 修改URL以创建独立的数据库
        String newUrl = generateTenantSpecificUrl(template.getUrl(), tenantId);
        newDataSource.setUrl(newUrl);
        
        newDataSource.setUsername(template.getUsername());
        newDataSource.setPassword(template.getPassword());
        
        // 复制连接池配置
        newDataSource.setInitialSize(template.getInitialSize());
        newDataSource.setMinIdle(template.getMinIdle());
        newDataSource.setMaxActive(template.getMaxActive());
        newDataSource.setMaxWait(template.getMaxWait());
        newDataSource.setTestOnBorrow(template.getTestOnBorrow());
        newDataSource.setTestOnReturn(template.getTestOnReturn());
        newDataSource.setTestWhileIdle(template.getTestWhileIdle());
        newDataSource.setValidationQuery(template.getValidationQuery());
        
        // 设置为非默认数据源
        newDataSource.setIsDefault(false);
        newDataSource.setStatus(1);
        newDataSource.setHealthCheckEnabled(true);
        newDataSource.setHealthCheckInterval(300000L);
        newDataSource.setMaxRetryCount(3);
        newDataSource.setDescription("租户 " + tenantId + " 的专用数据源");
        newDataSource.setCreateTime(LocalDateTime.now());
        newDataSource.setUpdateTime(LocalDateTime.now());
        
        return newDataSource;
    }

    /**
     * 生成租户专用的数据库URL
     */
    private String generateTenantSpecificUrl(String templateUrl, Long tenantId) {
        // 这里可以根据实际需求修改URL生成逻辑
        // 例如：为每个租户创建独立的数据库
        if (templateUrl.contains("postgresql")) {
            return templateUrl.replaceAll("/([^/]+)\\?", "/tenant_" + tenantId + "_db?");
        } else if (templateUrl.contains("mysql")) {
            return templateUrl.replaceAll("/([^/]+)\\?", "/tenant_" + tenantId + "_db?");
        }
        
        // 默认情况下，在数据库名后添加租户标识
        return templateUrl.replace("?", "_tenant_" + tenantId + "?");
    }

    // =====================================================
    // 租户数据源查询和管理
    // =====================================================

    /**
     * 获取租户的数据源（使用缓存）
     *
     * @param tenantId 租户ID
     * @return 数据源实例
     */
    public DataSource getTenantDataSource(Long tenantId) {
        if (tenantId == null) {
            return null;
        }

        // 从缓存获取租户数据源关联
        List<SysTenantDataSource> tenantDataSources = cacheManager.getTenantDataSources(
                tenantId, dataSourceService::getTenantDataSources);

        if (!tenantDataSources.isEmpty()) {
            // 获取主数据源
            SysTenantDataSource primaryTenantDs = tenantDataSources.stream()
                    .filter(SysTenantDataSource::isPrimaryDataSource)
                    .findFirst()
                    .orElse(tenantDataSources.get(0));

            // 从缓存获取数据源配置
            SysDataSource dataSourceConfig = cacheManager.getDataSourceConfig(
                    primaryTenantDs.getDatasourceId(), dataSourceService::getDataSourceById);

            if (dataSourceConfig != null) {
                return dataSourceRegistry.getDataSource(dataSourceConfig.getDatasourceKey());
            }
        }

        // 如果租户没有分配数据源，使用主数据源
        log.warn("租户未分配数据源，使用主数据源: tenantId={}", tenantId);
        return dataSourceRegistry.getPrimaryDataSource();
    }

    /**
     * 获取租户的数据源配置
     *
     * @param tenantId 租户ID
     * @return 数据源配置
     */
    public SysDataSource getTenantDataSourceConfig(Long tenantId) {
        return dataSourceRegistry.findDataSourceByTenant(tenantId);
    }

    /**
     * 移除租户的数据源关联（使用缓存）
     *
     * @param tenantId 租户ID
     * @param datasourceId 数据源ID
     * @return 是否移除成功
     */
    public boolean removeTenantFromDataSource(Long tenantId, Long datasourceId) {
        try {
            // 先获取数据源配置以获取数据源键
            SysDataSource dataSourceConfig = cacheManager.getDataSourceConfig(
                    datasourceId, dataSourceService::getDataSourceById);

            // 移除数据库中的关联
            boolean success = dataSourceService.removeDataSourceFromTenant(tenantId, datasourceId);

            if (success) {
                // 清理缓存
                cacheManager.evictTenantDataSources(tenantId);

                // 减少数据源使用计数
                if (dataSourceConfig != null) {
                    cacheManager.decrementDataSourceUsage(dataSourceConfig.getDatasourceKey(), 1);
                }

                log.info("租户数据源关联移除成功: tenantId={}, datasourceId={}", tenantId, datasourceId);
            }

            return success;
        } catch (Exception e) {
            log.error("移除租户数据源关联失败: tenantId={}, datasourceId={}", tenantId, datasourceId, e);
            return false;
        }
    }

    // =====================================================
    // 辅助方法
    // =====================================================

    /**
     * 获取主数据源（使用缓存）
     */
    private SysDataSource getPrimaryDataSource() {
        // 使用缓存查找主数据源
        List<SysDataSource> allDataSources = dataSourceService.getAllDataSources();
        return allDataSources.stream()
                .filter(SysDataSource::isDefaultDataSource)
                .filter(ds -> ds.getStatus() == 1)
                .findFirst()
                .map(ds -> cacheManager.getDataSourceConfig(ds.getId(), dataSourceService::getDataSourceById))
                .orElse(null);
    }

    /**
     * 刷新缓存（使用缓存管理器）
     */
    public void refreshCache() {
        log.info("开始刷新租户数据源缓存");

        // 清空所有数据源相关缓存
        cacheManager.clearAllDataSourceCache();

        log.info("租户数据源缓存刷新完成");
    }

    /**
     * 获取数据源使用统计（从缓存）
     */
    public Map<String, Integer> getDataSourceUsageStatistics() {
        return cacheManager.getAllDataSourceUsage();
    }

    /**
     * 检查数据源健康状态（使用缓存）
     */
    public boolean checkDataSourceHealth(String dataSourceKey) {
        try {
            // 先从缓存获取健康状态
            Boolean cachedHealth = cacheManager.getDataSourceHealth(dataSourceKey);
            if (cachedHealth != null) {
                return cachedHealth;
            }

            // 缓存中没有，执行实际健康检查
            DataSource dataSource = dataSourceRegistry.getDataSource(dataSourceKey);
            if (dataSource == null) {
                cacheManager.cacheDataSourceHealth(dataSourceKey, false);
                return false;
            }

            // 执行健康检查：尝试获取连接并测试
            boolean isHealthy = dataSourceService.testDataSourceConnection(
                    cacheManager.getDataSourceConfigByKey(dataSourceKey, dataSourceService::getDataSourceByKey));

            // 缓存健康状态
            cacheManager.cacheDataSourceHealth(dataSourceKey, isHealthy);

            return isHealthy;
        } catch (Exception e) {
            log.error("数据源健康检查失败: dataSourceKey={}", dataSourceKey, e);
            cacheManager.cacheDataSourceHealth(dataSourceKey, false);
            return false;
        }
    }
}
