package com.rega.erp.common.db.tenant.config;

import com.rega.erp.common.db.tenant.core.TenantManager;
import com.rega.erp.common.db.tenant.core.TenantRegistry;
import com.rega.erp.common.db.tenant.datasource.DataSourceFactory;
import com.rega.erp.common.db.tenant.datasource.DataSourceRegistry;
import com.rega.erp.common.db.tenant.datasource.DataSourceRouter;
import com.rega.erp.common.cache.core.CacheService;
import com.rega.erp.common.db.tenant.cache.DataSourceCacheManager;
import com.rega.erp.common.db.tenant.datasource.TenantDataSourceManager;
import com.rega.erp.common.db.tenant.service.DataSourceService;
import com.rega.erp.common.db.tenant.isolation.TenantIsolationManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * 租户自动配置
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(TenantProperties.class)
@ConditionalOnProperty(prefix = "rega.tenant", name = "enabled", havingValue = "true", matchIfMissing = true)
public class TenantAutoConfiguration {
    
    public TenantAutoConfiguration() {
        log.info("RegaWebERP Tenant Module Auto Configuration Initialized");
    }
    
    /**
     * 租户注册表
     */
    @Bean
    @ConditionalOnMissingBean
    public TenantRegistry tenantRegistry() {
        log.info("Register TenantRegistry");
        return new TenantRegistry();
    }
    
    /**
     * 数据源注册表
     */
    @Bean
    @ConditionalOnMissingBean
    public DataSourceRegistry dataSourceRegistry() {
        log.info("Register DataSourceRegistry");
        return new DataSourceRegistry();
    }
    
    /**
     * 数据源工厂
     */
    @Bean
    @ConditionalOnMissingBean
    public DataSourceFactory dataSourceFactory() {
        log.info("Register DataSourceFactory");
        return new DataSourceFactory();
    }
    
    /**
     * 数据源路由器
     */
    @Bean
    @ConditionalOnMissingBean
    public DataSourceRouter dataSourceRouter(DataSourceRegistry dataSourceRegistry, 
                                            TenantRegistry tenantRegistry) {
        log.info("Register DataSourceRouter");
        return new DataSourceRouter(dataSourceRegistry, tenantRegistry);
    }
    
    /**
     * 租户数据源管理器
     */
    /**
     * 数据源缓存管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public DataSourceCacheManager dataSourceCacheManager(CacheService cacheService) {
        log.info("Register DataSourceCacheManager");
        return new DataSourceCacheManager(cacheService);
    }

    /**
     * 租户数据源管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public TenantDataSourceManager tenantDataSourceManager(DataSourceRegistry dataSourceRegistry,
                                                          DataSourceService dataSourceService,
                                                          TenantRegistry tenantRegistry,
                                                          DataSourceCacheManager cacheManager) {
        log.info("Register TenantDataSourceManager");
        return new TenantDataSourceManager(dataSourceRegistry, dataSourceService, tenantRegistry, cacheManager);
    }
    
    /**
     * 租户隔离管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public TenantIsolationManager tenantIsolationManager(TenantRegistry tenantRegistry,
                                                        DataSourceRouter dataSourceRouter) {
        log.info("Register TenantIsolationManager");
        return new TenantIsolationManager(tenantRegistry, dataSourceRouter);
    }
    
    /**
     * 租户管理器
     */
    /**
     * 租户管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public TenantManager tenantManager(TenantRegistry tenantRegistry,
                                     TenantDataSourceManager dataSourceManager,
                                     TenantIsolationManager isolationManager) {
        log.info("Register TenantManager");
        return new TenantManager(tenantRegistry, dataSourceManager, isolationManager);
    }
}
