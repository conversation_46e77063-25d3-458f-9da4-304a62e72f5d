# Anyline ORM 集成指南

## 概述

本文档详细说明了如何在 RegaWebERP 项目中正确集成 Anyline ORM 框架。

## Anyline 简介

Anyline 是一个面向运行时的元数据动态映射框架，主要用来操作各种数据库结构以及读写元数据，已经适配100+数据库。

### 核心特性
- **动态数据源管理** - 支持运行时动态注册、切换、注销各种不同类型数据源
- **数据库结构和元数据管理** - 支持数据库结构的动态管理和元数据的标准化采集
- **动态DDL** - 基于元数据信息比对，分析表结构差异并生成跨数据库的动态DDL
- **动态查询条件** - 基于元数据的动态查询条件解决方案
- **数据库兼容适配** - 统一各种数据库方言，实现元数据对象在各个数据库之间无缝兼容

## 依赖配置

### 1. 父 POM 配置

在 `rega-parent/pom.xml` 中添加：

```xml
<properties>
    <anyline.version>8.7.2-SNAPSHOT</anyline.version>
</properties>

<dependencyManagement>
    <dependencies>
        <!-- Anyline ORM 依赖管理 -->
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-dependency</artifactId>
            <version>${anyline.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>

<!-- 启用 SNAPSHOT 版本仓库 -->
<repositories>
    <repository>
        <id>ossrh</id>
        <name>OSS Sonatype Snapshots</name>
        <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
        <snapshots>
            <enabled>true</enabled>
        </snapshots>
    </repository>
</repositories>
```

### 2. 子模块 POM 配置

在 `rega-common-db/pom.xml` 中添加：

```xml
<dependencies>
    <!-- Anyline ORM 依赖 -->
    <dependency>
        <groupId>org.anyline</groupId>
        <artifactId>anyline-environment-spring-data-jdbc</artifactId>
    </dependency>
    <dependency>
        <groupId>org.anyline</groupId>
        <artifactId>anyline-data-jdbc-postgresql</artifactId>
    </dependency>
</dependencies>
```

## 配置文件

### application.yml 配置示例

```yaml
# Anyline 配置
anyline:
  # 数据源配置
  datasource:
    # 主数据源
    primary:
      driver-class-name: org.postgresql.Driver
      url: *****************************************
      username: rega_user
      password: ${DB_PASSWORD}
    
    # 动态数据源配置
    dynamic:
      enabled: true
      # 数据源注册方式
      register-type: runtime
      
  # 元数据配置
  metadata:
    # 是否启用元数据缓存
    cache-enabled: true
    # 缓存过期时间（秒）
    cache-timeout: 3600
    
  # SQL 配置
  sql:
    # 是否显示 SQL
    show-sql: true
    # 是否格式化 SQL
    format-sql: true
    # 是否显示执行时间
    show-execution-time: true
```

## 基本使用

### 1. 注入 AnylineService

```java
@Service
public class TenantDataService {
    
    @Autowired
    private AnylineService service;
    
    /**
     * 动态查询租户数据
     */
    public DataSet queryTenantData(String tenantId, ConfigStore conditions) {
        // 设置租户上下文
        TenantContextHolder.setTenantId(tenantId);
        
        try {
            // 使用 Anyline 动态查询
            return service.querys("user_table", conditions);
        } finally {
            TenantContextHolder.clear();
        }
    }
}
```

### 2. 动态查询条件

```java
@Service
public class DynamicQueryService {
    
    @Autowired
    private AnylineService service;
    
    /**
     * 构建动态查询条件
     */
    public DataSet queryWithConditions(String table, Map<String, Object> params) {
        ConfigStore conditions = new ConfigStore();
        
        // 动态添加查询条件
        if (params.containsKey("name")) {
            conditions.and("name", params.get("name"));
        }
        
        if (params.containsKey("status")) {
            conditions.and("status", params.get("status"));
        }
        
        if (params.containsKey("createTime")) {
            conditions.and("create_time", ">=", params.get("createTime"));
        }
        
        // 添加分页
        conditions.setPageNavi(1, 20);
        
        return service.querys(table, conditions);
    }
}
```

### 3. 元数据操作

```java
@Service
public class MetadataService {
    
    @Autowired
    private AnylineService service;
    
    /**
     * 获取表结构信息
     */
    public List<Table> getTableStructure(String tableName) {
        return service.metadata().tables(tableName);
    }
    
    /**
     * 获取列信息
     */
    public List<Column> getColumnInfo(String tableName) {
        return service.metadata().columns(tableName);
    }
    
    /**
     * 动态创建表
     */
    public boolean createTable(Table table) {
        try {
            service.ddl().create(table);
            return true;
        } catch (Exception e) {
            log.error("创建表失败: {}", e.getMessage());
            return false;
        }
    }
}
```

### 4. 多数据源切换

```java
@Service
public class MultiDataSourceService {
    
    @Autowired
    private AnylineService service;
    
    /**
     * 切换到指定数据源执行操作
     */
    public <T> T executeWithDataSource(String dataSourceId, Supplier<T> operation) {
        String originalDataSource = DataSourceHolder.getDataSource();
        
        try {
            // 切换数据源
            DataSourceHolder.setDataSource(dataSourceId);
            return operation.get();
        } finally {
            // 恢复原数据源
            if (originalDataSource != null) {
                DataSourceHolder.setDataSource(originalDataSource);
            } else {
                DataSourceHolder.clear();
            }
        }
    }
}
```

## 与租户系统集成

### 1. 租户数据源管理

```java
@Component
public class TenantAnylineIntegration {
    
    @Autowired
    private AnylineService service;
    
    /**
     * 为租户注册专用数据源
     */
    public void registerTenantDataSource(String tenantId, DataSourceConfig config) {
        String dataSourceId = "tenant_" + tenantId;
        
        // 使用 Anyline 注册数据源
        DataSourceHolder.reg(dataSourceId, config.toDataSource());
        
        log.info("租户数据源注册成功: tenantId={}, dataSourceId={}", tenantId, dataSourceId);
    }
    
    /**
     * 在租户数据源中执行查询
     */
    public DataSet queryInTenantDataSource(String tenantId, String sql, Object... params) {
        String dataSourceId = "tenant_" + tenantId;
        
        return executeWithDataSource(dataSourceId, () -> {
            return service.querys(sql, params);
        });
    }
}
```

### 2. 租户表结构管理

```java
@Component
public class TenantSchemaManager {
    
    @Autowired
    private AnylineService service;
    
    /**
     * 为租户初始化表结构
     */
    public void initTenantSchema(String tenantId, List<Table> tables) {
        String dataSourceId = "tenant_" + tenantId;
        
        executeWithDataSource(dataSourceId, () -> {
            for (Table table : tables) {
                // 检查表是否存在
                if (!service.metadata().exists(table)) {
                    // 创建表
                    service.ddl().create(table);
                    log.info("为租户创建表: tenantId={}, table={}", tenantId, table.getName());
                }
            }
            return null;
        });
    }
}
```

## 注意事项

### 1. Java 版本要求
- Anyline 8.7.2-SNAPSHOT 需要 Java 17
- 确保开发和生产环境都使用 Java 17

### 2. SNAPSHOT 版本使用
- 开发环境使用 SNAPSHOT 版本获取最新功能
- 生产环境建议使用 Release 版本确保稳定性

### 3. 性能优化
- 合理使用元数据缓存
- 避免频繁的数据源切换
- 使用连接池优化数据库连接

### 4. 错误处理
- 捕获并处理 Anyline 相关异常
- 记录详细的错误日志
- 提供友好的错误提示

## 参考资源

- [Anyline 官方文档](http://www.anyline.org/doc)
- [Anyline GitHub](https://gitee.com/anyline)
- [Anyline 示例代码](https://gitee.com/anyline/anyline-simple)
- [Maven 中央仓库](https://mvnrepository.com/artifact/org.anyline/anyline-core)
