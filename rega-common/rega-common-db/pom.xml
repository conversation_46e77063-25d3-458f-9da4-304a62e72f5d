<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.rega.erp</groupId>
        <artifactId>rega-common</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>rega-common-db</artifactId>
    <name>RegaWebERP Common DB</name>
    <description>RegaWebERP Common Database Module</description>

    <dependencies>
        <!-- 内部依赖 -->
        <dependency>
            <groupId>com.rega.erp</groupId>
            <artifactId>rega-common-core</artifactId>
        </dependency>

        <!-- Spring Boot 核心依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 数据库相关依赖 -->
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- Anyline ORM 依赖 -->
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-environment-spring-data-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-data-jdbc-postgresql</artifactId>
        </dependency>

        <!-- 工具依赖 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project> 