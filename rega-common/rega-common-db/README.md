# RegaWebERP 数据库访问模块

## 概述

rega-common-db 是 RegaWebERP 系统的数据库访问模块，基于 Anyline ORM 框架和 PostgreSQL 数据库构建，提供了完整的多租户数据隔离、动态查询构建、元数据管理等功能。

## 功能特性

### 🏢 多租户数据隔离

#### **两种隔离策略**
- **字段隔离** - 多租户共享数据源，通过 `tenant_id` 字段区分数据
- **数据源隔离** - 租户使用专用数据源，提供更高的数据隔离性

#### **灵活的租户管理**
- 租户创建时选择隔离策略
- 支持租户状态管理（活跃、暂停、禁用、过期、删除）
- 自动租户环境初始化和清理
- 租户信息缓存和性能优化

#### **智能数据源分配**
- 自动分配可用的专用数据源
- 支持指定数据源创建租户
- 动态创建新的专用数据源
- 数据源使用率监控和负载均衡

### 🔄 动态数据源管理

#### **数据源注册表**
- 统一管理所有数据源实例
- 数据源信息缓存和元数据管理
- 数据源健康检查和状态监控
- 自动故障检测和恢复

#### **数据源路由器**
- 根据租户信息自动路由到正确数据源
- 支持数据源回调执行
- 连接池管理和优化
- 性能统计和监控

#### **数据源工厂**
- 标准化数据源创建流程
- HikariCP 连接池集成
- PostgreSQL 特定优化配置
- 连接测试和验证

### 📊 查询和操作上下文

#### **查询上下文 (QueryContext)**
- 租户过滤条件自动添加
- 复杂查询条件构建
- 分页、排序、分组支持
- 多表连接查询支持

#### **数据操作上下文 (DataOperationContext)**
- 自动设置租户ID字段
- 批量操作支持
- 事务管理集成
- 审计信息记录

### 🛡️ 安全和权限

#### **租户隔离验证**
- 防止跨租户数据访问
- 租户权限验证
- 数据源访问控制
- 安全审计日志

#### **数据权限控制**
- 行级数据权限
- 字段级访问控制
- 动态权限验证
- 权限缓存优化

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.rega.erp</groupId>
    <artifactId>rega-common-db</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

```yaml
# 租户配置
rega:
  tenant:
    enabled: true
    
    # 租户字段配置
    field:
      name: tenant_id
      type: VARCHAR(64)
      nullable: false
      indexed: true
      comment: 租户ID
    
    # 数据源配置
    datasource:
      # 主数据源
      primary:
        url: *****************************************
        username: rega_user
        password: ${DB_PASSWORD}
        driver-class-name: org.postgresql.Driver
      
      # 专用数据源模板
      dedicated:
        url-template: ********************************************_{index}
        username-template: tenant_user_{index}
        password-template: ${TENANT_DB_PASSWORD}
        driver-class-name: org.postgresql.Driver
        max-tenants-per-datasource: 10
        
        # 连接池配置
        hikari:
          maximum-pool-size: 10
          minimum-idle: 2
          connection-timeout: 30000
          idle-timeout: 600000
          max-lifetime: 1800000
    
    # 租户管理配置
    management:
      cache-timeout: 3600
      auto-create-database: true
      auto-init-schema: true
      datasource-warmup: true
      health-check-interval: 300
      max-tenants: 1000
```

### 3. 基本使用

#### 创建租户

```java
@Service
public class TenantService {
    
    @Autowired
    private TenantManager tenantManager;
    
    /**
     * 创建字段隔离租户
     */
    public TenantCreationResult createFieldIsolationTenant() {
        TenantCreationRequest request = TenantCreationRequest.builder()
            .tenantId("small_company_001")
            .tenantName("小型企业001")
            .description("字段隔离租户")
            .isolationType(TenantIsolationType.FIELD_ISOLATION)
            .contactName("张三")
            .contactEmail("<EMAIL>")
            .maxUsers(50)
            .build();
        
        return tenantManager.createTenant(request);
    }
    
    /**
     * 创建数据源隔离租户
     */
    public TenantCreationResult createDataSourceIsolationTenant() {
        TenantCreationRequest request = TenantCreationRequest.builder()
            .tenantId("enterprise_001")
            .tenantName("大型企业001")
            .description("数据源隔离租户")
            .isolationType(TenantIsolationType.DATASOURCE_ISOLATION)
            .maxTenantsPerDataSource(5)
            .contactName("李四")
            .contactEmail("<EMAIL>")
            .maxUsers(1000)
            .autoCreateDatabase(true)
            .autoInitSchema(true)
            .build();
        
        return tenantManager.createTenant(request);
    }
}
```

#### 数据源使用

```java
@Service
public class DataService {
    
    @Autowired
    private DataSourceRouter dataSourceRouter;
    
    /**
     * 获取当前租户数据源
     */
    public void queryData() {
        // 自动根据租户上下文获取正确的数据源
        DataSource dataSource = dataSourceRouter.getCurrentDataSource();
        
        // 使用数据源执行查询
        // ...
    }
    
    /**
     * 在指定租户上下文中执行
     */
    public <T> T executeInTenant(String tenantId, DataSourceRouter.DataSourceCallback<T> callback) {
        return dataSourceRouter.executeWithTenant(tenantId, callback);
    }
}
```

#### 查询上下文使用

```java
@Service
public class QueryService {
    
    @Autowired
    private TenantIsolationManager isolationManager;
    
    public void executeQuery() {
        // 创建查询上下文
        QueryContext context = new QueryContext();
        context.setTableName("users");
        context.addCondition("status", "active");
        context.addOrderBy("create_time", "DESC");
        
        // 设置分页
        QueryContext.PageInfo pageInfo = new QueryContext.PageInfo();
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(20);
        context.setPageInfo(pageInfo);
        
        // 处理租户隔离
        isolationManager.processQuery(context);
        
        // 执行查询（租户过滤条件已自动添加）
        // ...
    }
}
```

## 核心组件

### TenantManager - 租户管理器
- 租户创建、更新、删除
- 租户状态管理
- 租户环境初始化

### DataSourceRouter - 数据源路由器
- 自动数据源路由
- 租户数据源获取
- 数据源健康检查

### TenantIsolationManager - 租户隔离管理器
- 查询租户过滤
- 数据操作租户设置
- 隔离策略处理

### DataSourceFactory - 数据源工厂
- 标准化数据源创建
- 连接池配置
- PostgreSQL优化

## 隔离策略对比

| 特性 | 字段隔离 | 数据源隔离 |
|------|----------|------------|
| **数据安全性** | 中等 | 高 |
| **性能影响** | 低 | 中等 |
| **资源消耗** | 低 | 高 |
| **管理复杂度** | 低 | 中等 |
| **扩展性** | 高 | 中等 |
| **适用场景** | 小型客户 | 大型客户 |
| **成本** | 低 | 高 |

## 最佳实践

### 1. 租户策略选择
- **小型客户** - 使用字段隔离，成本低，管理简单
- **大型客户** - 使用数据源隔离，安全性高，性能好
- **政府客户** - 使用数据源隔离，满足合规要求

### 2. 性能优化
- 合理设置连接池大小
- 启用数据源预热
- 使用租户信息缓存
- 定期健康检查

### 3. 安全考虑
- 定期更新数据源密码
- 启用审计日志
- 监控跨租户访问
- 数据备份和恢复

### 4. 监控和运维
- 监控数据源使用率
- 跟踪租户活跃度
- 性能指标收集
- 异常告警设置

## 扩展指南

### 1. 自定义隔离策略
```java
@Component
public class CustomIsolationStrategy implements TenantIsolationStrategy {
    // 实现自定义隔离逻辑
}
```

### 2. 自定义数据源工厂
```java
@Component
@Primary
public class CustomDataSourceFactory extends DataSourceFactory {
    // 扩展数据源创建逻辑
}
```

### 3. 添加新的数据库支持
```java
public class MySQLDataSourceConfig extends DataSourceConfig {
    // MySQL特定配置
}
```

## 注意事项

1. **数据一致性** - 确保所有表都有 tenant_id 字段
2. **性能监控** - 定期监控数据源性能和连接池状态
3. **资源管理** - 及时清理无用的数据源和连接
4. **安全审计** - 记录所有租户操作和数据访问
5. **备份策略** - 制定合适的数据备份和恢复策略

## 依赖说明

- Anyline ORM Framework (动态SQL和元数据管理)
- PostgreSQL Driver (数据库驱动)
- HikariCP (连接池)
- Spring Boot (自动配置和依赖注入)
- rega-common-core (内部依赖)
