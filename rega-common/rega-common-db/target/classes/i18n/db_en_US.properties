# Database Module Internationalization Messages - English

# Tenant related
tenant.id.required=Tenant ID is required
tenant.id.invalid.format=Invalid tenant ID format
tenant.info.null=Tenant info cannot be null
tenant.info.invalid=Invalid tenant info
tenant.request.null=Tenant request cannot be null
tenant.request.invalid=Invalid tenant request
tenant.not.found=Tenant not found: {0}
tenant.already.exists=Tenant already exists: {0}
tenant.not.active=Tenant not active: {0}
tenant.not.available=Tenant not available: {0}
tenant.init.failed=Tenant environment initialization failed: {0}
tenant.delete.failed=Tenant deletion failed: {0}

# DataSource related
datasource.id.required=DataSource ID is required
datasource.instance.null=DataSource instance cannot be null
datasource.info.null=DataSource info cannot be null
datasource.info.invalid=Invalid datasource info
datasource.config.invalid=Invalid datasource configuration
datasource.not.found=DataSource not found: {0}
datasource.already.exists=DataSource already exists: {0}
datasource.not.active=DataSource not active: {0}
datasource.not.available=DataSource not available: {0}
datasource.create.failed=DataSource creation failed: {0}
datasource.connection.invalid=Invalid datasource connection
datasource.connection.test.failed=DataSource connection test failed: {0}
datasource.tenant.limit.exceeded=DataSource tenant limit exceeded: {0}
datasource.callback.failed=DataSource callback execution failed: {0}

# Primary datasource related
primary.datasource.not.found=Primary datasource not found

# Tenant datasource related
tenant.datasource.callback.failed=Tenant datasource callback execution failed: {0}

# Query related
query.context.invalid=Invalid query context
query.execution.failed=Query execution failed: {0}
query.tenant.filter.failed=Failed to add tenant filter condition

# Data operation related
data.operation.failed=Data operation failed: {0}
data.operation.context.invalid=Invalid data operation context
data.operation.tenant.set.failed=Failed to set tenant ID

# Transaction related
transaction.start.failed=Transaction start failed
transaction.commit.failed=Transaction commit failed
transaction.rollback.failed=Transaction rollback failed
transaction.not.active=Transaction not active

# Connection pool related
pool.config.invalid=Invalid pool configuration
pool.creation.failed=Pool creation failed
pool.close.failed=Pool close failed

# Isolation related
isolation.type.invalid=Invalid isolation type
isolation.strategy.not.found=Isolation strategy not found
isolation.processing.failed=Isolation processing failed

# Cache related
cache.operation.failed=Cache operation failed
cache.key.invalid=Invalid cache key
cache.value.invalid=Invalid cache value

# Health check related
health.check.failed=Health check failed
health.check.timeout=Health check timeout

# Audit related
audit.log.failed=Audit log failed
audit.info.invalid=Invalid audit info

# Permission related
permission.denied=Permission denied
permission.tenant.access.denied=Tenant access permission denied

# Configuration related
config.invalid=Invalid configuration
config.missing=Missing configuration
config.load.failed=Configuration load failed

# General errors
internal.error=Internal system error
operation.not.supported=Operation not supported
parameter.invalid=Invalid parameter
resource.not.found=Resource not found
resource.already.exists=Resource already exists
resource.locked=Resource locked
resource.expired=Resource expired

# Database related
database.connection.failed=Database connection failed
database.query.failed=Database query failed
database.update.failed=Database update failed
database.transaction.failed=Database transaction failed
database.schema.invalid=Invalid database schema
database.constraint.violation=Database constraint violation

# Performance related
performance.threshold.exceeded=Performance threshold exceeded
performance.monitoring.failed=Performance monitoring failed

# Security related
security.validation.failed=Security validation failed
security.encryption.failed=Encryption failed
security.decryption.failed=Decryption failed
