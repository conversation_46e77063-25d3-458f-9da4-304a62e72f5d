# 数据库模块国际化消息 - 中文

# 租户相关
tenant.id.required=租户ID不能为空
tenant.id.invalid.format=租户ID格式无效
tenant.info.null=租户信息不能为空
tenant.info.invalid=租户信息无效
tenant.request.null=租户请求不能为空
tenant.request.invalid=租户请求无效
tenant.not.found=租户不存在：{0}
tenant.already.exists=租户已存在：{0}
tenant.not.active=租户未激活：{0}
tenant.not.available=租户不可用：{0}
tenant.init.failed=租户环境初始化失败：{0}
tenant.delete.failed=租户删除失败：{0}

# 数据源相关
datasource.id.required=数据源ID不能为空
datasource.instance.null=数据源实例不能为空
datasource.info.null=数据源信息不能为空
datasource.info.invalid=数据源信息无效
datasource.config.invalid=数据源配置无效
datasource.not.found=数据源不存在：{0}
datasource.already.exists=数据源已存在：{0}
datasource.not.active=数据源未激活：{0}
datasource.not.available=数据源不可用：{0}
datasource.create.failed=数据源创建失败：{0}
datasource.connection.invalid=数据源连接无效
datasource.connection.test.failed=数据源连接测试失败：{0}
datasource.tenant.limit.exceeded=数据源租户数量超限：{0}
datasource.callback.failed=数据源回调执行失败：{0}

# 主数据源相关
primary.datasource.not.found=主数据源不存在

# 租户数据源相关
tenant.datasource.callback.failed=租户数据源回调执行失败：{0}

# 查询相关
query.context.invalid=查询上下文无效
query.execution.failed=查询执行失败：{0}
query.tenant.filter.failed=租户过滤条件添加失败

# 数据操作相关
data.operation.failed=数据操作失败：{0}
data.operation.context.invalid=数据操作上下文无效
data.operation.tenant.set.failed=设置租户ID失败

# 事务相关
transaction.start.failed=事务启动失败
transaction.commit.failed=事务提交失败
transaction.rollback.failed=事务回滚失败
transaction.not.active=事务未激活

# 连接池相关
pool.config.invalid=连接池配置无效
pool.creation.failed=连接池创建失败
pool.close.failed=连接池关闭失败

# 隔离相关
isolation.type.invalid=隔离类型无效
isolation.strategy.not.found=隔离策略不存在
isolation.processing.failed=隔离处理失败

# 缓存相关
cache.operation.failed=缓存操作失败
cache.key.invalid=缓存键无效
cache.value.invalid=缓存值无效

# 健康检查相关
health.check.failed=健康检查失败
health.check.timeout=健康检查超时

# 审计相关
audit.log.failed=审计日志记录失败
audit.info.invalid=审计信息无效

# 权限相关
permission.denied=权限不足
permission.tenant.access.denied=租户访问权限不足

# 配置相关
config.invalid=配置无效
config.missing=配置缺失
config.load.failed=配置加载失败

# 通用错误
internal.error=系统内部错误
operation.not.supported=操作不支持
parameter.invalid=参数无效
resource.not.found=资源不存在
resource.already.exists=资源已存在
resource.locked=资源被锁定
resource.expired=资源已过期

# 数据库相关
database.connection.failed=数据库连接失败
database.query.failed=数据库查询失败
database.update.failed=数据库更新失败
database.transaction.failed=数据库事务失败
database.schema.invalid=数据库结构无效
database.constraint.violation=数据库约束违反

# 性能相关
performance.threshold.exceeded=性能阈值超限
performance.monitoring.failed=性能监控失败

# 安全相关
security.validation.failed=安全验证失败
security.encryption.failed=加密失败
security.decryption.failed=解密失败
