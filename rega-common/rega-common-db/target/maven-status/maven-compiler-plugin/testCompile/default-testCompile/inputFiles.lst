/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-common/rega-common-db/src/test/java/com/rega/erp/common/db/tenant/integration/DataSourceIntegrationTest.java
/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-common/rega-common-db/src/test/java/com/rega/erp/common/db/tenant/datasource/DataSourceRegistryTest.java
/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-common/rega-common-db/src/test/java/com/rega/erp/common/db/tenant/TenantManagerTest.java
/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-common/rega-common-db/src/test/java/com/rega/erp/common/db/tenant/domain/DataSourceEntityTest.java
/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-common/rega-common-db/src/test/java/com/rega/erp/common/db/tenant/service/DataSourceServiceTest.java
/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-common/rega-common-db/src/test/java/com/rega/erp/common/db/AnylineIntegrationTest.java
