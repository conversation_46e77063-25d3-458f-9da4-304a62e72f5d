# rega-common-db 依赖说明

## 当前状态

由于一些依赖问题，当前模块的依赖配置已经简化，以确保基本功能可以正常编译和运行。

## 已解决的依赖问题

### 1. Anyline ORM 依赖问题
**问题**: Anyline 8.7.2-SNAPSHOT 版本在 Maven 中央仓库中不可用
**解决方案**: 暂时注释掉 Anyline 相关依赖，等待稳定版本发布

```xml
<!-- 暂时注释掉的依赖 -->
<!--
<dependency>
    <groupId>org.anyline</groupId>
    <artifactId>anyline-environment-spring-data-jdbc</artifactId>
    <optional>true</optional>
</dependency>
-->
```

### 2. 重复依赖清理
**问题**: POM 文件中存在重复的依赖声明
**解决方案**: 清理重复依赖，保持依赖声明的唯一性

### 3. Java 版本兼容性
**问题**: 项目要求 Java 17，但系统可能使用较低版本
**解决方案**: 确保使用 Java 17 进行编译

## 当前依赖列表

### 核心依赖
- `rega-common-core` - 内部核心模块
- `spring-boot-starter-jdbc` - Spring Boot JDBC 支持
- `spring-boot-configuration-processor` - 配置处理器

### 数据库依赖
- `HikariCP` - 高性能连接池
- `postgresql` - PostgreSQL 数据库驱动

### 工具依赖
- `lombok` - 代码生成工具

### 测试依赖
- `spring-boot-starter-test` - Spring Boot 测试支持
- `h2` - 内存数据库（测试用）

## 待添加的依赖

当以下依赖的稳定版本可用时，需要重新添加：

### Anyline ORM 框架
```xml
<dependency>
    <groupId>org.anyline</groupId>
    <artifactId>anyline-environment-spring-data-jdbc</artifactId>
    <version>${anyline.version}</version>
    <optional>true</optional>
</dependency>
```

### 可选的增强依赖
```xml
<!-- Redis 缓存支持 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
    <optional>true</optional>
</dependency>

<!-- Jackson JSON 处理 -->
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
</dependency>

<!-- 监控和指标 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
    <optional>true</optional>
</dependency>
```

## 编译要求

### Java 版本
- **最低要求**: Java 17
- **推荐版本**: Java 17 或更高版本

### Maven 版本
- **最低要求**: Maven 3.6.0
- **推荐版本**: Maven 3.8.0 或更高版本

## 编译命令

```bash
# 清理并编译
mvn clean compile

# 跳过测试编译
mvn clean compile -DskipTests

# 完整构建
mvn clean install
```

## 故障排除

### 1. Java 版本问题
如果遇到 Java 版本相关错误：
```bash
# 检查 Java 版本
java -version
javac -version

# 设置 JAVA_HOME（如果需要）
export JAVA_HOME=/path/to/java17
```

### 2. 依赖下载问题
如果遇到依赖下载失败：
```bash
# 清理本地仓库
mvn dependency:purge-local-repository

# 强制更新依赖
mvn clean compile -U
```

### 3. 编译错误
如果遇到编译错误：
1. 检查 Java 版本是否为 17
2. 检查网络连接是否正常
3. 清理并重新编译项目

## 未来计划

1. **Anyline 集成**: 等待 Anyline 稳定版本发布后重新集成
2. **性能优化**: 添加连接池监控和性能指标
3. **缓存支持**: 集成 Redis 缓存支持
4. **监控集成**: 添加 Spring Boot Actuator 监控支持

## 注意事项

1. 当前版本的功能可能受限，因为缺少 Anyline ORM 支持
2. 在生产环境使用前，请确保所有依赖都已正确配置
3. 定期检查依赖更新，特别是安全补丁
4. 建议在 CI/CD 流水线中使用固定的 Java 版本
