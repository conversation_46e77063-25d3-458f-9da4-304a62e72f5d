#!/bin/bash

# RegaWebERP common-db 模块构建验证脚本

echo "========================================="
echo "RegaWebERP common-db 模块构建验证"
echo "========================================="

# 检查 Java 版本
echo "1. 检查 Java 版本..."
java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
echo "当前 Java 版本: $java_version"

if [[ "$java_version" < "17" ]]; then
    echo "❌ 错误: 需要 Java 17 或更高版本"
    echo "请设置正确的 JAVA_HOME 环境变量"
    exit 1
else
    echo "✅ Java 版本检查通过"
fi

# 检查 Maven 版本
echo ""
echo "2. 检查 Maven 版本..."
if command -v mvn &> /dev/null; then
    mvn_version=$(mvn -version | head -n 1 | cut -d' ' -f3)
    echo "当前 Maven 版本: $mvn_version"
    echo "✅ Maven 检查通过"
else
    echo "❌ 错误: 未找到 Maven"
    exit 1
fi

# 清理项目
echo ""
echo "3. 清理项目..."
mvn clean -q
if [ $? -eq 0 ]; then
    echo "✅ 项目清理成功"
else
    echo "❌ 项目清理失败"
    exit 1
fi

# 编译项目
echo ""
echo "4. 编译项目..."
mvn compile -DskipTests -q
if [ $? -eq 0 ]; then
    echo "✅ 项目编译成功"
else
    echo "❌ 项目编译失败"
    echo "请检查依赖配置和 Java 版本"
    exit 1
fi

# 运行测试编译
echo ""
echo "5. 编译测试代码..."
mvn test-compile -DskipTests -q
if [ $? -eq 0 ]; then
    echo "✅ 测试代码编译成功"
else
    echo "❌ 测试代码编译失败"
    exit 1
fi

# 检查依赖
echo ""
echo "6. 检查依赖..."
mvn dependency:resolve -q > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 依赖下载成功"

    # 检查 Anyline 依赖
    echo "7. 检查 Anyline 依赖..."
    if mvn dependency:tree -q | grep -q "anyline"; then
        echo "✅ Anyline 依赖已正确加载"
    else
        echo "⚠️  未检测到 Anyline 依赖"
    fi
else
    echo "❌ 依赖下载失败"
    exit 1
fi

echo ""
echo "========================================="
echo "✅ 构建验证完成！"
echo "========================================="
echo ""
echo "项目状态:"
echo "- 编译: 成功"
echo "- 测试编译: 成功"
echo "- 依赖: 正常"
echo "- Anyline ORM: 已集成"
echo ""
echo "注意事项:"
echo "- Anyline ORM 8.7.2-SNAPSHOT 已成功集成"
echo "- 项目需要 Java 17 进行编译和运行"
echo "- 详细信息请查看 DEPENDENCIES.md 和 ANYLINE_INTEGRATION.md"
echo ""
echo "下一步:"
echo "- 运行测试: mvn test"
echo "- 打包项目: mvn package"
echo "- 安装到本地: mvn install"
