# rega-common-log 更新日志

## [1.0.0] - 2024-01-XX

### 新增功能

#### 📝 操作日志记录
- **@Log注解** - 提供声明式操作日志记录
  - 支持模块标题和功能描述配置
  - 支持业务类型和操作人类别配置
  - 支持请求参数和响应数据记录控制
  - 支持敏感参数过滤
  - 支持异步/同步记录模式选择

- **LogAspect切面** - AOP自动拦截和记录
  - 自动记录方法执行信息
  - 自动计算方法执行时间
  - 自动捕获和记录异常信息
  - 自动获取用户和环境信息
  - 支持线程本地变量管理

- **OperLog实体** - 完整的操作日志数据模型
  - 基础信息：标题、描述、业务类型
  - 方法信息：类名、方法名、请求方式
  - 用户信息：操作人、部门、操作类别
  - 环境信息：IP、地址、浏览器、操作系统
  - 数据信息：请求参数、返回结果、业务数据
  - 状态信息：执行状态、错误信息、执行时间
  - 扩展字段：5个自定义扩展字段

#### 🔐 登录日志记录
- **LoginLog实体** - 完整的登录日志数据模型
  - 用户信息：用户名、昵称、会话ID
  - 登录信息：登录时间、登出时间、在线时长
  - 环境信息：IP地址、地理位置、设备信息
  - 状态信息：登录状态、提示消息、失败次数
  - 方式信息：登录方式、登录来源、Token
  - 扩展字段：3个自定义扩展字段

- **登录追踪** - 全面的登录行为监控
  - 支持多种登录方式记录
  - 支持多种登录来源识别
  - 支持登录失败次数统计
  - 支持首次登录标识

#### 🚀 高性能设计
- **异步处理** - 基于线程池的异步日志记录
  - 可配置线程池参数
  - 支持拒绝策略配置
  - 支持优雅关闭
  - 不影响业务性能

- **事件驱动** - 基于Spring事件机制
  - OperLogEvent - 操作日志事件
  - LoginLogEvent - 登录日志事件
  - LogEventListener - 统一事件监听器
  - LogPublisher - 事件发布器

- **智能优化** - 内存和性能优化
  - 自动截取长文本避免内存溢出
  - 敏感参数过滤保护隐私
  - 异常信息格式化和截取
  - 线程本地变量自动清理

#### 🛠️ 工具类库
- **LogUtils** - 日志工具类
  - 客户端信息获取（IP、浏览器、操作系统）
  - 用户代理解析和设备类型识别
  - 请求信息获取（URI、方法、参数）
  - 日志对象创建和初始化
  - 敏感参数过滤和文本截取
  - 异常信息格式化

- **LogPublisher** - 事件发布器
  - 操作日志事件发布
  - 登录日志事件发布
  - 异常安全处理

#### ⚙️ 配置管理
- **LogConfig** - 日志配置类
  - 操作日志开关配置
  - 登录日志开关配置
  - 异步处理开关配置
  - 日志保留天数配置
  - 文本长度限制配置
  - 线程池参数配置

- **LogAutoConfiguration** - 自动配置
  - 条件化Bean注册
  - 配置属性绑定
  - 自动装配管理

#### 🧹 自动清理
- **LogCleanTask** - 定时清理任务
  - 支持定时清理过期日志
  - 支持分别清理操作日志和登录日志
  - 可配置清理时间和保留天数
  - 支持手动触发清理

#### 📊 枚举定义
- **BusinessType** - 业务操作类型（21种）
  - 基础操作：新增、修改、删除、查询
  - 权限操作：授权、强退
  - 数据操作：导入、导出、清空、生成代码
  - 系统操作：登录、登出、上传、下载
  - 流程操作：审核、发布、撤销
  - 维护操作：同步、备份、恢复

- **OperatorType** - 操作人类别（7种）
  - 后台用户、手机端用户、系统自动
  - API接口、定时任务、第三方系统

- **LogStatus** - 日志状态（3种）
  - 成功、失败、异常

#### 🧪 测试支持
- **LogUtilsTest** - 工具类测试
  - 日志对象创建测试
  - 参数过滤功能测试
  - 文本截取功能测试
  - 异常格式化测试
  - 枚举类型测试

- **LogExampleController** - 示例控制器
  - 各种业务类型的日志记录示例
  - 参数过滤示例
  - 异常处理示例
  - 同步/异步记录示例

#### 🔧 服务接口
- **LogService** - 日志服务接口
  - 操作日志保存（同步/异步）
  - 登录日志保存（同步/异步）
  - 过期日志清理

- **DefaultLogServiceImpl** - 默认实现
  - 基于日志框架的默认实现
  - 支持条件化注册
  - 便于扩展和替换

### 技术特性

#### 🔒 安全特性
- **敏感信息保护** - 自动过滤密码等敏感参数
- **参数长度限制** - 防止超长参数导致存储问题
- **异常安全** - 日志记录失败不影响业务流程
- **线程安全** - 所有组件都是线程安全的

#### 🌐 多租户支持
- **租户隔离** - 支持多租户日志隔离
- **租户标识** - 自动记录租户ID
- **租户清理** - 支持按租户清理日志

#### 📱 多端支持
- **设备识别** - 自动识别PC、移动端、平板设备
- **浏览器识别** - 自动识别各种浏览器类型
- **操作系统识别** - 自动识别操作系统信息
- **登录来源** - 支持PC、移动端、小程序、APP等来源

#### 🔄 扩展性
- **接口化设计** - 核心功能基于接口，便于扩展
- **事件驱动** - 基于事件机制，松耦合设计
- **配置化** - 丰富的配置选项，灵活适应需求
- **插件化** - 支持自定义日志服务实现

### 配置示例

```yaml
rega:
  log:
    enabled: true
    oper-log-enabled: true
    login-log-enabled: true
    async-enabled: true
    retention-days: 30
    clean-enabled: true
    clean-cron: "0 0 2 * * ?"
    max-param-length: 2000
    max-result-length: 2000
    max-error-length: 2000
    thread-pool:
      core-pool-size: 2
      max-pool-size: 10
      queue-capacity: 200
      keep-alive-seconds: 60
      thread-name-prefix: "log-task-"
```

### 使用示例

```java
// 基础日志记录
@Log(title = "用户管理", description = "查询用户", businessType = BusinessType.SELECT)
public User getUser(Long id) { ... }

// 高级配置
@Log(title = "用户登录", businessType = BusinessType.LOGIN,
     excludeParamNames = {"password"}, async = true)
public LoginResult login(LoginRequest request) { ... }

// 事件方式
LogPublisher.publishOperLog(operLog);
LogPublisher.publishLoginLog(loginLog);
```

### 依赖管理

#### 新增依赖
- `org.springframework.boot:spring-boot-starter-aop` - AOP支持
- `net.logstash.logback:logstash-logback-encoder:7.3` - 结构化日志
- `cn.hutool:hutool-all` - 工具库
- `rega-common-core` - 内部核心依赖

#### 版本要求
- Spring Boot 3.1.0+
- Java 17+

### 注意事项

1. **性能影响**: 建议在生产环境使用异步模式
2. **存储管理**: 定期清理过期日志，避免存储空间不足
3. **敏感信息**: 务必配置敏感参数过滤
4. **异常处理**: 日志记录异常不应中断业务流程
5. **线程安全**: 所有组件都经过线程安全设计

### 后续计划

- [ ] 添加日志统计分析功能
- [ ] 支持日志数据导出
- [ ] 集成ELK日志分析
- [ ] 添加日志告警功能
- [ ] 支持日志数据可视化
- [ ] 优化大数据量场景性能
