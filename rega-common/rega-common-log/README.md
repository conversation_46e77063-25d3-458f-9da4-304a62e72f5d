# RegaWebERP 日志模块

## 概述

rega-common-log 是 RegaWebERP 系统的日志管理模块，提供了完整的操作日志和登录日志记录功能，支持异步处理、事件驱动、自动清理等特性。

## 功能特性

### 📝 操作日志记录

- **注解驱动** - 使用 `@Log` 注解轻松记录操作日志
- **AOP切面** - 自动拦截方法调用，记录详细信息
- **异步处理** - 支持异步记录，不影响业务性能
- **参数过滤** - 支持敏感参数过滤（如密码）
- **异常记录** - 自动记录异常信息和堆栈跟踪
- **多维度信息** - 记录用户、IP、浏览器、操作系统等信息

### 🔐 登录日志记录

- **登录追踪** - 记录用户登录、登出行为
- **安全监控** - 记录登录失败次数、异常登录
- **设备识别** - 识别登录设备类型和浏览器信息
- **地理位置** - 记录登录IP和地理位置
- **会话管理** - 记录会话ID和在线时长

### 🚀 高性能设计

- **异步处理** - 使用线程池异步处理日志
- **事件驱动** - 基于Spring事件机制解耦
- **批量处理** - 支持批量写入优化性能
- **内存优化** - 智能截取长文本，避免内存溢出

### 🧹 自动清理

- **定时清理** - 定时清理过期日志
- **灵活配置** - 可配置保留天数和清理策略
- **分类清理** - 支持分别清理操作日志和登录日志

## 快速开始

### 1. 添加依赖

在你的模块的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>com.rega.erp</groupId>
    <artifactId>rega-common-log</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

在 `application.yml` 中添加配置：

```yaml
rega:
  log:
    # 是否启用日志模块
    enabled: true
    # 是否启用操作日志
    oper-log-enabled: true
    # 是否启用登录日志
    login-log-enabled: true
    # 是否启用异步处理
    async-enabled: true
    # 日志保留天数
    retention-days: 30
    # 是否启用自动清理
    clean-enabled: true
    # 清理任务执行时间（cron表达式）
    clean-cron: "0 0 2 * * ?"
    # 最大参数长度
    max-param-length: 2000
    # 最大返回值长度
    max-result-length: 2000
    # 最大异常信息长度
    max-error-length: 2000
    # 线程池配置
    thread-pool:
      core-pool-size: 2
      max-pool-size: 10
      queue-capacity: 200
      keep-alive-seconds: 60
      thread-name-prefix: "log-task-"
```

### 3. 使用操作日志

#### 基本使用

```java
@RestController
@RequestMapping("/user")
public class UserController {

    @GetMapping("/{id}")
    @Log(title = "用户管理", description = "查询用户信息", businessType = BusinessType.SELECT)
    public User getUser(@PathVariable Long id) {
        return userService.getById(id);
    }

    @PostMapping
    @Log(title = "用户管理", description = "新增用户", businessType = BusinessType.INSERT)
    public User createUser(@RequestBody User user) {
        return userService.save(user);
    }

    @PutMapping("/{id}")
    @Log(title = "用户管理", description = "更新用户", businessType = BusinessType.UPDATE)
    public User updateUser(@PathVariable Long id, @RequestBody User user) {
        return userService.updateById(id, user);
    }

    @DeleteMapping("/{id}")
    @Log(title = "用户管理", description = "删除用户", businessType = BusinessType.DELETE)
    public void deleteUser(@PathVariable Long id) {
        userService.deleteById(id);
    }
}
```

#### 高级配置

```java
@PostMapping("/login")
@Log(
    title = "系统登录",
    description = "用户登录系统",
    businessType = BusinessType.LOGIN,
    operatorType = OperatorType.MANAGE,
    isSaveRequestData = true,
    isSaveResponseData = false,
    isRecordException = true,
    excludeParamNames = {"password", "confirmPassword"},
    async = true
)
public LoginResult login(@RequestBody LoginRequest request) {
    return authService.login(request);
}
```

### 4. 使用登录日志

```java
@Service
public class AuthService {

    @Autowired
    private LogService logService;

    public LoginResult login(LoginRequest request) {
        try {
            // 执行登录逻辑
            User user = authenticate(request);
            
            // 记录登录成功日志
            LoginLog loginLog = LogUtils.createLoginLog();
            loginLog.setUserName(user.getUsername());
            loginLog.setUserNickName(user.getNickName());
            loginLog.setStatus(LogStatus.SUCCESS);
            loginLog.setMsg("登录成功");
            loginLog.setLoginType(0); // 密码登录
            loginLog.setLoginSource(0); // PC端
            
            logService.saveLoginLogAsync(loginLog);
            
            return LoginResult.success(user);
            
        } catch (Exception e) {
            // 记录登录失败日志
            LoginLog loginLog = LogUtils.createLoginLog();
            loginLog.setUserName(request.getUsername());
            loginLog.setStatus(LogStatus.FAIL);
            loginLog.setMsg("登录失败：" + e.getMessage());
            
            logService.saveLoginLogAsync(loginLog);
            
            throw e;
        }
    }
}
```

### 5. 事件驱动方式

```java
@Service
public class UserService {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    public void createUser(User user) {
        // 执行业务逻辑
        userRepository.save(user);
        
        // 发布操作日志事件
        OperLog operLog = LogUtils.createOperLog();
        operLog.setTitle("用户管理");
        operLog.setDescription("新增用户");
        operLog.setBusinessType(BusinessType.INSERT);
        operLog.setOperName(getCurrentUser().getUsername());
        operLog.setBusinessId(user.getId().toString());
        
        LogPublisher.publishOperLog(operLog);
    }
}
```

### 6. 自定义日志服务

```java
@Service
@Primary
public class DatabaseLogServiceImpl implements LogService {

    @Autowired
    private OperLogRepository operLogRepository;
    
    @Autowired
    private LoginLogRepository loginLogRepository;

    @Override
    public void saveOperLog(OperLog operLog) {
        operLogRepository.save(operLog);
    }

    @Override
    @Async("logTaskExecutor")
    public void saveOperLogAsync(OperLog operLog) {
        saveOperLog(operLog);
    }

    @Override
    public void saveLoginLog(LoginLog loginLog) {
        loginLogRepository.save(loginLog);
    }

    @Override
    @Async("logTaskExecutor")
    public void saveLoginLogAsync(LoginLog loginLog) {
        saveLoginLog(loginLog);
    }

    @Override
    public void cleanExpiredLogs(int days) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
        operLogRepository.deleteByOperTimeBefore(expireTime);
        loginLogRepository.deleteByLoginTimeBefore(expireTime);
    }
}
```

## 注解说明

### @Log 注解属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | String | "" | 模块名称 |
| description | String | "" | 功能描述 |
| businessType | BusinessType | OTHER | 业务类型 |
| operatorType | OperatorType | MANAGE | 操作人类别 |
| isSaveRequestData | boolean | true | 是否保存请求参数 |
| isSaveResponseData | boolean | true | 是否保存响应参数 |
| isRecordException | boolean | true | 是否记录异常信息 |
| excludeParamNames | String[] | {} | 排除的请求参数名 |
| async | boolean | true | 是否异步记录 |

### 业务类型 (BusinessType)

| 类型 | 代码 | 说明 |
|------|------|------|
| OTHER | 0 | 其它 |
| INSERT | 1 | 新增 |
| UPDATE | 2 | 修改 |
| DELETE | 3 | 删除 |
| GRANT | 4 | 授权 |
| EXPORT | 5 | 导出 |
| IMPORT | 6 | 导入 |
| FORCE | 7 | 强退 |
| GENCODE | 8 | 生成代码 |
| CLEAN | 9 | 清空数据 |
| SELECT | 10 | 查询 |
| LOGIN | 11 | 登录 |
| LOGOUT | 12 | 登出 |
| UPLOAD | 13 | 上传 |
| DOWNLOAD | 14 | 下载 |
| AUDIT | 15 | 审核 |
| PUBLISH | 16 | 发布 |
| REVOKE | 17 | 撤销 |
| SYNC | 18 | 同步 |
| BACKUP | 19 | 备份 |
| RESTORE | 20 | 恢复 |

### 操作人类别 (OperatorType)

| 类型 | 代码 | 说明 |
|------|------|------|
| OTHER | 0 | 其它 |
| MANAGE | 1 | 后台用户 |
| MOBILE | 2 | 手机端用户 |
| SYSTEM | 3 | 系统自动 |
| API | 4 | API接口 |
| SCHEDULE | 5 | 定时任务 |
| THIRD_PARTY | 6 | 第三方系统 |

## 工具类说明

### LogUtils

| 方法 | 说明 |
|------|------|
| `getClientIP()` | 获取客户端IP地址 |
| `getUserAgent()` | 获取用户代理信息 |
| `getBrowser()` | 获取浏览器信息 |
| `getOs()` | 获取操作系统信息 |
| `getDeviceType()` | 获取设备类型 |
| `createOperLog()` | 创建操作日志对象 |
| `createLoginLog()` | 创建登录日志对象 |
| `filterSensitiveParams()` | 过滤敏感参数 |
| `formatException()` | 格式化异常信息 |

### LogPublisher

| 方法 | 说明 |
|------|------|
| `publishOperLog(OperLog)` | 发布操作日志事件 |
| `publishLoginLog(LoginLog)` | 发布登录日志事件 |

## 扩展指南

### 1. 自定义日志服务

实现 `LogService` 接口，提供自己的日志存储逻辑：

```java
@Service
@Primary
public class CustomLogServiceImpl implements LogService {
    // 实现自定义逻辑
}
```

### 2. 自定义事件监听器

```java
@Component
public class CustomLogEventListener {

    @EventListener
    @Async("logTaskExecutor")
    public void handleOperLogEvent(OperLogEvent event) {
        // 自定义处理逻辑
    }
}
```

### 3. 扩展日志字段

继承 `OperLog` 或 `LoginLog` 类，添加自定义字段：

```java
@Data
@EqualsAndHashCode(callSuper = true)
public class ExtendedOperLog extends OperLog {
    private String customField1;
    private String customField2;
}
```

## 注意事项

1. **性能考虑**: 大量日志记录时建议使用异步模式
2. **存储空间**: 定期清理过期日志，避免存储空间不足
3. **敏感信息**: 注意过滤密码等敏感参数
4. **异常处理**: 日志记录失败不应影响业务流程
5. **线程安全**: 所有组件都是线程安全的

## 依赖说明

- Spring Boot 3.1.0
- Spring AOP
- Hutool 5.8.18
- Logback
- Logstash Logback Encoder
- rega-common-core (内部依赖)
