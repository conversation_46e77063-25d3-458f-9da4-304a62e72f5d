package com.rega.erp.common.log.example;

import com.rega.erp.common.log.annotation.Log;
import com.rega.erp.common.log.enums.BusinessType;
import com.rega.erp.common.log.enums.OperatorType;
import lombok.Data;
import org.springframework.web.bind.annotation.*;

/**
 * 日志功能示例控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/example/log")
public class LogExampleController {

    @Data
    public static class User {
        private Long id;
        private String username;
        private String password;
        private String email;
    }

    /**
     * 查询用户示例
     */
    @GetMapping("/user/{id}")
    @Log(title = "用户管理", description = "查询用户信息", businessType = BusinessType.SELECT)
    public User getUser(@PathVariable Long id) {
        User user = new User();
        user.setId(id);
        user.setUsername("admin");
        user.setEmail("<EMAIL>");
        return user;
    }

    /**
     * 新增用户示例
     */
    @PostMapping("/user")
    @Log(title = "用户管理", description = "新增用户", businessType = BusinessType.INSERT, 
         operatorType = OperatorType.MANAGE, excludeParamNames = {"password"})
    public User createUser(@RequestBody User user) {
        user.setId(System.currentTimeMillis());
        return user;
    }

    /**
     * 更新用户示例
     */
    @PutMapping("/user/{id}")
    @Log(title = "用户管理", description = "更新用户信息", businessType = BusinessType.UPDATE,
         excludeParamNames = {"password"})
    public User updateUser(@PathVariable Long id, @RequestBody User user) {
        user.setId(id);
        return user;
    }

    /**
     * 删除用户示例
     */
    @DeleteMapping("/user/{id}")
    @Log(title = "用户管理", description = "删除用户", businessType = BusinessType.DELETE)
    public String deleteUser(@PathVariable Long id) {
        return "删除成功";
    }

    /**
     * 导出用户示例
     */
    @GetMapping("/user/export")
    @Log(title = "用户管理", description = "导出用户数据", businessType = BusinessType.EXPORT,
         isSaveResponseData = false)
    public String exportUsers() {
        return "导出成功";
    }

    /**
     * 异常操作示例
     */
    @GetMapping("/error")
    @Log(title = "错误测试", description = "测试异常记录", businessType = BusinessType.OTHER)
    public String errorExample() {
        throw new RuntimeException("这是一个测试异常");
    }

    /**
     * 同步日志示例
     */
    @GetMapping("/sync")
    @Log(title = "同步测试", description = "测试同步日志记录", businessType = BusinessType.OTHER, async = false)
    public String syncLogExample() {
        return "同步日志记录成功";
    }

    /**
     * 不记录请求参数示例
     */
    @PostMapping("/no-request")
    @Log(title = "参数测试", description = "不记录请求参数", businessType = BusinessType.OTHER,
         isSaveRequestData = false)
    public String noRequestDataExample(@RequestBody User user) {
        return "不记录请求参数";
    }

    /**
     * 不记录响应数据示例
     */
    @GetMapping("/no-response")
    @Log(title = "响应测试", description = "不记录响应数据", businessType = BusinessType.OTHER,
         isSaveResponseData = false)
    public User noResponseDataExample() {
        User user = new User();
        user.setId(1L);
        user.setUsername("test");
        return user;
    }
}
