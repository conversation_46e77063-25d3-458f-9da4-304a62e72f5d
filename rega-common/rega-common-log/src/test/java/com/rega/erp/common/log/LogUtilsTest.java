package com.rega.erp.common.log;

import com.rega.erp.common.log.domain.LoginLog;
import com.rega.erp.common.log.domain.OperLog;
import com.rega.erp.common.log.enums.BusinessType;
import com.rega.erp.common.log.enums.LogStatus;
import com.rega.erp.common.log.enums.OperatorType;
import com.rega.erp.common.log.utils.LogUtils;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 日志工具类测试
 *
 * <AUTHOR>
 */
class LogUtilsTest {

    @Test
    void testCreateOperLog() {
        OperLog operLog = LogUtils.createOperLog();
        
        assertNotNull(operLog);
        assertNotNull(operLog.getOperTime());
        assertEquals(LogStatus.SUCCESS, operLog.getStatus());
        assertNotNull(operLog.getOperIp());
        assertNotNull(operLog.getUserAgent());
        assertNotNull(operLog.getBrowser());
        assertNotNull(operLog.getOs());
        assertNotNull(operLog.getDeviceType());
    }

    @Test
    void testCreateLoginLog() {
        LoginLog loginLog = LogUtils.createLoginLog();
        
        assertNotNull(loginLog);
        assertNotNull(loginLog.getLoginTime());
        assertEquals(LogStatus.SUCCESS, loginLog.getStatus());
        assertNotNull(loginLog.getIpaddr());
        assertNotNull(loginLog.getUserAgent());
        assertNotNull(loginLog.getBrowser());
        assertNotNull(loginLog.getOs());
        assertNotNull(loginLog.getDeviceType());
    }

    @Test
    void testFilterSensitiveParams() {
        String params = "{\"username\":\"admin\",\"password\":\"123456\",\"email\":\"<EMAIL>\"}";
        String[] excludeParams = {"password"};
        
        String filtered = LogUtils.filterSensitiveParams(params, excludeParams);
        
        assertNotNull(filtered);
        assertTrue(filtered.contains("\"password\":\"***\""));
        assertTrue(filtered.contains("\"username\":\"admin\""));
    }

    @Test
    void testSubstring() {
        String longStr = "这是一个很长的字符串，用来测试截取功能";
        String result = LogUtils.substring(longStr, 10);
        
        assertNotNull(result);
        assertEquals(10, result.length());
        assertEquals("这是一个很长的字符串，", result);
        
        // 测试短字符串
        String shortStr = "短字符串";
        String shortResult = LogUtils.substring(shortStr, 10);
        assertEquals(shortStr, shortResult);
        
        // 测试null
        String nullResult = LogUtils.substring(null, 10);
        assertNull(nullResult);
    }

    @Test
    void testFormatException() {
        Exception e = new RuntimeException("测试异常");
        String formatted = LogUtils.formatException(e);
        
        assertNotNull(formatted);
        assertTrue(formatted.contains("RuntimeException"));
        assertTrue(formatted.contains("测试异常"));
        
        // 测试null
        String nullResult = LogUtils.formatException(null);
        assertEquals("", nullResult);
    }

    @Test
    void testBusinessTypeEnum() {
        assertEquals(1, BusinessType.INSERT.getCode());
        assertEquals("新增", BusinessType.INSERT.getDescription());
        
        BusinessType type = BusinessType.getByCode(2);
        assertEquals(BusinessType.UPDATE, type);
        
        BusinessType unknownType = BusinessType.getByCode(999);
        assertEquals(BusinessType.OTHER, unknownType);
    }

    @Test
    void testOperatorTypeEnum() {
        assertEquals(1, OperatorType.MANAGE.getCode());
        assertEquals("后台用户", OperatorType.MANAGE.getDescription());
        
        OperatorType type = OperatorType.getByCode(2);
        assertEquals(OperatorType.MOBILE, type);
        
        OperatorType unknownType = OperatorType.getByCode(999);
        assertEquals(OperatorType.OTHER, unknownType);
    }

    @Test
    void testLogStatusEnum() {
        assertEquals(0, LogStatus.SUCCESS.getCode());
        assertEquals("成功", LogStatus.SUCCESS.getDescription());
        
        LogStatus status = LogStatus.getByCode(1);
        assertEquals(LogStatus.FAIL, status);
        
        LogStatus unknownStatus = LogStatus.getByCode(999);
        assertEquals(LogStatus.SUCCESS, unknownStatus);
    }
}
