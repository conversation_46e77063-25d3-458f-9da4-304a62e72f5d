package com.rega.erp.common.log.config;

import com.rega.erp.common.log.aspect.LogAspect;
import com.rega.erp.common.log.service.LogService;
import com.rega.erp.common.log.service.impl.DefaultLogServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

/**
 * 日志自动配置
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(LogConfig.class)
@ConditionalOnProperty(prefix = "rega.log", name = "enabled", havingValue = "true", matchIfMissing = true)
@Import({LogConfig.class})
public class LogAutoConfiguration {

    public LogAutoConfiguration() {
        log.info("RegaWebERP Log Module Auto Configuration Initialized");
    }

    /**
     * 日志切面
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "rega.log", name = "oper-log-enabled", havingValue = "true", matchIfMissing = true)
    public LogAspect logAspect(LogService logService) {
        log.info("Register LogAspect");
        return new LogAspect(logService);
    }

    /**
     * 默认日志服务
     */
    @Bean
    @ConditionalOnMissingBean
    public LogService logService() {
        log.info("Register DefaultLogService");
        return new DefaultLogServiceImpl();
    }
}
