package com.rega.erp.common.log.task;

import com.rega.erp.common.log.config.LogConfig;
import com.rega.erp.common.log.service.LogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 日志清理任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "rega.log", name = "clean-enabled", havingValue = "true", matchIfMissing = true)
public class LogCleanTask {

    private final LogService logService;
    private final LogConfig logConfig;

    /**
     * 清理过期日志
     * 默认每天凌晨2点执行
     */
    @Scheduled(cron = "${rega.log.clean-cron:0 0 2 * * ?}")
    public void cleanExpiredLogs() {
        try {
            log.info("开始清理过期日志，保留天数: {}", logConfig.getRetentionDays());
            long startTime = System.currentTimeMillis();
            
            logService.cleanExpiredLogs(logConfig.getRetentionDays());
            
            long costTime = System.currentTimeMillis() - startTime;
            log.info("清理过期日志完成，耗时: {}ms", costTime);
            
        } catch (Exception e) {
            log.error("清理过期日志失败", e);
        }
    }

    /**
     * 清理操作日志
     */
    public void cleanOperLogs() {
        try {
            log.info("开始清理操作日志，保留天数: {}", logConfig.getRetentionDays());
            logService.cleanOperLogs(logConfig.getRetentionDays());
            log.info("清理操作日志完成");
        } catch (Exception e) {
            log.error("清理操作日志失败", e);
        }
    }

    /**
     * 清理登录日志
     */
    public void cleanLoginLogs() {
        try {
            log.info("开始清理登录日志，保留天数: {}", logConfig.getRetentionDays());
            logService.cleanLoginLogs(logConfig.getRetentionDays());
            log.info("清理登录日志完成");
        } catch (Exception e) {
            log.error("清理登录日志失败", e);
        }
    }
}
