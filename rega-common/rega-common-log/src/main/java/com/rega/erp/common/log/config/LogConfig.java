package com.rega.erp.common.log.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 日志配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@EnableAsync
@ConfigurationProperties(prefix = "rega.log")
public class LogConfig {

    /**
     * 是否启用操作日志
     */
    private boolean operLogEnabled = true;

    /**
     * 是否启用登录日志
     */
    private boolean loginLogEnabled = true;

    /**
     * 是否启用异步日志
     */
    private boolean asyncEnabled = true;

    /**
     * 日志保留天数
     */
    private int retentionDays = 30;

    /**
     * 是否启用日志清理任务
     */
    private boolean cleanEnabled = true;

    /**
     * 日志清理任务执行时间（cron表达式）
     */
    private String cleanCron = "0 0 2 * * ?";

    /**
     * 最大参数长度
     */
    private int maxParamLength = 2000;

    /**
     * 最大返回值长度
     */
    private int maxResultLength = 2000;

    /**
     * 最大异常信息长度
     */
    private int maxErrorLength = 2000;

    /**
     * 线程池配置
     */
    private ThreadPool threadPool = new ThreadPool();

    @Data
    public static class ThreadPool {
        /**
         * 核心线程数
         */
        private int corePoolSize = 2;

        /**
         * 最大线程数
         */
        private int maxPoolSize = 10;

        /**
         * 队列容量
         */
        private int queueCapacity = 200;

        /**
         * 线程空闲时间
         */
        private int keepAliveSeconds = 60;

        /**
         * 线程名前缀
         */
        private String threadNamePrefix = "log-task-";
    }

    /**
     * 日志任务执行器
     */
    @Bean("logTaskExecutor")
    public Executor logTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadPool.getCorePoolSize());
        executor.setMaxPoolSize(threadPool.getMaxPoolSize());
        executor.setQueueCapacity(threadPool.getQueueCapacity());
        executor.setKeepAliveSeconds(threadPool.getKeepAliveSeconds());
        executor.setThreadNamePrefix(threadPool.getThreadNamePrefix());
        
        // 拒绝策略：由调用线程处理该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        return executor;
    }
}
