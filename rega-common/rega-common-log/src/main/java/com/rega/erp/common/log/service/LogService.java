package com.rega.erp.common.log.service;

import com.rega.erp.common.log.domain.LoginLog;
import com.rega.erp.common.log.domain.OperLog;

/**
 * 日志服务接口
 *
 * <AUTHOR>
 */
public interface LogService {

    /**
     * 保存操作日志
     *
     * @param operLog 操作日志信息
     */
    void saveOperLog(OperLog operLog);

    /**
     * 异步保存操作日志
     *
     * @param operLog 操作日志信息
     */
    void saveOperLogAsync(OperLog operLog);

    /**
     * 保存登录日志
     *
     * @param loginLog 登录日志信息
     */
    void saveLoginLog(LoginLog loginLog);

    /**
     * 异步保存登录日志
     *
     * @param loginLog 登录日志信息
     */
    void saveLoginLogAsync(LoginLog loginLog);

    /**
     * 清理过期日志
     *
     * @param days 保留天数
     */
    void cleanExpiredLogs(int days);

    /**
     * 清理操作日志
     *
     * @param days 保留天数
     */
    void cleanOperLogs(int days);

    /**
     * 清理登录日志
     *
     * @param days 保留天数
     */
    void cleanLoginLogs(int days);
}
