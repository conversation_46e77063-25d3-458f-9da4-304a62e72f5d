package com.rega.erp.common.log.publisher;

import com.rega.erp.common.log.domain.LoginLog;
import com.rega.erp.common.log.domain.OperLog;
import com.rega.erp.common.log.event.LoginLogEvent;
import com.rega.erp.common.log.event.OperLogEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Component;

/**
 * 日志事件发布器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LogPublisher implements ApplicationEventPublisherAware {

    private static ApplicationEventPublisher publisher;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        LogPublisher.publisher = applicationEventPublisher;
    }

    /**
     * 发布操作日志事件
     *
     * @param operLog 操作日志
     */
    public static void publishOperLog(OperLog operLog) {
        try {
            if (publisher != null && operLog != null) {
                publisher.publishEvent(new OperLogEvent(LogPublisher.class, operLog));
            }
        } catch (Exception e) {
            log.error("发布操作日志事件失败", e);
        }
    }

    /**
     * 发布登录日志事件
     *
     * @param loginLog 登录日志
     */
    public static void publishLoginLog(LoginLog loginLog) {
        try {
            if (publisher != null && loginLog != null) {
                publisher.publishEvent(new LoginLogEvent(LogPublisher.class, loginLog));
            }
        } catch (Exception e) {
            log.error("发布登录日志事件失败", e);
        }
    }
}
