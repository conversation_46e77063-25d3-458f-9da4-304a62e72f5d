package com.rega.erp.common.log.listener;

import com.rega.erp.common.log.event.LoginLogEvent;
import com.rega.erp.common.log.event.OperLogEvent;
import com.rega.erp.common.log.service.LogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 日志事件监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LogEventListener {

    private final LogService logService;

    /**
     * 监听操作日志事件
     */
    @Async("logTaskExecutor")
    @EventListener
    public void handleOperLogEvent(OperLogEvent event) {
        try {
            logService.saveOperLog(event.getOperLog());
        } catch (Exception e) {
            log.error("处理操作日志事件失败", e);
        }
    }

    /**
     * 监听登录日志事件
     */
    @Async("logTaskExecutor")
    @EventListener
    public void handleLoginLogEvent(LoginLogEvent event) {
        try {
            logService.saveLoginLog(event.getLoginLog());
        } catch (Exception e) {
            log.error("处理登录日志事件失败", e);
        }
    }
}
