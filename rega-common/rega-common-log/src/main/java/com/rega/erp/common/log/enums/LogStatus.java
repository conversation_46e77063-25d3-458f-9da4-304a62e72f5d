package com.rega.erp.common.log.enums;

/**
 * 日志状态
 *
 * <AUTHOR>
 */
public enum LogStatus {
    /**
     * 成功
     */
    SUCCESS(0, "成功"),

    /**
     * 失败
     */
    FAIL(1, "失败"),

    /**
     * 异常
     */
    ERROR(2, "异常");

    private final int code;
    private final String description;

    LogStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取日志状态
     */
    public static LogStatus getByCode(int code) {
        for (LogStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return SUCCESS;
    }
}
