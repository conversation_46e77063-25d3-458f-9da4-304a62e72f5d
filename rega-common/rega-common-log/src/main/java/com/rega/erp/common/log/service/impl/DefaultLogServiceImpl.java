package com.rega.erp.common.log.service.impl;

import com.rega.erp.common.log.domain.LoginLog;
import com.rega.erp.common.log.domain.OperLog;
import com.rega.erp.common.log.service.LogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 默认日志服务实现（使用日志框架记录）
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@ConditionalOnMissingBean(LogService.class)
public class DefaultLogServiceImpl implements LogService {

    @Override
    public void saveOperLog(OperLog operLog) {
        log.info("操作日志: 用户[{}] 在模块[{}] 执行[{}]操作, 方法[{}], 状态[{}], 耗时[{}]ms",
                operLog.getOperName(),
                operLog.getTitle(),
                operLog.getBusinessType().getDescription(),
                operLog.getMethod(),
                operLog.getStatus().getDescription(),
                operLog.getCostTime());

        if (operLog.getErrorMsg() != null) {
            log.error("操作异常: {}", operLog.getErrorMsg());
        }
    }

    @Override
    @Async("logTaskExecutor")
    public void saveOperLogAsync(OperLog operLog) {
        saveOperLog(operLog);
    }

    @Override
    public void saveLoginLog(LoginLog loginLog) {
        log.info("登录日志: 用户[{}] 从[{}] 登录系统, IP[{}], 状态[{}], 浏览器[{}], 操作系统[{}]",
                loginLog.getUserName(),
                loginLog.getLoginLocation(),
                loginLog.getIpaddr(),
                loginLog.getStatus().getDescription(),
                loginLog.getBrowser(),
                loginLog.getOs());

        if (loginLog.getMsg() != null) {
            log.info("登录消息: {}", loginLog.getMsg());
        }
    }

    @Override
    @Async("logTaskExecutor")
    public void saveLoginLogAsync(LoginLog loginLog) {
        saveLoginLog(loginLog);
    }

    @Override
    public void cleanExpiredLogs(int days) {
        log.info("清理{}天前的过期日志", days);
        cleanOperLogs(days);
        cleanLoginLogs(days);
    }

    @Override
    public void cleanOperLogs(int days) {
        log.info("清理{}天前的操作日志", days);
        // 默认实现不做实际清理，由具体实现类处理
    }

    @Override
    public void cleanLoginLogs(int days) {
        log.info("清理{}天前的登录日志", days);
        // 默认实现不做实际清理，由具体实现类处理
    }
}
