package com.rega.erp.common.log.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.rega.erp.common.core.util.JsonUtils;
import com.rega.erp.common.log.domain.LoginLog;
import com.rega.erp.common.log.domain.OperLog;
import com.rega.erp.common.log.enums.LogStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 日志工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class LogUtils {

    /**
     * 获取客户端IP
     */
    public static String getClientIP() {
        try {
            HttpServletRequest request = getRequest();
            if (request != null) {
                return ServletUtil.getClientIP(request);
            }
        } catch (Exception e) {
            log.warn("获取客户端IP失败", e);
        }
        return "unknown";
    }

    /**
     * 获取用户代理信息
     */
    public static String getUserAgent() {
        try {
            HttpServletRequest request = getRequest();
            if (request != null) {
                return request.getHeader("User-Agent");
            }
        } catch (Exception e) {
            log.warn("获取用户代理信息失败", e);
        }
        return "unknown";
    }

    /**
     * 解析用户代理信息
     */
    public static UserAgent parseUserAgent(String userAgentString) {
        try {
            if (StrUtil.isNotBlank(userAgentString)) {
                return UserAgentUtil.parse(userAgentString);
            }
        } catch (Exception e) {
            log.warn("解析用户代理信息失败: {}", userAgentString, e);
        }
        return null;
    }

    /**
     * 获取浏览器信息
     */
    public static String getBrowser() {
        UserAgent userAgent = parseUserAgent(getUserAgent());
        return userAgent != null ? userAgent.getBrowser().toString() : "unknown";
    }

    /**
     * 获取操作系统信息
     */
    public static String getOs() {
        UserAgent userAgent = parseUserAgent(getUserAgent());
        return userAgent != null ? userAgent.getOs().toString() : "unknown";
    }

    /**
     * 获取设备类型
     */
    public static String getDeviceType() {
        UserAgent userAgent = parseUserAgent(getUserAgent());
        if (userAgent != null) {
            if (userAgent.isMobile()) {
                return "Mobile";
            } else if (userAgent.isTablet()) {
                return "Tablet";
            } else {
                return "Desktop";
            }
        }
        return "unknown";
    }

    /**
     * 获取请求URI
     */
    public static String getRequestURI() {
        try {
            HttpServletRequest request = getRequest();
            if (request != null) {
                return request.getRequestURI();
            }
        } catch (Exception e) {
            log.warn("获取请求URI失败", e);
        }
        return "";
    }

    /**
     * 获取请求方法
     */
    public static String getRequestMethod() {
        try {
            HttpServletRequest request = getRequest();
            if (request != null) {
                return request.getMethod();
            }
        } catch (Exception e) {
            log.warn("获取请求方法失败", e);
        }
        return "";
    }

    /**
     * 获取请求参数
     */
    public static String getRequestParams() {
        try {
            HttpServletRequest request = getRequest();
            if (request != null) {
                Map<String, String[]> parameterMap = request.getParameterMap();
                if (!parameterMap.isEmpty()) {
                    return JsonUtils.toJsonString(parameterMap);
                }
            }
        } catch (Exception e) {
            log.warn("获取请求参数失败", e);
        }
        return "";
    }

    /**
     * 获取HttpServletRequest
     */
    private static HttpServletRequest getRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 创建操作日志
     */
    public static OperLog createOperLog() {
        OperLog operLog = new OperLog();
        operLog.setOperTime(LocalDateTime.now());
        operLog.setOperIp(getClientIP());
        operLog.setOperUrl(getRequestURI());
        operLog.setRequestMethod(getRequestMethod());
        operLog.setUserAgent(getUserAgent());
        operLog.setBrowser(getBrowser());
        operLog.setOs(getOs());
        operLog.setDeviceType(getDeviceType());
        operLog.setStatus(LogStatus.SUCCESS);
        return operLog;
    }

    /**
     * 创建登录日志
     */
    public static LoginLog createLoginLog() {
        LoginLog loginLog = new LoginLog();
        loginLog.setLoginTime(LocalDateTime.now());
        loginLog.setIpaddr(getClientIP());
        loginLog.setUserAgent(getUserAgent());
        loginLog.setBrowser(getBrowser());
        loginLog.setOs(getOs());
        loginLog.setDeviceType(getDeviceType());
        loginLog.setStatus(LogStatus.SUCCESS);
        return loginLog;
    }

    /**
     * 过滤敏感参数
     */
    public static String filterSensitiveParams(String params, String[] excludeParamNames) {
        if (StrUtil.isBlank(params) || excludeParamNames == null || excludeParamNames.length == 0) {
            return params;
        }

        try {
            // 这里可以实现具体的敏感参数过滤逻辑
            // 例如：密码、身份证号、手机号等
            String filteredParams = params;
            for (String excludeParam : excludeParamNames) {
                if (StrUtil.isNotBlank(excludeParam)) {
                    // 简单的字符串替换，实际项目中可以使用更复杂的JSON解析和过滤
                    filteredParams = filteredParams.replaceAll(
                            "\"" + excludeParam + "\"\\s*:\\s*\"[^\"]*\"",
                            "\"" + excludeParam + "\":\"***\""
                    );
                }
            }
            return filteredParams;
        } catch (Exception e) {
            log.warn("过滤敏感参数失败", e);
            return params;
        }
    }

    /**
     * 截取字符串
     */
    public static String substring(String str, int maxLength) {
        if (StrUtil.isBlank(str)) {
            return str;
        }
        return str.length() > maxLength ? str.substring(0, maxLength) : str;
    }

    /**
     * 格式化异常信息
     */
    public static String formatException(Throwable e) {
        if (e == null) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(e.getClass().getSimpleName()).append(": ").append(e.getMessage());
        
        StackTraceElement[] stackTrace = e.getStackTrace();
        if (stackTrace != null && stackTrace.length > 0) {
            sb.append("\n");
            // 只记录前5行堆栈信息
            int maxLines = Math.min(5, stackTrace.length);
            for (int i = 0; i < maxLines; i++) {
                sb.append("\tat ").append(stackTrace[i].toString()).append("\n");
            }
        }
        
        return substring(sb.toString(), 2000);
    }
}
