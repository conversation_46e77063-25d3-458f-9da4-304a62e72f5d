package com.rega.erp.common.log.enums;

/**
 * 操作人类别
 *
 * <AUTHOR>
 */
public enum OperatorType {
    /**
     * 其它
     */
    OTHER(0, "其它"),

    /**
     * 后台用户
     */
    MANAGE(1, "后台用户"),

    /**
     * 手机端用户
     */
    MOBILE(2, "手机端用户"),

    /**
     * 系统自动
     */
    SYSTEM(3, "系统自动"),

    /**
     * API接口
     */
    API(4, "API接口"),

    /**
     * 定时任务
     */
    SCHEDULE(5, "定时任务"),

    /**
     * 第三方系统
     */
    THIRD_PARTY(6, "第三方系统");

    private final int code;
    private final String description;

    OperatorType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取操作人类别
     */
    public static OperatorType getByCode(int code) {
        for (OperatorType type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return OTHER;
    }
}
