package com.rega.erp.common.log.domain;

import com.rega.erp.common.core.domain.BaseEntity;
import com.rega.erp.common.log.enums.LogStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 系统访问记录表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LoginLog extends BaseEntity {

    /**
     * 访问ID
     */
    private Long infoId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户名称
     */
    private String userNickName;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 登录状态
     */
    private LogStatus status;

    /**
     * 提示消息
     */
    private String msg;

    /**
     * 访问时间
     */
    private LocalDateTime loginTime;

    /**
     * 登出时间
     */
    private LocalDateTime logoutTime;

    /**
     * 在线时长（分钟）
     */
    private Long onlineTime;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 登录方式（0密码登录 1短信登录 2扫码登录 3第三方登录）
     */
    private Integer loginType;

    /**
     * 登录来源（0PC 1移动端 2小程序 3APP）
     */
    private Integer loginSource;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * Token
     */
    private String token;

    /**
     * 登录失败次数
     */
    private Integer failCount;

    /**
     * 是否首次登录
     */
    private Boolean firstLogin;

    /**
     * 扩展字段1
     */
    private String ext1;

    /**
     * 扩展字段2
     */
    private String ext2;

    /**
     * 扩展字段3
     */
    private String ext3;
}
