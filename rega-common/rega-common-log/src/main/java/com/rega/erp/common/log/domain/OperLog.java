package com.rega.erp.common.log.domain;

import com.rega.erp.common.core.domain.BaseEntity;
import com.rega.erp.common.log.enums.BusinessType;
import com.rega.erp.common.log.enums.LogStatus;
import com.rega.erp.common.log.enums.OperatorType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作日志记录表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OperLog extends BaseEntity {

    /**
     * 日志主键
     */
    private Long operId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 模块标题
     */
    private String title;

    /**
     * 业务类型
     */
    private BusinessType businessType;

    /**
     * 方法名称
     */
    private String method;

    /**
     * 请求方式
     */
    private String requestMethod;

    /**
     * 操作类别
     */
    private OperatorType operatorType;

    /**
     * 操作人员
     */
    private String operName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 请求URL
     */
    private String operUrl;

    /**
     * 主机地址
     */
    private String operIp;

    /**
     * 操作地点
     */
    private String operLocation;

    /**
     * 请求参数
     */
    private String operParam;

    /**
     * 返回参数
     */
    private String jsonResult;

    /**
     * 操作状态
     */
    private LogStatus status;

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 操作时间
     */
    private LocalDateTime operTime;

    /**
     * 消耗时间（毫秒）
     */
    private Long costTime;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 操作描述
     */
    private String description;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 业务数据
     */
    private String businessData;

    /**
     * 扩展字段1
     */
    private String ext1;

    /**
     * 扩展字段2
     */
    private String ext2;

    /**
     * 扩展字段3
     */
    private String ext3;

    /**
     * 扩展字段4
     */
    private String ext4;

    /**
     * 扩展字段5
     */
    private String ext5;
}
