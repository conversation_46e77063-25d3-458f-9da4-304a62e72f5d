package com.rega.erp.common.log.enums;

/**
 * 业务操作类型
 *
 * <AUTHOR>
 */
public enum BusinessType {
    /**
     * 其它
     */
    OTHER(0, "其它"),

    /**
     * 新增
     */
    INSERT(1, "新增"),

    /**
     * 修改
     */
    UPDATE(2, "修改"),

    /**
     * 删除
     */
    DELETE(3, "删除"),

    /**
     * 授权
     */
    GRANT(4, "授权"),

    /**
     * 导出
     */
    EXPORT(5, "导出"),

    /**
     * 导入
     */
    IMPORT(6, "导入"),

    /**
     * 强退
     */
    FORCE(7, "强退"),

    /**
     * 生成代码
     */
    GENCODE(8, "生成代码"),

    /**
     * 清空数据
     */
    CLEAN(9, "清空数据"),

    /**
     * 查询
     */
    SELECT(10, "查询"),

    /**
     * 登录
     */
    LOGIN(11, "登录"),

    /**
     * 登出
     */
    LOGOUT(12, "登出"),

    /**
     * 上传
     */
    UPLOAD(13, "上传"),

    /**
     * 下载
     */
    DOWNLOAD(14, "下载"),

    /**
     * 审核
     */
    AUDIT(15, "审核"),

    /**
     * 发布
     */
    PUBLISH(16, "发布"),

    /**
     * 撤销
     */
    REVOKE(17, "撤销"),

    /**
     * 同步
     */
    SYNC(18, "同步"),

    /**
     * 备份
     */
    BACKUP(19, "备份"),

    /**
     * 恢复
     */
    RESTORE(20, "恢复");

    private final int code;
    private final String description;

    BusinessType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取业务类型
     */
    public static BusinessType getByCode(int code) {
        for (BusinessType type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return OTHER;
    }
}
