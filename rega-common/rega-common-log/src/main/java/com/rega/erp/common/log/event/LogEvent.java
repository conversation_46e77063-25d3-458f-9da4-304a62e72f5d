package com.rega.erp.common.log.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 日志事件基类
 *
 * <AUTHOR>
 */
@Getter
public abstract class LogEvent extends ApplicationEvent {

    /**
     * 事件类型
     */
    private final String eventType;

    /**
     * 事件时间戳
     */
    private final long timestamp;

    public LogEvent(Object source, String eventType) {
        super(source);
        this.eventType = eventType;
        this.timestamp = System.currentTimeMillis();
    }
}
