package com.rega.erp.common.log.aspect;

import cn.hutool.core.util.StrUtil;
import com.rega.erp.common.core.util.JsonUtils;
import com.rega.erp.common.log.annotation.Log;
import com.rega.erp.common.log.domain.OperLog;
import com.rega.erp.common.log.enums.LogStatus;
import com.rega.erp.common.log.service.LogService;
import com.rega.erp.common.log.utils.LogUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.LocalDateTime;

/**
 * 操作日志记录处理切面
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class LogAspect {

    private final LogService logService;

    /**
     * 线程本地变量，存储操作开始时间和日志对象
     */
    private static final ThreadLocal<Long> START_TIME = new ThreadLocal<>();
    private static final ThreadLocal<OperLog> OPER_LOG = new ThreadLocal<>();

    /**
     * 处理请求前执行
     */
    @Before(value = "@annotation(controllerLog)")
    public void boBefore(JoinPoint joinPoint, Log controllerLog) {
        // 记录开始时间
        START_TIME.set(System.currentTimeMillis());

        // 创建操作日志对象
        OperLog operLog = LogUtils.createOperLog();
        
        // 设置基本信息
        operLog.setTitle(controllerLog.title());
        operLog.setDescription(controllerLog.description());
        operLog.setBusinessType(controllerLog.businessType());
        operLog.setOperatorType(controllerLog.operatorType());

        // 设置方法信息
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();
        operLog.setMethod(className + "." + methodName + "()");

        // 设置请求参数
        if (controllerLog.isSaveRequestData()) {
            setRequestValue(joinPoint, operLog, controllerLog.excludeParamNames());
        }

        // 存储到线程本地变量
        OPER_LOG.set(operLog);
    }

    /**
     * 处理完请求后执行
     */
    @AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, Log controllerLog, Object jsonResult) {
        handleLog(joinPoint, controllerLog, null, jsonResult);
    }

    /**
     * 拦截异常操作
     */
    @AfterThrowing(value = "@annotation(controllerLog)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Log controllerLog, Exception e) {
        handleLog(joinPoint, controllerLog, e, null);
    }

    /**
     * 处理日志
     */
    protected void handleLog(final JoinPoint joinPoint, Log controllerLog, final Exception e, Object jsonResult) {
        try {
            OperLog operLog = OPER_LOG.get();
            if (operLog == null) {
                return;
            }

            // 计算执行时间
            Long startTime = START_TIME.get();
            if (startTime != null) {
                operLog.setCostTime(System.currentTimeMillis() - startTime);
            }

            // 设置操作状态
            if (e != null) {
                operLog.setStatus(LogStatus.FAIL);
                if (controllerLog.isRecordException()) {
                    operLog.setErrorMsg(LogUtils.formatException(e));
                }
            } else {
                operLog.setStatus(LogStatus.SUCCESS);
            }

            // 设置返回参数
            if (controllerLog.isSaveResponseData() && jsonResult != null) {
                operLog.setJsonResult(LogUtils.substring(JsonUtils.toJsonString(jsonResult), 2000));
            }

            // 获取当前用户信息（这里需要根据实际的用户获取方式来实现）
            setUserInfo(operLog);

            // 保存日志
            if (controllerLog.async()) {
                logService.saveOperLogAsync(operLog);
            } else {
                logService.saveOperLog(operLog);
            }

        } catch (Exception ex) {
            log.error("记录操作日志异常", ex);
        } finally {
            // 清理线程本地变量
            START_TIME.remove();
            OPER_LOG.remove();
        }
    }

    /**
     * 获取请求的参数，放到log中
     */
    private void setRequestValue(JoinPoint joinPoint, OperLog operLog, String[] excludeParamNames) {
        try {
            String requestMethod = operLog.getRequestMethod();
            if ("PUT".equals(requestMethod) || "POST".equals(requestMethod)) {
                String params = argsArrayToString(joinPoint.getArgs(), excludeParamNames);
                operLog.setOperParam(LogUtils.substring(params, 2000));
            } else {
                String params = LogUtils.getRequestParams();
                operLog.setOperParam(LogUtils.substring(
                        LogUtils.filterSensitiveParams(params, excludeParamNames), 2000));
            }
        } catch (Exception e) {
            log.warn("获取请求参数异常", e);
        }
    }

    /**
     * 设置用户信息
     */
    private void setUserInfo(OperLog operLog) {
        try {
            // TODO: 这里需要根据实际的用户获取方式来实现
            // 例如：从SecurityContext、Session或Token中获取用户信息
            
            // 示例代码（需要根据实际情况修改）:
            // Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            // if (authentication != null && authentication.getPrincipal() instanceof UserDetails) {
            //     UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            //     operLog.setOperName(userDetails.getUsername());
            // }
            
            // 临时设置默认值
            operLog.setOperName("system");
            operLog.setDeptName("系统部门");
            
        } catch (Exception e) {
            log.warn("获取用户信息异常", e);
        }
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray, String[] excludeParamNames) {
        StringBuilder params = new StringBuilder();
        if (paramsArray != null && paramsArray.length > 0) {
            for (Object o : paramsArray) {
                if (o != null && !isFilterObject(o)) {
                    try {
                        String jsonObj = JsonUtils.toJsonString(o);
                        params.append(jsonObj).append(" ");
                    } catch (Exception e) {
                        log.warn("参数序列化异常", e);
                    }
                }
            }
        }
        return LogUtils.filterSensitiveParams(params.toString().trim(), excludeParamNames);
    }

    /**
     * 判断是否需要过滤的对象
     */
    private boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(jakarta.servlet.http.HttpServletRequest.class)
                    || clazz.getComponentType().isAssignableFrom(jakarta.servlet.http.HttpServletResponse.class);
        }
        return o instanceof jakarta.servlet.http.HttpServletRequest
                || o instanceof jakarta.servlet.http.HttpServletResponse
                || o instanceof org.springframework.web.multipart.MultipartFile;
    }
}
