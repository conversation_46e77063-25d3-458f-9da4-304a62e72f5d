package com.rega.erp.common.cache.util;

import com.rega.erp.common.cache.core.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 缓存工具类
 * 提供便捷的缓存操作方法
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "rega.cache", name = "enabled", havingValue = "true", matchIfMissing = true)
@ConditionalOnBean(CacheService.class)
public class CacheUtils {
    
    private static CacheService cacheService;
    
    @Autowired
    public void setCacheService(CacheService cacheService) {
        CacheUtils.cacheService = cacheService;
    }
    
    // ==================== 基本操作 ====================
    
    /**
     * 设置缓存
     */
    public static void set(String key, Object value) {
        cacheService.set(key, value);
    }
    
    /**
     * 设置缓存（带过期时间）
     */
    public static void set(String key, Object value, long timeout, TimeUnit timeUnit) {
        cacheService.set(key, value, timeout, timeUnit);
    }
    
    /**
     * 设置缓存（带过期时间）
     */
    public static void set(String key, Object value, Duration duration) {
        cacheService.set(key, value, duration);
    }
    
    /**
     * 获取缓存
     */
    public static <T> T get(String key) {
        return cacheService.get(key);
    }
    
    /**
     * 获取缓存（指定类型）
     */
    public static <T> T get(String key, Class<T> clazz) {
        return cacheService.get(key, clazz);
    }
    
    /**
     * 获取缓存，如果不存在则通过加载器获取并缓存
     */
    public static <T> T get(String key, Function<String, T> loader) {
        return cacheService.get(key, loader);
    }
    
    /**
     * 获取缓存，如果不存在则通过加载器获取并缓存（带过期时间）
     */
    public static <T> T get(String key, Function<String, T> loader, long timeout, TimeUnit timeUnit) {
        return cacheService.get(key, loader, timeout, timeUnit);
    }
    
    /**
     * 删除缓存
     */
    public static boolean delete(String key) {
        return cacheService.delete(key);
    }
    
    /**
     * 批量删除缓存
     */
    public static long delete(Collection<String> keys) {
        return cacheService.delete(keys);
    }
    
    /**
     * 检查缓存是否存在
     */
    public static boolean exists(String key) {
        return cacheService.exists(key);
    }
    
    /**
     * 设置过期时间
     */
    public static boolean expire(String key, long timeout, TimeUnit timeUnit) {
        return cacheService.expire(key, timeout, timeUnit);
    }
    
    /**
     * 获取剩余过期时间
     */
    public static long getExpire(String key) {
        return cacheService.getExpire(key);
    }
    
    // ==================== 批量操作 ====================
    
    /**
     * 批量设置缓存
     */
    public static void multiSet(Map<String, Object> map) {
        cacheService.multiSet(map);
    }
    
    /**
     * 批量获取缓存
     */
    public static <T> Map<String, T> multiGet(Collection<String> keys) {
        return cacheService.multiGet(keys);
    }
    
    // ==================== 模式匹配 ====================
    
    /**
     * 根据模式获取键集合
     */
    public static Set<String> keys(String pattern) {
        return cacheService.keys(pattern);
    }
    
    /**
     * 根据模式删除缓存
     */
    public static long deleteByPattern(String pattern) {
        return cacheService.deleteByPattern(pattern);
    }
    
    // ==================== 原子操作 ====================
    
    /**
     * 原子递增
     */
    public static long increment(String key) {
        return cacheService.increment(key);
    }
    
    /**
     * 原子递增（指定步长）
     */
    public static long increment(String key, long delta) {
        return cacheService.increment(key, delta);
    }
    
    /**
     * 原子递减
     */
    public static long decrement(String key) {
        return cacheService.decrement(key);
    }
    
    /**
     * 原子递减（指定步长）
     */
    public static long decrement(String key, long delta) {
        return cacheService.decrement(key, delta);
    }
    
    // ==================== 分布式锁 ====================
    
    /**
     * 尝试获取分布式锁
     */
    public static boolean tryLock(String lockKey, long timeout, TimeUnit unit) {
        return cacheService.tryLock(lockKey, timeout, unit);
    }
    
    /**
     * 释放分布式锁
     */
    public static boolean unlock(String lockKey) {
        return cacheService.unlock(lockKey);
    }
    
    /**
     * 在锁保护下执行操作
     */
    public static boolean executeWithLock(String lockKey, long timeout, TimeUnit unit, Runnable runnable) {
        return cacheService.executeWithLock(lockKey, timeout, unit, runnable);
    }
    
    /**
     * 在锁保护下执行操作（带返回值）
     */
    public static <T> T executeWithLock(String lockKey, long timeout, TimeUnit unit, Function<String, T> supplier) {
        return cacheService.executeWithLock(lockKey, timeout, unit, supplier);
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 设置缓存（1小时过期）
     */
    public static void setHour(String key, Object value) {
        set(key, value, 1, TimeUnit.HOURS);
    }
    
    /**
     * 设置缓存（1天过期）
     */
    public static void setDay(String key, Object value) {
        set(key, value, 1, TimeUnit.DAYS);
    }
    
    /**
     * 设置缓存（30分钟过期）
     */
    public static void setMinutes(String key, Object value, long minutes) {
        set(key, value, minutes, TimeUnit.MINUTES);
    }
    
    /**
     * 设置缓存（指定秒数过期）
     */
    public static void setSeconds(String key, Object value, long seconds) {
        set(key, value, seconds, TimeUnit.SECONDS);
    }
    
    /**
     * 获取字符串缓存
     */
    public static String getString(String key) {
        return get(key, String.class);
    }
    
    /**
     * 获取整数缓存
     */
    public static Integer getInteger(String key) {
        return get(key, Integer.class);
    }
    
    /**
     * 获取长整数缓存
     */
    public static Long getLong(String key) {
        return get(key, Long.class);
    }
    
    /**
     * 获取布尔值缓存
     */
    public static Boolean getBoolean(String key) {
        return get(key, Boolean.class);
    }
    
    /**
     * 构建缓存键
     */
    public static String buildKey(String... parts) {
        return String.join(":", parts);
    }
    
    /**
     * 构建用户缓存键
     */
    public static String buildUserKey(String userId, String suffix) {
        return buildKey("user", userId, suffix);
    }
    
    /**
     * 构建业务缓存键
     */
    public static String buildBusinessKey(String module, String business, String id) {
        return buildKey(module, business, id);
    }
    
    /**
     * 清空所有缓存
     */
    public static void clear() {
        cacheService.clear();
    }
}
