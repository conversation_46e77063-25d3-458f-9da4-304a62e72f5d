package com.rega.erp.common.cache.tenant;

import com.rega.erp.common.cache.core.CacheService;
import com.rega.erp.common.cache.core.CacheStats;
import com.rega.erp.common.core.context.TenantContextHolder;
import com.rega.erp.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 租户缓存管理器
 * 为多租户环境提供缓存隔离和管理
 *
 * <AUTHOR>
 */
@Slf4j
public class TenantCacheManager implements CacheService {
    
    private final CacheService delegateCache;
    private final String keyPrefix;
    private final String keySeparator;
    
    public TenantCacheManager(CacheService delegateCache) {
        this(delegateCache, "tenant", ":");
    }
    
    public TenantCacheManager(CacheService delegateCache, String keyPrefix, String keySeparator) {
        this.delegateCache = delegateCache;
        this.keyPrefix = keyPrefix;
        this.keySeparator = keySeparator;
    }
    
    /**
     * 构建租户缓存键
     *
     * @param key 原始键
     * @return 租户缓存键
     */
    private String buildTenantKey(String key) {
        String tenantId = TenantContextHolder.getTenantId();
        if (!StringUtils.hasText(tenantId)) {
            throw new BusinessException("租户上下文为空，无法构建租户缓存键");
        }
        return keyPrefix + keySeparator + tenantId + keySeparator + key;
    }
    
    /**
     * 批量构建租户缓存键
     *
     * @param keys 原始键集合
     * @return 租户缓存键集合
     */
    private Set<String> buildTenantKeys(Collection<String> keys) {
        return keys.stream()
                .map(this::buildTenantKey)
                .collect(Collectors.toSet());
    }
    
    /**
     * 构建租户缓存键映射
     *
     * @param map 原始键值映射
     * @return 租户缓存键值映射
     */
    private Map<String, Object> buildTenantKeyMap(Map<String, Object> map) {
        return map.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> buildTenantKey(entry.getKey()),
                        Map.Entry::getValue
                ));
    }
    
    /**
     * 从租户缓存键中提取原始键
     *
     * @param tenantKey 租户缓存键
     * @return 原始键
     */
    private String extractOriginalKey(String tenantKey) {
        String tenantId = TenantContextHolder.getTenantId();
        String prefix = keyPrefix + keySeparator + tenantId + keySeparator;
        if (tenantKey.startsWith(prefix)) {
            return tenantKey.substring(prefix.length());
        }
        return tenantKey;
    }
    
    /**
     * 构建租户模式
     *
     * @param pattern 原始模式
     * @return 租户模式
     */
    private String buildTenantPattern(String pattern) {
        String tenantId = TenantContextHolder.getTenantId();
        if (!StringUtils.hasText(tenantId)) {
            throw new BusinessException("租户上下文为空，无法构建租户缓存模式");
        }
        return keyPrefix + keySeparator + tenantId + keySeparator + pattern;
    }
    
    // ==================== CacheService 实现 ====================
    
    @Override
    public void set(String key, Object value) {
        delegateCache.set(buildTenantKey(key), value);
    }
    
    @Override
    public void set(String key, Object value, long timeout, TimeUnit timeUnit) {
        delegateCache.set(buildTenantKey(key), value, timeout, timeUnit);
    }
    
    @Override
    public void set(String key, Object value, Duration duration) {
        delegateCache.set(buildTenantKey(key), value, duration);
    }
    
    @Override
    public <T> T get(String key) {
        return delegateCache.get(buildTenantKey(key));
    }
    
    @Override
    public <T> T get(String key, Class<T> clazz) {
        return delegateCache.get(buildTenantKey(key), clazz);
    }
    
    @Override
    public <T> T get(String key, Function<String, T> loader) {
        return delegateCache.get(buildTenantKey(key), tenantKey -> loader.apply(extractOriginalKey(tenantKey)));
    }
    
    @Override
    public <T> T get(String key, Function<String, T> loader, long timeout, TimeUnit timeUnit) {
        return delegateCache.get(buildTenantKey(key), 
                tenantKey -> loader.apply(extractOriginalKey(tenantKey)), timeout, timeUnit);
    }
    
    @Override
    public boolean delete(String key) {
        return delegateCache.delete(buildTenantKey(key));
    }
    
    @Override
    public long delete(Collection<String> keys) {
        return delegateCache.delete(buildTenantKeys(keys));
    }
    
    @Override
    public boolean exists(String key) {
        return delegateCache.exists(buildTenantKey(key));
    }
    
    @Override
    public boolean expire(String key, long timeout, TimeUnit timeUnit) {
        return delegateCache.expire(buildTenantKey(key), timeout, timeUnit);
    }
    
    @Override
    public long getExpire(String key) {
        return delegateCache.getExpire(buildTenantKey(key));
    }
    
    @Override
    public void multiSet(Map<String, Object> map) {
        delegateCache.multiSet(buildTenantKeyMap(map));
    }
    
    @Override
    public <T> Map<String, T> multiGet(Collection<String> keys) {
        Map<String, T> tenantResult = delegateCache.multiGet(buildTenantKeys(keys));
        return tenantResult.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> extractOriginalKey(entry.getKey()),
                        Map.Entry::getValue
                ));
    }
    
    @Override
    public Set<String> keys(String pattern) {
        Set<String> tenantKeys = delegateCache.keys(buildTenantPattern(pattern));
        return tenantKeys.stream()
                .map(this::extractOriginalKey)
                .collect(Collectors.toSet());
    }
    
    @Override
    public long deleteByPattern(String pattern) {
        return delegateCache.deleteByPattern(buildTenantPattern(pattern));
    }
    
    @Override
    public long increment(String key) {
        return delegateCache.increment(buildTenantKey(key));
    }
    
    @Override
    public long increment(String key, long delta) {
        return delegateCache.increment(buildTenantKey(key), delta);
    }
    
    @Override
    public long decrement(String key) {
        return delegateCache.decrement(buildTenantKey(key));
    }
    
    @Override
    public long decrement(String key, long delta) {
        return delegateCache.decrement(buildTenantKey(key), delta);
    }
    
    @Override
    public boolean tryLock(String lockKey, long timeout, TimeUnit unit) {
        return delegateCache.tryLock(buildTenantKey(lockKey), timeout, unit);
    }
    
    @Override
    public boolean unlock(String lockKey) {
        return delegateCache.unlock(buildTenantKey(lockKey));
    }
    
    @Override
    public boolean executeWithLock(String lockKey, long timeout, TimeUnit unit, Runnable runnable) {
        return delegateCache.executeWithLock(buildTenantKey(lockKey), timeout, unit, runnable);
    }
    
    @Override
    public <T> T executeWithLock(String lockKey, long timeout, TimeUnit unit, Function<String, T> supplier) {
        return delegateCache.executeWithLock(buildTenantKey(lockKey), timeout, unit, 
                tenantKey -> supplier.apply(extractOriginalKey(tenantKey)));
    }
    
    @Override
    public CacheStats getStats() {
        return delegateCache.getStats();
    }
    
    @Override
    public void clear() {
        // 只清空当前租户的缓存
        String tenantId = TenantContextHolder.getTenantId();
        if (StringUtils.hasText(tenantId)) {
            String pattern = keyPrefix + keySeparator + tenantId + keySeparator + "*";
            long deleted = delegateCache.deleteByPattern(pattern);
            log.info("清空租户缓存: tenantId={}, deleted={}", tenantId, deleted);
        }
    }
    
    @Override
    public com.rega.erp.common.cache.core.CacheType getType() {
        return delegateCache.getType();
    }
    
    // ==================== 租户特定方法 ====================
    
    /**
     * 清空指定租户的缓存
     *
     * @param tenantId 租户ID
     * @return 删除的缓存数量
     */
    public long clearTenantCache(String tenantId) {
        if (!StringUtils.hasText(tenantId)) {
            throw new BusinessException("租户ID不能为空");
        }
        
        String pattern = keyPrefix + keySeparator + tenantId + keySeparator + "*";
        long deleted = delegateCache.deleteByPattern(pattern);
        log.info("清空指定租户缓存: tenantId={}, deleted={}", tenantId, deleted);
        return deleted;
    }
    
    /**
     * 获取指定租户的缓存键数量
     *
     * @param tenantId 租户ID
     * @return 缓存键数量
     */
    public long getTenantCacheCount(String tenantId) {
        if (!StringUtils.hasText(tenantId)) {
            throw new BusinessException("租户ID不能为空");
        }
        
        String pattern = keyPrefix + keySeparator + tenantId + keySeparator + "*";
        return delegateCache.keys(pattern).size();
    }
    
    /**
     * 获取所有租户的缓存键
     *
     * @return 租户ID集合
     */
    public Set<String> getAllTenantIds() {
        String pattern = keyPrefix + keySeparator + "*";
        return delegateCache.keys(pattern).stream()
                .map(key -> {
                    String[] parts = key.split(keySeparator);
                    return parts.length >= 2 ? parts[1] : null;
                })
                .filter(StringUtils::hasText)
                .collect(Collectors.toSet());
    }
}
