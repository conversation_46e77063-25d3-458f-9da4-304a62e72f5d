package com.rega.erp.common.cache.core;

import java.time.Duration;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 统一缓存服务接口
 * 提供多种缓存操作的统一抽象
 *
 * <AUTHOR>
 */
public interface CacheService {

    // ==================== 基本操作 ====================

    /**
     * 设置缓存
     *
     * @param key   缓存键
     * @param value 缓存值
     */
    void set(String key, Object value);

    /**
     * 设置缓存（带过期时间）
     *
     * @param key      缓存键
     * @param value    缓存值
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     */
    void set(String key, Object value, long timeout, TimeUnit timeUnit);

    /**
     * 设置缓存（带过期时间）
     *
     * @param key      缓存键
     * @param value    缓存值
     * @param duration 过期时间
     */
    void set(String key, Object value, Duration duration);

    /**
     * 获取缓存
     *
     * @param key 缓存键
     * @return 缓存值
     */
    <T> T get(String key);

    /**
     * 获取缓存（指定类型）
     *
     * @param key   缓存键
     * @param clazz 值类型
     * @return 缓存值
     */
    <T> T get(String key, Class<T> clazz);

    /**
     * 获取缓存，如果不存在则通过加载器获取并缓存
     *
     * @param key    缓存键
     * @param loader 值加载器
     * @return 缓存值
     */
    <T> T get(String key, Function<String, T> loader);

    /**
     * 获取缓存，如果不存在则通过加载器获取并缓存（带过期时间）
     *
     * @param key      缓存键
     * @param loader   值加载器
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     * @return 缓存值
     */
    <T> T get(String key, Function<String, T> loader, long timeout, TimeUnit timeUnit);

    /**
     * 删除缓存
     *
     * @param key 缓存键
     * @return 是否删除成功
     */
    boolean delete(String key);

    /**
     * 批量删除缓存
     *
     * @param keys 缓存键集合
     * @return 删除的数量
     */
    long delete(Collection<String> keys);

    /**
     * 检查缓存是否存在
     *
     * @param key 缓存键
     * @return 是否存在
     */
    boolean exists(String key);

    /**
     * 设置过期时间
     *
     * @param key      缓存键
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     * @return 是否设置成功
     */
    boolean expire(String key, long timeout, TimeUnit timeUnit);

    /**
     * 获取剩余过期时间
     *
     * @param key 缓存键
     * @return 剩余时间（秒），-1表示永不过期，-2表示不存在
     */
    long getExpire(String key);

    // ==================== 批量操作 ====================

    /**
     * 批量设置缓存
     *
     * @param map 键值对映射
     */
    void multiSet(Map<String, Object> map);

    /**
     * 批量获取缓存
     *
     * @param keys 缓存键集合
     * @return 键值对映射
     */
    <T> Map<String, T> multiGet(Collection<String> keys);

    // ==================== 模式匹配 ====================

    /**
     * 根据模式获取键集合
     *
     * @param pattern 键模式（支持通配符）
     * @return 匹配的键集合
     */
    Set<String> keys(String pattern);

    /**
     * 根据模式删除缓存
     *
     * @param pattern 键模式（支持通配符）
     * @return 删除的数量
     */
    long deleteByPattern(String pattern);

    // ==================== 原子操作 ====================

    /**
     * 原子递增
     *
     * @param key 缓存键
     * @return 递增后的值
     */
    long increment(String key);

    /**
     * 原子递增（指定步长）
     *
     * @param key   缓存键
     * @param delta 递增步长
     * @return 递增后的值
     */
    long increment(String key, long delta);

    /**
     * 原子递减
     *
     * @param key 缓存键
     * @return 递减后的值
     */
    long decrement(String key);

    /**
     * 原子递减（指定步长）
     *
     * @param key   缓存键
     * @param delta 递减步长
     * @return 递减后的值
     */
    long decrement(String key, long delta);

    // ==================== 分布式锁 ====================

    /**
     * 尝试获取分布式锁
     *
     * @param lockKey 锁键
     * @param timeout 锁超时时间
     * @param unit    时间单位
     * @return 是否获取成功
     */
    boolean tryLock(String lockKey, long timeout, TimeUnit unit);

    /**
     * 释放分布式锁
     *
     * @param lockKey 锁键
     * @return 是否释放成功
     */
    boolean unlock(String lockKey);

    /**
     * 在锁保护下执行操作
     *
     * @param lockKey  锁键
     * @param timeout  锁超时时间
     * @param unit     时间单位
     * @param runnable 执行操作
     * @return 是否执行成功
     */
    boolean executeWithLock(String lockKey, long timeout, TimeUnit unit, Runnable runnable);

    /**
     * 在锁保护下执行操作（带返回值）
     *
     * @param lockKey  锁键
     * @param timeout  锁超时时间
     * @param unit     时间单位
     * @param supplier 执行操作
     * @return 执行结果
     */
    <T> T executeWithLock(String lockKey, long timeout, TimeUnit unit, Function<String, T> supplier);

    // ==================== 缓存统计 ====================

    /**
     * 获取缓存统计信息
     *
     * @return 统计信息
     */
    CacheStats getStats();

    /**
     * 清空所有缓存
     */
    void clear();

    /**
     * 获取缓存类型
     *
     * @return 缓存类型
     */
    CacheType getType();
}
