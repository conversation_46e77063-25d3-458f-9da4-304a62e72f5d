package com.rega.erp.common.cache.config;

import com.rega.erp.common.cache.core.CacheService;
import com.rega.erp.common.cache.redis.RedisCacheService;
import com.rega.erp.common.cache.tenant.TenantCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * 缓存自动配置
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(CacheProperties.class)
@ConditionalOnProperty(prefix = "rega.cache", name = "enabled", havingValue = "true", matchIfMissing = true)
public class CacheAutoConfiguration {
    
    public CacheAutoConfiguration() {
        log.info("RegaWebERP Cache Module Auto Configuration Initialized");
    }
    
    /**
     * Redis 缓存服务
     */
    @Bean
    @ConditionalOnClass(RedisTemplate.class)
    @ConditionalOnProperty(prefix = "rega.cache.redis", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean(name = "redisCacheService")
    public CacheService redisCacheService(RedisTemplate<String, Object> redisTemplate,
                                         CacheProperties cacheProperties) {
        log.info("Register Redis Cache Service");
        return new RedisCacheService(redisTemplate, cacheProperties.getRedis().getCacheName());
    }
    
    /**
     * 租户缓存管理器
     */
    @Bean
    @Primary
    @ConditionalOnProperty(prefix = "rega.cache", name = "tenant-isolation", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean(name = "tenantCacheManager")
    public CacheService tenantCacheManager(CacheService redisCacheService,
                                          CacheProperties cacheProperties) {
        log.info("Register Tenant Cache Manager");
        return new TenantCacheManager(
                redisCacheService,
                cacheProperties.getTenantKeyPrefix(),
                cacheProperties.getKeySeparator()
        );
    }
    
    /**
     * 默认缓存服务（当未启用租户隔离时）
     */
    @Bean
    @Primary
    @ConditionalOnProperty(prefix = "rega.cache", name = "tenant-isolation", havingValue = "false")
    @ConditionalOnMissingBean(name = "defaultCacheService")
    public CacheService defaultCacheService(CacheService redisCacheService) {
        log.info("Register Default Cache Service (without tenant isolation)");
        return redisCacheService;
    }
}
