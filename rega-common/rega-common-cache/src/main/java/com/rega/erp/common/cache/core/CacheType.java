package com.rega.erp.common.cache.core;

/**
 * 缓存类型枚举
 *
 * <AUTHOR>
 */
public enum CacheType {
    
    /**
     * Redis 缓存
     */
    REDIS("Redis缓存", "基于Redis的分布式缓存"),
    
    /**
     * JetCache 多级缓存
     */
    JETCACHE("JetCache多级缓存", "基于JetCache的多级缓存"),
    
    /**
     * 本地缓存
     */
    LOCAL("本地缓存", "基于Caffeine的本地缓存"),
    
    /**
     * 混合缓存
     */
    HYBRID("混合缓存", "本地缓存+分布式缓存的混合模式");
    
    private final String name;
    private final String description;
    
    CacheType(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
}
