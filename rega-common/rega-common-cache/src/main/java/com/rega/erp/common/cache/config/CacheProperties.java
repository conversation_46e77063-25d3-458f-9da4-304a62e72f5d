package com.rega.erp.common.cache.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;

/**
 * 缓存配置属性
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "rega.cache")
public class CacheProperties {
    
    /**
     * 是否启用缓存
     */
    private boolean enabled = true;
    
    /**
     * 默认缓存类型
     */
    private String type = "redis";
    
    /**
     * 是否启用租户隔离
     */
    private boolean tenantIsolation = true;
    
    /**
     * 租户缓存键前缀
     */
    private String tenantKeyPrefix = "tenant";
    
    /**
     * 键分隔符
     */
    private String keySeparator = ":";
    
    /**
     * Redis 配置
     */
    private Redis redis = new Redis();
    
    /**
     * JetCache 配置
     */
    private JetCache jetcache = new JetCache();
    
    /**
     * 本地缓存配置
     */
    private Local local = new Local();
    
    /**
     * 统计配置
     */
    private Stats stats = new Stats();
    
    /**
     * Redis 配置
     */
    @Data
    public static class Redis {
        /**
         * 是否启用 Redis 缓存
         */
        private boolean enabled = true;
        
        /**
         * 缓存名称
         */
        private String cacheName = "rega-cache";
        
        /**
         * 默认过期时间
         */
        private Duration defaultExpiration = Duration.ofHours(1);
        
        /**
         * 键前缀
         */
        private String keyPrefix = "rega";
        
        /**
         * 是否启用键前缀
         */
        private boolean useKeyPrefix = true;
        
        /**
         * 序列化类型
         */
        private String serializer = "jackson";
        
        /**
         * 是否缓存空值
         */
        private boolean cacheNullValues = false;
        
        /**
         * 空值过期时间
         */
        private Duration nullValueExpiration = Duration.ofMinutes(5);
    }
    
    /**
     * JetCache 配置
     */
    @Data
    public static class JetCache {
        /**
         * 是否启用 JetCache
         */
        private boolean enabled = false;
        
        /**
         * 本地缓存类型
         */
        private String localType = "caffeine";
        
        /**
         * 远程缓存类型
         */
        private String remoteType = "redis";
        
        /**
         * 本地缓存配置
         */
        private LocalCache localCache = new LocalCache();
        
        /**
         * 远程缓存配置
         */
        private RemoteCache remoteCache = new RemoteCache();
        
        /**
         * 本地缓存配置
         */
        @Data
        public static class LocalCache {
            /**
             * 最大缓存数量
             */
            private int maxSize = 1000;
            
            /**
             * 过期时间
             */
            private Duration expiration = Duration.ofMinutes(30);
            
            /**
             * 写入后过期时间
             */
            private Duration expireAfterWrite = Duration.ofMinutes(30);
            
            /**
             * 访问后过期时间
             */
            private Duration expireAfterAccess = Duration.ofMinutes(10);
        }
        
        /**
         * 远程缓存配置
         */
        @Data
        public static class RemoteCache {
            /**
             * 过期时间
             */
            private Duration expiration = Duration.ofHours(2);
            
            /**
             * 键前缀
             */
            private String keyPrefix = "jetcache";
            
            /**
             * 序列化类型
             */
            private String serializer = "jackson";
        }
    }
    
    /**
     * 本地缓存配置
     */
    @Data
    public static class Local {
        /**
         * 是否启用本地缓存
         */
        private boolean enabled = true;
        
        /**
         * 缓存类型
         */
        private String type = "caffeine";
        
        /**
         * 最大缓存数量
         */
        private int maxSize = 10000;
        
        /**
         * 初始容量
         */
        private int initialCapacity = 100;
        
        /**
         * 过期时间
         */
        private Duration expiration = Duration.ofMinutes(30);
        
        /**
         * 写入后过期时间
         */
        private Duration expireAfterWrite = Duration.ofMinutes(30);
        
        /**
         * 访问后过期时间
         */
        private Duration expireAfterAccess = Duration.ofMinutes(10);
        
        /**
         * 刷新时间
         */
        private Duration refreshAfterWrite = Duration.ofMinutes(15);
        
        /**
         * 是否启用软引用
         */
        private boolean softValues = false;
        
        /**
         * 是否启用弱引用
         */
        private boolean weakKeys = false;
        
        /**
         * 是否启用统计
         */
        private boolean recordStats = true;
    }
    
    /**
     * 统计配置
     */
    @Data
    public static class Stats {
        /**
         * 是否启用统计
         */
        private boolean enabled = true;
        
        /**
         * 统计收集间隔
         */
        private Duration collectInterval = Duration.ofMinutes(1);
        
        /**
         * 统计数据保留时间
         */
        private Duration retentionTime = Duration.ofHours(24);
        
        /**
         * 是否启用 JMX 监控
         */
        private boolean jmxEnabled = false;
        
        /**
         * 是否启用 Micrometer 指标
         */
        private boolean micrometerEnabled = true;
    }
    
    /**
     * 分布式锁配置
     */
    @Data
    public static class Lock {
        /**
         * 是否启用分布式锁
         */
        private boolean enabled = true;
        
        /**
         * 默认锁超时时间
         */
        private Duration defaultTimeout = Duration.ofSeconds(30);
        
        /**
         * 锁重试间隔
         */
        private Duration retryInterval = Duration.ofMillis(100);
        
        /**
         * 最大重试次数
         */
        private int maxRetries = 3;
        
        /**
         * 锁键前缀
         */
        private String keyPrefix = "lock";
    }
}
