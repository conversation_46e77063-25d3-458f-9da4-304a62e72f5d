# RegaWebERP 核心模块

## 概述

rega-common-core 是 RegaWebERP 系统的核心基础模块，提供了系统运行所需的基础工具类、实体基类、配置管理、多租户支持等核心功能。

## 功能特性

### 🛠️ 核心工具类

- **日期时间工具** (`DateUtils`) - 提供日期时间处理、格式化、转换等功能
- **文件工具** (`FileUtils`) - 文件操作、上传下载、类型检测等功能
- **加密工具** (`CryptoUtils`) - MD5、SHA、AES、Base64等加密解密功能
- **JSON工具** (`JsonUtils`) - JSON序列化反序列化、对象转换等功能
- **验证工具** (`ValidatorUtils`) - 数据格式验证、正则表达式验证等功能
- **ID生成工具** (`IdUtils`) - UUID、雪花ID、订单号等ID生成功能（待CosID集成完成后添加）

### 🏗️ 基础实体类

- **BaseEntity** - 实体基类，包含通用字段（创建时间、更新时间等）
- **TreeEntity** - 树形结构实体基类，支持层级数据
- **PageDomain** - 分页查询基类
- **TableSupport** - 表格数据处理支持

### 🏢 多租户支持

- **TenantContextHolder** - 租户上下文管理
- **TenantUtils** - 租户工具类，提供租户相关操作

### ✅ 验证注解

- **@Mobile** - 手机号验证注解
- **@IdCard** - 身份证号验证注解
- 支持自定义验证器扩展

### ⚙️ 配置管理

- **RegaProperties** - 系统基础配置属性
- **SystemConfig** - 系统详细配置管理
- 支持配置热更新和环境隔离

## 快速开始

### 1. 添加依赖

在你的模块的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>com.rega.erp</groupId>
    <artifactId>rega-common-core</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 基本使用

#### 日期时间工具

```java
// 获取当前时间
String now = DateUtils.getTime();

// 日期格式化
String formatted = DateUtils.formatLocalDateTime(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss");

// 日期转换
Date date = DateUtils.toDate(LocalDateTime.now());
LocalDateTime localDateTime = DateUtils.toLocalDateTime(new Date());

// 计算日期差
long days = DateUtils.daysBetween(startDate, endDate);
```

#### JSON工具

```java
// 对象转JSON
String json = JsonUtils.toJsonString(user);

// JSON转对象
User user = JsonUtils.parseObject(json, User.class);

// JSON转List
List<User> users = JsonUtils.parseList(json, User.class);

// JSON转Map
Map<String, Object> map = JsonUtils.parseMap(json);
```

#### 加密工具

```java
// MD5加密
String md5 = CryptoUtils.md5("password");

// SHA256加密
String sha256 = CryptoUtils.sha256("data");

// Base64编码
String encoded = CryptoUtils.base64Encode("data");

// AES加密
String key = CryptoUtils.generateAESKey();
String encrypted = CryptoUtils.aesEncrypt("data", key);
String decrypted = CryptoUtils.aesDecrypt(encrypted, key);
```

#### ID生成工具（待实现）

```java
// 注意：IdUtils 需要在 CosID 依赖注入配置完成后才能使用
// 以下是预期的API设计：

// UUID生成
String uuid = IdUtils.randomUUID();
String simpleUuid = IdUtils.simpleUUID();

// 雪花ID生成（需要CosID配置）
long snowflakeId = IdUtils.snowflakeId();
String snowflakeIdStr = IdUtils.snowflakeIdString();

// 订单号生成
String orderNo = IdUtils.generateOrderNo();
String orderNoWithPrefix = IdUtils.generateOrderNo("ORD");

// 验证码生成
String verifyCode = IdUtils.generateVerifyCode(6);
```

#### 验证工具

```java
// 手机号验证
boolean isMobile = ValidatorUtils.isMobile("13800138000");

// 邮箱验证
boolean isEmail = ValidatorUtils.isEmail("<EMAIL>");

// 身份证验证
boolean isIdCard = ValidatorUtils.isIDCard("110101199001011234");

// 数字验证
boolean isNumber = ValidatorUtils.isNumber("123.45");
```

#### 文件工具

```java
// 文件名验证
boolean isValid = FileUtils.isValidFilename("document.pdf");

// 获取文件扩展名
String extension = FileUtils.getExtension("document.pdf");

// 格式化文件大小
String size = FileUtils.formatFileSize(1024000);

// 文件操作
boolean exists = FileUtils.exists("/path/to/file");
String content = FileUtils.readFileToString("/path/to/file");
```

### 3. 实体基类使用

```java
@Entity
@Table(name = "sys_user")
public class User extends BaseEntity {
    private Long id;
    private String username;
    private String email;
    // ... 其他字段
}

// 树形实体
@Entity
@Table(name = "sys_dept")
public class Department extends TreeEntity<Department> {
    private Long id;
    private String name;
    // ... 其他字段
}
```

### 4. 多租户使用

```java
// 设置租户ID
TenantUtils.setTenantId("123456");

// 获取当前租户ID
String tenantId = TenantUtils.getCurrentTenantId();

// 在指定租户上下文中执行操作
TenantUtils.runWithTenant("123456", () -> {
    // 业务逻辑
});

// 生成带租户前缀的键
String tenantKey = TenantUtils.generateTenantKey("cache_key");
```

### 5. 验证注解使用

```java
public class UserDTO {
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @Mobile(message = "手机号格式不正确")
    private String mobile;
    
    @IdCard(message = "身份证号格式不正确")
    private String idCard;
    
    @Email(message = "邮箱格式不正确")
    private String email;
}
```

### 6. 配置使用

```java
@Autowired
private RegaProperties regaProperties;

@Autowired
private SystemConfig systemConfig;

// 获取配置值
String appName = regaProperties.getName();
Integer pageSize = systemConfig.getPageSize();
```

## 工具类详细说明

### DateUtils - 日期时间工具

| 方法 | 说明 |
|------|------|
| `getNow()` | 获取当前Date |
| `getDate()` | 获取当前日期字符串 |
| `getTime()` | 获取当前时间字符串 |
| `parseDate(String)` | 解析日期字符串 |
| `toLocalDateTime(Date)` | Date转LocalDateTime |
| `toDate(LocalDateTime)` | LocalDateTime转Date |
| `formatLocalDateTime()` | 格式化LocalDateTime |
| `daysBetween()` | 计算日期间隔天数 |

### JsonUtils - JSON工具

| 方法 | 说明 |
|------|------|
| `toJsonString(Object)` | 对象转JSON字符串 |
| `parseObject(String, Class)` | JSON转对象 |
| `parseList(String, Class)` | JSON转List |
| `parseMap(String)` | JSON转Map |
| `isValidJson(String)` | 验证JSON格式 |
| `convertValue()` | 对象转换 |

### CryptoUtils - 加密工具

| 方法 | 说明 |
|------|------|
| `md5(String)` | MD5加密 |
| `sha256(String)` | SHA256加密 |
| `base64Encode(String)` | Base64编码 |
| `base64Decode(String)` | Base64解码 |
| `aesEncrypt()` | AES加密 |
| `aesDecrypt()` | AES解密 |
| `generateSalt()` | 生成盐值 |

### IdUtils - ID生成工具（待实现）

| 方法 | 说明 |
|------|------|
| `randomUUID()` | 生成UUID |
| `simpleUUID()` | 生成简化UUID |
| `snowflakeId()` | 生成雪花ID（需要CosID配置） |
| `generateOrderNo()` | 生成订单号 |
| `generateVerifyCode()` | 生成验证码 |
| `randomNumeric(int)` | 生成随机数字 |

> **注意**: IdUtils 类需要在 CosID 依赖注入和配置完成后才能实现。

### ValidatorUtils - 验证工具

| 方法 | 说明 |
|------|------|
| `isMobile(String)` | 验证手机号 |
| `isEmail(String)` | 验证邮箱 |
| `isIDCard(String)` | 验证身份证 |
| `isNumber(String)` | 验证数字 |
| `isAlpha(String)` | 验证字母 |
| `isValidDate(String)` | 验证日期格式 |

## 配置说明

### application.yml 配置示例

```yaml
rega:
  name: RegaWebERP
  version: 1.0.0
  demo-enabled: false
  profile: /data/rega
  
  system:
    name: RegaWebERP
    page-size: 20
    session-timeout: 30
    captcha-enabled: true
    multi-tenant-enabled: true
    i18n-enabled: true
```

## 扩展指南

### 1. 添加新的工具类

```java
@Component
public class CustomUtils {
    // 自定义工具方法
}
```

### 2. 自定义验证注解

```java
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CustomValidator.class)
public @interface Custom {
    String message() default "自定义验证失败";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
```

### 3. 扩展配置属性

```java
@ConfigurationProperties(prefix = "rega.custom")
public class CustomProperties {
    // 自定义配置属性
}
```

## 注意事项

1. **线程安全**: 所有工具类都是线程安全的
2. **性能考虑**: 大量数据处理时注意内存使用
3. **异常处理**: 工具类方法会返回null或抛出运行时异常
4. **多租户**: 使用多租户功能时注意上下文清理
5. **配置更新**: 配置变更后需要重启应用生效

## 依赖说明

- Spring Boot 3.1.0
- Jackson 2.15.0
- Apache Commons Lang3
- Apache Commons IO
- Apache Commons Codec
- CosID (分布式ID生成，待配置)
- 标准 ThreadLocal (线程本地变量)
