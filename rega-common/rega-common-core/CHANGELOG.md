# rega-common-core 更新日志

## [1.0.0] - 2024-01-XX

### 新增功能

#### 🛠️ 核心工具类
- **DateUtils** - 完整的日期时间处理工具
  - 支持 Date 和 LocalDateTime 互转
  - 提供多种日期格式化方法
  - 支持日期计算和比较
  - 兼容多种日期格式解析

- **JsonUtils** - 强大的JSON处理工具
  - 基于Jackson的高性能JSON序列化
  - 支持复杂对象和泛型转换
  - 提供JsonNode操作方法
  - 内置JSON格式验证

- **CryptoUtils** - 全面的加密解密工具
  - 支持MD5、SHA1、SHA256、SHA512哈希
  - 提供AES对称加密
  - Base64编码解码
  - 随机盐值生成
  - HMAC-SHA256签名

- **IdUtils** - 多样化的ID生成工具（待实现）
  - UUID生成（标准和简化版本）
  - 雪花ID生成（基于CosID，需要依赖注入配置）
  - 订单号、流水号生成
  - 验证码、邀请码生成
  - 文件名生成
  - **注意**: 该工具类需要在CosID依赖注入和配置完成后才能实现

- **ValidatorUtils** - 数据验证工具
  - 手机号、邮箱、身份证验证
  - 数字、字母、日期格式验证
  - 正则表达式验证
  - 长度范围验证

- **FileUtils** - 文件操作工具
  - 文件上传下载处理
  - 文件类型检测
  - 文件大小格式化
  - 文件名安全验证
  - 文件编码处理

- **ServletUtils** - Web请求处理工具
  - 请求参数获取和转换
  - 客户端IP获取
  - Ajax请求判断
  - URL编码解码
  - 响应渲染

- **StringUtils** - 字符串处理工具
  - 扩展Apache Commons Lang
  - 驼峰和下划线转换
  - 空值处理
  - 字符串格式化
  - 类型转换

#### 🏗️ 基础实体类
- **BaseEntity** - 实体基类
  - 包含创建时间、更新时间等审计字段
  - 支持搜索值和请求参数
  - 提供JSON序列化配置

- **TreeEntity** - 树形结构实体
  - 支持父子关系
  - 提供层级数据处理
  - 支持祖先路径

- **PageDomain** - 分页查询基类
  - 支持分页参数
  - 提供排序功能
  - 兼容前端分页组件

- **TableSupport** - 表格数据支持
  - 自动解析分页参数
  - 支持多种排序方式
  - 提供参数验证

#### 🏢 多租户支持
- **TenantContextHolder** - 租户上下文管理
  - 基于TransmittableThreadLocal
  - 支持异步线程传递
  - 提供租户信息存储

- **TenantUtils** - 租户工具类
  - 租户ID格式化和验证
  - 租户上下文操作
  - 带租户前缀的键生成
  - 租户隔离执行

#### ✅ 验证注解
- **@Mobile** - 手机号验证注解
  - 支持中国大陆手机号格式
  - 可配置错误消息
  - 支持空值跳过

- **@IdCard** - 身份证号验证注解
  - 支持15位和18位身份证
  - 可配置错误消息
  - 支持空值跳过

#### ⚙️ 配置管理
- **RegaProperties** - 系统基础配置
  - 支持Spring Boot配置绑定
  - 提供默认值
  - 支持环境变量覆盖

- **SystemConfig** - 系统详细配置
  - 完整的系统参数配置
  - 支持功能开关
  - 提供配置验证

#### 🧪 测试支持
- **DateUtilsTest** - 日期工具测试
  - 覆盖所有日期操作方法
  - 包含边界条件测试
  - 验证格式转换正确性

- **JsonUtilsTest** - JSON工具测试
  - 测试复杂对象序列化
  - 验证泛型转换
  - 包含异常情况处理

- **IdUtilsTest** - ID生成工具测试（待实现）
  - 验证ID唯一性
  - 测试格式正确性
  - 包含性能测试
  - **注意**: 需要在CosID配置完成后实现

### 技术特性

#### 🚀 性能优化
- 使用ThreadLocalRandom提升随机数生成性能
- 基于Jackson的高性能JSON处理
- 优化的字符串操作算法
- 缓存常用配置减少重复计算

#### 🔒 安全增强
- 文件名安全验证防止路径遍历
- 客户端IP获取防止伪造
- 加密算法使用安全随机数
- 输入参数严格验证

#### 🌐 国际化支持
- 验证错误消息支持国际化
- 日期格式支持多地区
- 配置项支持多语言
- 异常信息本地化

#### 📱 多租户架构
- 完整的租户上下文管理
- 支持租户数据隔离
- 提供租户切换功能
- 兼容微服务架构

### 依赖更新

#### 新增依赖
- `me.ahoo.cosid:cosid-spring-boot-starter:2.5.5` - 分布式ID生成（待配置）
- `org.apache.commons:commons-lang3` - 字符串工具增强
- `commons-io:commons-io` - 文件操作工具
- `commons-codec:commons-codec` - 编码解码工具
- `com.fasterxml.jackson.datatype:jackson-datatype-jsr310` - Java 8时间支持

#### 移除依赖
- `com.alibaba:transmittable-thread-local` - 改用标准ThreadLocal实现

#### 版本升级
- Spring Boot 3.1.0
- Jackson 2.15.0
- Hutool 5.8.18

### 配置示例

```yaml
rega:
  name: RegaWebERP
  version: 1.0.0
  demo-enabled: false
  profile: /data/rega
  
  system:
    name: RegaWebERP
    page-size: 20
    max-page-size: 500
    session-timeout: 30
    password-min-length: 6
    password-max-length: 20
    captcha-enabled: true
    captcha-type: math
    multi-tenant-enabled: true
    i18n-enabled: true
    cache-enabled: true
    audit-log-enabled: true
```

### 使用示例

```java
// 日期时间处理
String now = DateUtils.getTime();
LocalDateTime dateTime = DateUtils.parseLocalDateTime("2024-01-01 10:30:00", "yyyy-MM-dd HH:mm:ss");

// JSON处理
String json = JsonUtils.toJsonString(user);
User user = JsonUtils.parseObject(json, User.class);

// ID生成
String orderId = IdUtils.generateOrderNo("ORD");
long snowflakeId = IdUtils.snowflakeId();

// 加密处理
String encrypted = CryptoUtils.aesEncrypt("data", key);
String hash = CryptoUtils.sha256("password");

// 多租户
TenantUtils.runWithTenant("123456", () -> {
    // 业务逻辑
});
```

### 注意事项

1. **线程安全**: 所有工具类都是线程安全的
2. **性能考虑**: 大量数据处理时注意内存使用
3. **异常处理**: 工具类方法会返回null或抛出运行时异常
4. **多租户**: 使用多租户功能时注意上下文清理
5. **配置更新**: 配置变更后需要重启应用生效

### 后续计划

- [ ] 添加更多加密算法支持
- [ ] 扩展文件处理功能
- [ ] 增加缓存工具类
- [ ] 完善监控指标
- [ ] 添加性能测试用例
