package com.rega.erp.common.core.exception;

import com.rega.erp.common.core.constant.HttpStatus;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
public class BusinessException extends BaseException {
    
    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(HttpStatus.ERROR, message);
    }

    /**
     * 构造函数
     *
     * @param code    错误码
     * @param message 错误消息
     */
    public BusinessException(Integer code, String message) {
        super(code, message);
    }

    /**
     * 构造函数
     *
     * @param code    错误码
     * @param message 错误消息
     * @param cause   异常
     */
    public BusinessException(Integer code, String message, Throwable cause) {
        super(code, message, cause);
    }
    
    /**
     * 构造函数 - 支持int类型错误码
     *
     * @param code    错误码
     * @param message 错误消息
     */
    public BusinessException(int code, String message) {
        super(Integer.valueOf(code), message);
    }

    /**
     * 构造函数 - 支持int类型错误码
     *
     * @param code    错误码
     * @param message 错误消息
     * @param cause   异常
     */
    public BusinessException(int code, String message, Throwable cause) {
        super(Integer.valueOf(code), message, cause);
    }
}
