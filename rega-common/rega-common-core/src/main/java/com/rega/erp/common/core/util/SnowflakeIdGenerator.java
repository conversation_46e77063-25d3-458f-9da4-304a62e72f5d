package com.rega.erp.common.core.util;

import java.lang.management.ManagementFactory;
import java.net.InetAddress;
import java.net.NetworkInterface;

/**
 * 雪花ID生成器（简化版本）
 * 基于Twitter的Snowflake算法
 *
 * <AUTHOR>
 */
public class SnowflakeIdGenerator {

    /**
     * 开始时间戳 (2023-01-01 00:00:00)
     */
    private static final long START_TIMESTAMP = 1672531200000L;

    /**
     * 序列号占用的位数
     */
    private static final long SEQUENCE_BIT = 12;

    /**
     * 机器标识占用的位数
     */
    private static final long MACHINE_BIT = 5;

    /**
     * 数据中心占用的位数
     */
    private static final long DATACENTER_BIT = 5;

    /**
     * 每一部分的最大值
     */
    private static final long MAX_DATACENTER_NUM = ~(-1L << DATACENTER_BIT);
    private static final long MAX_MACHINE_NUM = ~(-1L << MACHINE_BIT);
    private static final long MAX_SEQUENCE = ~(-1L << SEQUENCE_BIT);

    /**
     * 每一部分向左的位移
     */
    private static final long MACHINE_LEFT = SEQUENCE_BIT;
    private static final long DATACENTER_LEFT = SEQUENCE_BIT + MACHINE_BIT;
    private static final long TIMESTAMP_LEFT = DATACENTER_LEFT + DATACENTER_BIT;

    /**
     * 数据中心ID
     */
    private final long datacenterId;

    /**
     * 机器ID
     */
    private final long machineId;

    /**
     * 序列号
     */
    private long sequence = 0L;

    /**
     * 上一次时间戳
     */
    private long lastTimestamp = -1L;

    /**
     * 单例实例
     */
    private static volatile SnowflakeIdGenerator instance;

    /**
     * 私有构造函数
     */
    private SnowflakeIdGenerator(long datacenterId, long machineId) {
        if (datacenterId > MAX_DATACENTER_NUM || datacenterId < 0) {
            throw new IllegalArgumentException("datacenterId can't be greater than MAX_DATACENTER_NUM or less than 0");
        }
        if (machineId > MAX_MACHINE_NUM || machineId < 0) {
            throw new IllegalArgumentException("machineId can't be greater than MAX_MACHINE_NUM or less than 0");
        }
        this.datacenterId = datacenterId;
        this.machineId = machineId;
    }

    /**
     * 获取单例实例
     */
    public static SnowflakeIdGenerator getInstance() {
        if (instance == null) {
            synchronized (SnowflakeIdGenerator.class) {
                if (instance == null) {
                    // 自动获取数据中心ID和机器ID
                    long datacenterId = getDatacenterId();
                    long machineId = getMachineId();
                    instance = new SnowflakeIdGenerator(datacenterId, machineId);
                }
            }
        }
        return instance;
    }

    /**
     * 产生下一个ID
     */
    public synchronized long nextId() {
        long currTimestamp = getNewTimestamp();
        if (currTimestamp < lastTimestamp) {
            throw new RuntimeException("Clock moved backwards. Refusing to generate id");
        }

        if (currTimestamp == lastTimestamp) {
            // 相同毫秒内，序列号自增
            sequence = (sequence + 1) & MAX_SEQUENCE;
            // 同一毫秒的序列数已经达到最大
            if (sequence == 0L) {
                currTimestamp = getNextMill();
            }
        } else {
            // 不同毫秒内，序列号置为0
            sequence = 0L;
        }

        lastTimestamp = currTimestamp;

        return (currTimestamp - START_TIMESTAMP) << TIMESTAMP_LEFT // 时间戳部分
                | datacenterId << DATACENTER_LEFT                 // 数据中心部分
                | machineId << MACHINE_LEFT                       // 机器标识部分
                | sequence;                                       // 序列号部分
    }

    /**
     * 获取下一个毫秒
     */
    private long getNextMill() {
        long mill = getNewTimestamp();
        while (mill <= lastTimestamp) {
            mill = getNewTimestamp();
        }
        return mill;
    }

    /**
     * 获取新的时间戳
     */
    private long getNewTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 获取数据中心ID
     */
    private static long getDatacenterId() {
        try {
            InetAddress ip = InetAddress.getLocalHost();
            NetworkInterface network = NetworkInterface.getByInetAddress(ip);
            if (network == null) {
                return 1L;
            }
            byte[] mac = network.getHardwareAddress();
            if (mac != null) {
                long id = ((0x000000FF & (long) mac[mac.length - 2])
                        | (0x0000FF00 & (((long) mac[mac.length - 1]) << 8))) >> 6;
                return id % (MAX_DATACENTER_NUM + 1);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return 1L;
    }

    /**
     * 获取机器ID
     */
    private static long getMachineId() {
        try {
            // 使用进程ID作为机器ID的一部分
            String name = ManagementFactory.getRuntimeMXBean().getName();
            String pid = name.split("@")[0];
            return Long.parseLong(pid) % (MAX_MACHINE_NUM + 1);
        } catch (Exception e) {
            // 如果获取失败，使用随机数
            return (long) (Math.random() * MAX_MACHINE_NUM);
        }
    }

    /**
     * 获取当前配置信息
     */
    public String getConfig() {
        return String.format("SnowflakeIdGenerator{datacenterId=%d, machineId=%d, sequence=%d, lastTimestamp=%d}",
                datacenterId, machineId, sequence, lastTimestamp);
    }
}
