package com.rega.erp.common.core.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 多租户基础实体类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TenantBaseEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 是否启用
     * 
     * @return true-启用，false-禁用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 设置为启用
     */
    public void enable() {
        this.status = 1;
    }

    /**
     * 设置为禁用
     */
    public void disable() {
        this.status = 0;
    }

    /**
     * 预插入处理
     */
    @Override
    public void preInsert() {
        super.preInsert();
        if (this.status == null) {
            this.status = 1;
        }
        if (this.sortOrder == null) {
            this.sortOrder = 0;
        }
    }
}
