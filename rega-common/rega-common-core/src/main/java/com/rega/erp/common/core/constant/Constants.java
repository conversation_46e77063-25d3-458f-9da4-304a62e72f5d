package com.rega.erp.common.core.constant;

/**
 * 系统常量
 *
 * <AUTHOR>
 */
public interface Constants {

    /**
     * UTF-8 字符集
     */
    String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    String GBK = "GBK";

    /**
     * http请求
     */
    String HTTP = "http://";

    /**
     * https请求
     */
    String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    String FAIL = "1";

    /**
     * 登录成功
     */
    String LOGIN_SUCCESS = "Success";

    /**
     * 登录失败
     */
    String LOGIN_FAIL = "Error";

    /**
     * 注销
     */
    String LOGOUT = "Logout";

    /**
     * 注册
     */
    String REGISTER = "Register";

    /**
     * 验证码有效期（分钟）
     */
    Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    String TOKEN = "token";

    /**
     * 令牌前缀
     */
    String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    String LOGIN_USER_KEY = "login_user";

    /**
     * 用户ID
     */
    String JWT_USER_ID = "userId";

    /**
     * 用户名称
     */
    String JWT_USERNAME = "username";

    /**
     * 用户头像
     */
    String JWT_AVATAR = "avatar";

    /**
     * 租户ID
     */
    String JWT_TENANT_ID = "tenantId";

    /**
     * 创建时间
     */
    String JWT_CREATED = "created";

    /**
     * 资源映射路径前缀
     */
    String RESOURCE_PREFIX = "/file";
} 