package com.rega.erp.common.core.util;

import java.util.regex.Pattern;

/**
 * 验证工具类
 *
 * <AUTHOR>
 */
public class ValidatorUtils {

    /**
     * 正则表达式：验证用户名
     */
    public static final String REGEX_USERNAME = "^[a-zA-Z]\\w{5,20}$";

    /**
     * 正则表达式：验证密码
     */
    public static final String REGEX_PASSWORD = "^[a-zA-Z0-9]{6,20}$";

    /**
     * 正则表达式：验证手机号
     */
    public static final String REGEX_MOBILE = "^((17[0-9])|(14[0-9])|(13[0-9])|(15[^4,\\D])|(18[0,5-9]))\\d{8}$";

    /**
     * 正则表达式：验证邮箱
     */
    public static final String REGEX_EMAIL = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";

    /**
     * 正则表达式：验证汉字
     */
    public static final String REGEX_CHINESE = "^[\u4e00-\u9fa5],{0,}$";

    /**
     * 正则表达式：验证身份证
     */
    public static final String REGEX_ID_CARD = "(^\\d{18}$)|(^\\d{15}$)";

    /**
     * 正则表达式：验证URL
     */
    public static final String REGEX_URL = "http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?";

    /**
     * 正则表达式：验证IP地址
     */
    public static final String REGEX_IP_ADDR = "(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)";

    /**
     * 校验用户名
     *
     * @param username 用户名
     * @return 校验结果
     */
    public static boolean isUsername(String username) {
        return Pattern.matches(REGEX_USERNAME, username);
    }

    /**
     * 校验密码
     *
     * @param password 密码
     * @return 校验结果
     */
    public static boolean isPassword(String password) {
        return Pattern.matches(REGEX_PASSWORD, password);
    }

    /**
     * 校验手机号
     *
     * @param mobile 手机号
     * @return 校验结果
     */
    public static boolean isMobile(String mobile) {
        return Pattern.matches(REGEX_MOBILE, mobile);
    }

    /**
     * 校验邮箱
     *
     * @param email 邮箱
     * @return 校验结果
     */
    public static boolean isEmail(String email) {
        return Pattern.matches(REGEX_EMAIL, email);
    }

    /**
     * 校验汉字
     *
     * @param chinese 汉字
     * @return 校验结果
     */
    public static boolean isChinese(String chinese) {
        return Pattern.matches(REGEX_CHINESE, chinese);
    }

    /**
     * 校验身份证
     *
     * @param idCard 身份证
     * @return 校验结果
     */
    public static boolean isIDCard(String idCard) {
        return Pattern.matches(REGEX_ID_CARD, idCard);
    }

    /**
     * 校验URL
     *
     * @param url URL
     * @return 校验结果
     */
    public static boolean isUrl(String url) {
        return Pattern.matches(REGEX_URL, url);
    }

    /**
     * 校验IP地址
     *
     * @param ipAddr IP地址
     * @return 校验结果
     */
    public static boolean isIPAddr(String ipAddr) {
        return Pattern.matches(REGEX_IP_ADDR, ipAddr);
    }

    /**
     * 验证是否为数字
     *
     * @param str 字符串
     * @return 是否为数字
     */
    public static boolean isNumeric(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("\\d+");
    }

    /**
     * 验证是否为整数
     *
     * @param str 字符串
     * @return 是否为整数
     */
    public static boolean isInteger(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("^-?\\d+$");
    }

    /**
     * 验证是否为小数
     *
     * @param str 字符串
     * @return 是否为小数
     */
    public static boolean isDecimal(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("^-?\\d+\\.\\d+$");
    }

    /**
     * 验证是否为数字（包括整数和小数）
     *
     * @param str 字符串
     * @return 是否为数字
     */
    public static boolean isNumber(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("^-?\\d+(\\.\\d+)?$");
    }

    /**
     * 验证是否为正整数
     *
     * @param str 字符串
     * @return 是否为正整数
     */
    public static boolean isPositiveInteger(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("^[1-9]\\d*$");
    }

    /**
     * 验证是否为负整数
     *
     * @param str 字符串
     * @return 是否为负整数
     */
    public static boolean isNegativeInteger(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("^-[1-9]\\d*$");
    }

    /**
     * 验证是否为非负整数（正整数 + 0）
     *
     * @param str 字符串
     * @return 是否为非负整数
     */
    public static boolean isNonNegativeInteger(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("^\\d+$");
    }

    /**
     * 验证是否为非正整数（负整数 + 0）
     *
     * @param str 字符串
     * @return 是否为非正整数
     */
    public static boolean isNonPositiveInteger(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("^((-\\d+)|(0+))$");
    }

    /**
     * 验证字符串长度是否在指定范围内
     *
     * @param str 字符串
     * @param min 最小长度
     * @param max 最大长度
     * @return 是否在范围内
     */
    public static boolean isLengthBetween(String str, int min, int max) {
        if (str == null) {
            return false;
        }
        int length = str.length();
        return length >= min && length <= max;
    }

    /**
     * 验证字符串是否只包含字母
     *
     * @param str 字符串
     * @return 是否只包含字母
     */
    public static boolean isAlpha(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("^[a-zA-Z]+$");
    }

    /**
     * 验证字符串是否只包含字母和数字
     *
     * @param str 字符串
     * @return 是否只包含字母和数字
     */
    public static boolean isAlphanumeric(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("^[a-zA-Z0-9]+$");
    }

    /**
     * 验证是否为有效的日期格式（yyyy-MM-dd）
     *
     * @param dateStr 日期字符串
     * @return 是否为有效日期格式
     */
    public static boolean isValidDate(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return false;
        }
        return dateStr.matches("^\\d{4}-\\d{2}-\\d{2}$");
    }

    /**
     * 验证是否为有效的时间格式（HH:mm:ss）
     *
     * @param timeStr 时间字符串
     * @return 是否为有效时间格式
     */
    public static boolean isValidTime(String timeStr) {
        if (StringUtils.isEmpty(timeStr)) {
            return false;
        }
        return timeStr.matches("^\\d{2}:\\d{2}:\\d{2}$");
    }

    /**
     * 验证是否为有效的日期时间格式（yyyy-MM-dd HH:mm:ss）
     *
     * @param dateTimeStr 日期时间字符串
     * @return 是否为有效日期时间格式
     */
    public static boolean isValidDateTime(String dateTimeStr) {
        if (StringUtils.isEmpty(dateTimeStr)) {
            return false;
        }
        return dateTimeStr.matches("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$");
    }
}
