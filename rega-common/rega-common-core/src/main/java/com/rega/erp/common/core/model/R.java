package com.rega.erp.common.core.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 统一响应结构
 *
 * @param <T> 数据类型
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class R<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private int code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 时间戳
     */
    private long timestamp = System.currentTimeMillis();

    /**
     * 成功
     *
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> R<T> ok() {
        return new R<T>().setCode(200).setMessage("操作成功");
    }

    /**
     * 成功
     *
     * @param data 数据
     * @param <T>  数据类型
     * @return 响应对象
     */
    public static <T> R<T> ok(T data) {
        return new R<T>().setCode(200).setMessage("操作成功").setData(data);
    }

    /**
     * 成功
     *
     * @param message 消息
     * @param data    数据
     * @param <T>     数据类型
     * @return 响应对象
     */
    public static <T> R<T> ok(String message, T data) {
        return new R<T>().setCode(200).setMessage(message).setData(data);
    }

    /**
     * 失败
     *
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> R<T> fail() {
        return new R<T>().setCode(500).setMessage("操作失败");
    }

    /**
     * 失败
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return 响应对象
     */
    public static <T> R<T> fail(String message) {
        return new R<T>().setCode(500).setMessage(message);
    }

    /**
     * 失败
     *
     * @param code    状态码
     * @param message 消息
     * @param <T>     数据类型
     * @return 响应对象
     */
    public static <T> R<T> fail(int code, String message) {
        return new R<T>().setCode(code).setMessage(message);
    }

    /**
     * 失败
     *
     * @param code    状态码
     * @param message 消息
     * @param data    数据
     * @param <T>     数据类型
     * @return 响应对象
     */
    public static <T> R<T> fail(int code, String message, T data) {
        return new R<T>().setCode(code).setMessage(message).setData(data);
    }
}