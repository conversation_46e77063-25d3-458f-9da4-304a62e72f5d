package com.rega.erp.common.core.constant;

/**
 * 错误消息常量
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface ErrorMessage {

    // =====================================================
    // 系统级错误消息
    // =====================================================
    
    String SYSTEM_ERROR = "系统异常，请联系管理员";
    String PARAM_VALIDATION_ERROR = "参数校验失败";
    String PARAM_TYPE_ERROR = "参数类型错误";
    String PARAM_MISSING_ERROR = "缺少必要参数";
    String JSON_PARSE_ERROR = "JSON解析失败";
    String FILE_UPLOAD_ERROR = "文件上传失败";
    String FILE_DOWNLOAD_ERROR = "文件下载失败";
    String FILE_TYPE_NOT_SUPPORTED = "不支持的文件类型";
    String FILE_SIZE_EXCEEDED = "文件大小超出限制";
    String DATABASE_ERROR = "数据库操作失败";
    String CACHE_ERROR = "缓存操作失败";
    String NETWORK_ERROR = "网络请求失败";
    String CONFIG_ERROR = "配置错误";
    
    // =====================================================
    // 认证授权错误消息
    // =====================================================
    
    String AUTH_ERROR = "认证失败";
    String LOGIN_ERROR = "用户名或密码错误";
    String ACCOUNT_LOCKED = "账号已被锁定";
    String ACCOUNT_DISABLED = "账号已被禁用";
    String ACCOUNT_EXPIRED = "账号已过期";
    String PASSWORD_EXPIRED = "密码已过期";
    String TOKEN_INVALID = "Token无效";
    String TOKEN_EXPIRED = "Token已过期";
    String PERMISSION_DENIED = "权限不足";
    String ROLE_NOT_FOUND = "角色不存在";
    String PERMISSION_NOT_FOUND = "权限不存在";
    
    // =====================================================
    // 用户管理错误消息
    // =====================================================
    
    String USER_NOT_FOUND = "用户不存在";
    String USERNAME_EXISTS = "用户名已存在";
    String EMAIL_EXISTS = "邮箱已存在";
    String PHONE_EXISTS = "手机号已存在";
    String USER_CREATE_ERROR = "用户创建失败";
    String USER_UPDATE_ERROR = "用户更新失败";
    String USER_DELETE_ERROR = "用户删除失败";
    String PASSWORD_FORMAT_ERROR = "密码格式错误";
    String OLD_PASSWORD_ERROR = "原密码错误";
    String SAME_PASSWORD_ERROR = "新密码不能与原密码相同";
    
    // =====================================================
    // 租户管理错误消息
    // =====================================================
    
    String TENANT_NOT_FOUND = "租户不存在";
    String TENANT_CODE_EXISTS = "租户编码已存在";
    String TENANT_EXPIRED = "租户已过期";
    String TENANT_DISABLED = "租户已被禁用";
    String TENANT_USER_LIMIT_EXCEEDED = "租户用户数量已达上限";
    String TENANT_STORAGE_LIMIT_EXCEEDED = "租户存储空间已达上限";
    String TENANT_CREATE_ERROR = "租户创建失败";
    String TENANT_UPDATE_ERROR = "租户更新失败";
    String TENANT_DELETE_ERROR = "租户删除失败";
    
    // =====================================================
    // 表单管理错误消息
    // =====================================================
    
    String FORM_NOT_FOUND = "表单不存在";
    String FORM_TEMPLATE_NOT_FOUND = "表单模板不存在";
    String FORM_FIELD_CONFIG_ERROR = "表单字段配置错误";
    String FORM_DATA_VALIDATION_ERROR = "表单数据验证失败";
    String FORM_SUBMIT_ERROR = "表单提交失败";
    String FORM_STATUS_ERROR = "表单状态错误";
    
    // =====================================================
    // 工作流错误消息
    // =====================================================
    
    String WORKFLOW_NOT_FOUND = "工作流不存在";
    String WORKFLOW_DEFINITION_ERROR = "工作流定义错误";
    String WORKFLOW_INSTANCE_NOT_FOUND = "工作流实例不存在";
    String WORKFLOW_TASK_NOT_FOUND = "工作流任务不存在";
    String WORKFLOW_STATUS_ERROR = "工作流状态错误";
    String WORKFLOW_APPROVE_ERROR = "工作流审批失败";
    String WORKFLOW_REJECT_ERROR = "工作流拒绝失败";
    
    // =====================================================
    // 报表管理错误消息
    // =====================================================
    
    String REPORT_NOT_FOUND = "报表不存在";
    String REPORT_TEMPLATE_NOT_FOUND = "报表模板不存在";
    String REPORT_GENERATE_ERROR = "报表生成失败";
    String REPORT_EXPORT_ERROR = "报表导出失败";
    String REPORT_DATASOURCE_ERROR = "报表数据源错误";
    
    // =====================================================
    // 业务错误消息
    // =====================================================
    
    String BUSINESS_RULE_VALIDATION_ERROR = "业务规则验证失败";
    String DATA_STATUS_ERROR = "数据状态错误";
    String DATA_EXISTS = "数据已存在";
    String DATA_NOT_FOUND = "数据不存在";
    String DATA_DELETED = "数据已被删除";
    String DATA_VERSION_CONFLICT = "数据版本冲突";
    String OPERATION_NOT_ALLOWED = "操作不被允许";
    String OPERATION_TIMEOUT = "操作超时";
    String OPERATION_RATE_LIMIT = "操作过于频繁，请稍后再试";
    
    // =====================================================
    // 第三方服务错误消息
    // =====================================================
    
    String THIRD_PARTY_SERVICE_UNAVAILABLE = "第三方服务不可用";
    String THIRD_PARTY_AUTH_ERROR = "第三方服务认证失败";
    String THIRD_PARTY_REQUEST_ERROR = "第三方服务请求失败";
    String THIRD_PARTY_RESPONSE_FORMAT_ERROR = "第三方服务响应格式错误";
    String THIRD_PARTY_TIMEOUT = "第三方服务超时";
    
    // =====================================================
    // 成功消息
    // =====================================================
    
    String SUCCESS = "操作成功";
    String CREATE_SUCCESS = "创建成功";
    String UPDATE_SUCCESS = "更新成功";
    String DELETE_SUCCESS = "删除成功";
    String QUERY_SUCCESS = "查询成功";
    String SAVE_SUCCESS = "保存成功";
    String SUBMIT_SUCCESS = "提交成功";
    String APPROVE_SUCCESS = "审批成功";
    String REJECT_SUCCESS = "拒绝成功";
    String EXPORT_SUCCESS = "导出成功";
    String IMPORT_SUCCESS = "导入成功";
    String UPLOAD_SUCCESS = "上传成功";
    String DOWNLOAD_SUCCESS = "下载成功";
}
