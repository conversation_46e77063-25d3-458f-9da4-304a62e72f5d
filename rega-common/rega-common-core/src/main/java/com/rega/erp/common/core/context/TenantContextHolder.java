package com.rega.erp.common.core.context;

/**
 * 租户上下文持有者
 *
 * <AUTHOR>
 */
public class TenantContextHolder {

    /**
     * 租户ID的ThreadLocal
     */
    private static final ThreadLocal<String> TENANT_ID_HOLDER = new ThreadLocal<>();

    /**
     * 租户名称的ThreadLocal
     */
    private static final ThreadLocal<String> TENANT_NAME_HOLDER = new ThreadLocal<>();

    /**
     * 设置租户ID
     *
     * @param tenantId 租户ID
     */
    public static void setTenantId(String tenantId) {
        TENANT_ID_HOLDER.set(tenantId);
    }

    /**
     * 获取租户ID
     *
     * @return 租户ID
     */
    public static String getTenantId() {
        return TENANT_ID_HOLDER.get();
    }

    /**
     * 设置租户名称
     *
     * @param tenantName 租户名称
     */
    public static void setTenantName(String tenantName) {
        TENANT_NAME_HOLDER.set(tenantName);
    }

    /**
     * 获取租户名称
     *
     * @return 租户名称
     */
    public static String getTenantName() {
        return TENANT_NAME_HOLDER.get();
    }

    /**
     * 清除租户信息
     */
    public static void clear() {
        TENANT_ID_HOLDER.remove();
        TENANT_NAME_HOLDER.remove();
    }

    /**
     * 清除租户ID
     */
    public static void clearTenantId() {
        TENANT_ID_HOLDER.remove();
    }

    /**
     * 清除租户名称
     */
    public static void clearTenantName() {
        TENANT_NAME_HOLDER.remove();
    }

    /**
     * 判断是否存在租户ID
     *
     * @return 是否存在租户ID
     */
    public static boolean hasTenantId() {
        return TENANT_ID_HOLDER.get() != null;
    }

    /**
     * 判断是否存在租户名称
     *
     * @return 是否存在租户名称
     */
    public static boolean hasTenantName() {
        return TENANT_NAME_HOLDER.get() != null;
    }

    /**
     * 获取租户ID，如果为空则返回默认值
     *
     * @param defaultValue 默认值
     * @return 租户ID
     */
    public static String getTenantId(String defaultValue) {
        String tenantId = getTenantId();
        return tenantId != null ? tenantId : defaultValue;
    }

    /**
     * 获取租户名称，如果为空则返回默认值
     *
     * @param defaultValue 默认值
     * @return 租户名称
     */
    public static String getTenantName(String defaultValue) {
        String tenantName = getTenantName();
        return tenantName != null ? tenantName : defaultValue;
    }
}
