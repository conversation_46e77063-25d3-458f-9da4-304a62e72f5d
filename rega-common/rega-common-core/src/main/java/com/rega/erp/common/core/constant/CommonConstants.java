package com.rega.erp.common.core.constant;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public interface CommonConstants {

    /**
     * 成功标记
     */
    Integer SUCCESS = 200;

    /**
     * 失败标记
     */
    Integer FAIL = 500;

    /**
     * 未授权
     */
    Integer UNAUTHORIZED = 401;

    /**
     * 禁止访问
     */
    Integer FORBIDDEN = 403;

    /**
     * 未找到
     */
    Integer NOT_FOUND = 404;
    
    /**
     * 默认页码
     */
    Integer DEFAULT_PAGE_NUM = 1;
    
    /**
     * 默认分页大小
     */
    Integer DEFAULT_PAGE_SIZE = 10;
    
    /**
     * 最大分页大小
     */
    Integer MAX_PAGE_SIZE = 100;

    /**
     * 当前记录起始索引名称
     */
    String PAGE_NUM = "pageNum";

    /**
     * 每页显示记录数名称
     */
    String PAGE_SIZE = "pageSize";

    /**
     * 排序列名称
     */
    String ORDER_BY_COLUMN = "orderByColumn";

    /**
     * 排序的方向名称
     */
    String IS_ASC = "isAsc";

    /**
     * 升序
     */
    String ASC = "asc";

    /**
     * 降序
     */
    String DESC = "desc";
} 