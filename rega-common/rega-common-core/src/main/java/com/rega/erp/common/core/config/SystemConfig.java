package com.rega.erp.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 系统配置
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "rega.system")
public class SystemConfig {

    /**
     * 系统名称
     */
    private String name = "RegaWebERP";

    /**
     * 系统版本
     */
    private String version = "1.0.0";

    /**
     * 系统描述
     */
    private String description = "Rega Web ERP System";

    /**
     * 系统作者
     */
    private String author = "wanghy";

    /**
     * 系统邮箱
     */
    private String email = "<EMAIL>";

    /**
     * 系统网站
     */
    private String website = "https://www.rega.com";

    /**
     * 系统电话
     */
    private String phone = "************";

    /**
     * 系统地址
     */
    private String address = "北京市朝阳区";

    /**
     * 系统备案号
     */
    private String icp = "";

    /**
     * 系统公安备案号
     */
    private String police = "";

    /**
     * 系统logo
     */
    private String logo = "/static/images/logo.png";

    /**
     * 系统favicon
     */
    private String favicon = "/static/images/favicon.ico";

    /**
     * 系统默认主题
     */
    private String theme = "default";

    /**
     * 系统默认语言
     */
    private String language = "zh-CN";

    /**
     * 系统时区
     */
    private String timezone = "Asia/Shanghai";

    /**
     * 系统日期格式
     */
    private String dateFormat = "yyyy-MM-dd";

    /**
     * 系统时间格式
     */
    private String timeFormat = "HH:mm:ss";

    /**
     * 系统日期时间格式
     */
    private String dateTimeFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * 系统货币符号
     */
    private String currency = "¥";

    /**
     * 系统数字格式
     */
    private String numberFormat = "#,##0.00";

    /**
     * 系统分页大小
     */
    private Integer pageSize = 20;

    /**
     * 系统最大分页大小
     */
    private Integer maxPageSize = 500;

    /**
     * 系统会话超时时间（分钟）
     */
    private Integer sessionTimeout = 30;

    /**
     * 系统密码最小长度
     */
    private Integer passwordMinLength = 6;

    /**
     * 系统密码最大长度
     */
    private Integer passwordMaxLength = 20;

    /**
     * 系统密码复杂度要求
     */
    private Boolean passwordComplexity = false;

    /**
     * 系统密码过期天数
     */
    private Integer passwordExpireDays = 90;

    /**
     * 系统登录失败锁定次数
     */
    private Integer loginFailLockCount = 5;

    /**
     * 系统登录失败锁定时间（分钟）
     */
    private Integer loginFailLockTime = 30;

    /**
     * 系统验证码开关
     */
    private Boolean captchaEnabled = true;

    /**
     * 系统验证码类型
     */
    private String captchaType = "math";

    /**
     * 系统验证码长度
     */
    private Integer captchaLength = 4;

    /**
     * 系统验证码过期时间（分钟）
     */
    private Integer captchaExpireTime = 5;

    /**
     * 系统文件上传大小限制（MB）
     */
    private Integer uploadMaxSize = 100;

    /**
     * 系统文件上传类型限制
     */
    private String uploadAllowTypes = "jpg,jpeg,png,gif,bmp,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar";

    /**
     * 系统文件上传路径
     */
    private String uploadPath = "/upload";

    /**
     * 系统文件下载路径
     */
    private String downloadPath = "/download";

    /**
     * 系统临时文件路径
     */
    private String tempPath = "/temp";

    /**
     * 系统备份路径
     */
    private String backupPath = "/backup";

    /**
     * 系统日志路径
     */
    private String logPath = "/logs";

    /**
     * 系统缓存开关
     */
    private Boolean cacheEnabled = true;

    /**
     * 系统缓存类型
     */
    private String cacheType = "redis";

    /**
     * 系统缓存过期时间（秒）
     */
    private Integer cacheExpireTime = 3600;

    /**
     * 系统多租户开关
     */
    private Boolean multiTenantEnabled = true;

    /**
     * 系统多租户模式
     */
    private String multiTenantMode = "schema";

    /**
     * 系统国际化开关
     */
    private Boolean i18nEnabled = true;

    /**
     * 系统审计日志开关
     */
    private Boolean auditLogEnabled = true;

    /**
     * 系统操作日志开关
     */
    private Boolean operateLogEnabled = true;

    /**
     * 系统登录日志开关
     */
    private Boolean loginLogEnabled = true;

    /**
     * 系统性能监控开关
     */
    private Boolean performanceMonitorEnabled = false;

    /**
     * 系统安全模式开关
     */
    private Boolean securityModeEnabled = false;

    /**
     * 系统调试模式开关
     */
    private Boolean debugModeEnabled = false;

    /**
     * 系统演示模式开关
     */
    private Boolean demoModeEnabled = false;
}
