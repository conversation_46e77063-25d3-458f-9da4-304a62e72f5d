package com.rega.erp.common.core.util;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * 加密工具类
 *
 * <AUTHOR>
 */
public class CryptoUtils {

    private static final String AES_ALGORITHM = "AES";
    private static final String AES_TRANSFORMATION = "AES/ECB/PKCS5Padding";
    private static final String DEFAULT_CHARSET = "UTF-8";

    /**
     * MD5加密
     *
     * @param data 待加密数据
     * @return 加密后的字符串
     */
    public static String md5(String data) {
        return DigestUtils.md5Hex(data);
    }

    /**
     * MD5加密（带盐值）
     *
     * @param data 待加密数据
     * @param salt 盐值
     * @return 加密后的字符串
     */
    public static String md5WithSalt(String data, String salt) {
        return DigestUtils.md5Hex(data + salt);
    }

    /**
     * SHA1加密
     *
     * @param data 待加密数据
     * @return 加密后的字符串
     */
    public static String sha1(String data) {
        return DigestUtils.sha1Hex(data);
    }

    /**
     * SHA256加密
     *
     * @param data 待加密数据
     * @return 加密后的字符串
     */
    public static String sha256(String data) {
        return DigestUtils.sha256Hex(data);
    }

    /**
     * SHA512加密
     *
     * @param data 待加密数据
     * @return 加密后的字符串
     */
    public static String sha512(String data) {
        return DigestUtils.sha512Hex(data);
    }

    /**
     * Base64编码
     *
     * @param data 待编码数据
     * @return 编码后的字符串
     */
    public static String base64Encode(String data) {
        return Base64.encodeBase64String(data.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * Base64编码
     *
     * @param data 待编码数据
     * @return 编码后的字符串
     */
    public static String base64Encode(byte[] data) {
        return Base64.encodeBase64String(data);
    }

    /**
     * Base64解码
     *
     * @param data 待解码数据
     * @return 解码后的字符串
     */
    public static String base64Decode(String data) {
        return new String(Base64.decodeBase64(data), StandardCharsets.UTF_8);
    }

    /**
     * Base64解码
     *
     * @param data 待解码数据
     * @return 解码后的字节数组
     */
    public static byte[] base64DecodeToBytes(String data) {
        return Base64.decodeBase64(data);
    }

    /**
     * 生成AES密钥
     *
     * @return AES密钥
     */
    public static String generateAESKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM);
            keyGenerator.init(256);
            SecretKey secretKey = keyGenerator.generateKey();
            return base64Encode(secretKey.getEncoded());
        } catch (Exception e) {
            throw new RuntimeException("生成AES密钥失败", e);
        }
    }

    /**
     * AES加密
     *
     * @param data 待加密数据
     * @param key  密钥
     * @return 加密后的字符串
     */
    public static String aesEncrypt(String data, String key) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(base64DecodeToBytes(key), AES_ALGORITHM);
            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
            byte[] encrypted = cipher.doFinal(data.getBytes(DEFAULT_CHARSET));
            return base64Encode(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("AES加密失败", e);
        }
    }

    /**
     * AES解密
     *
     * @param encryptedData 加密数据
     * @param key           密钥
     * @return 解密后的字符串
     */
    public static String aesDecrypt(String encryptedData, String key) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(base64DecodeToBytes(key), AES_ALGORITHM);
            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
            byte[] decrypted = cipher.doFinal(base64DecodeToBytes(encryptedData));
            return new String(decrypted, DEFAULT_CHARSET);
        } catch (Exception e) {
            throw new RuntimeException("AES解密失败", e);
        }
    }

    /**
     * 生成随机盐值
     *
     * @param length 盐值长度
     * @return 盐值
     */
    public static String generateSalt(int length) {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[length];
        random.nextBytes(salt);
        return base64Encode(salt);
    }

    /**
     * 生成随机盐值（默认16位）
     *
     * @return 盐值
     */
    public static String generateSalt() {
        return generateSalt(16);
    }

    /**
     * HMAC-SHA256加密
     *
     * @param data 待加密数据
     * @param key  密钥
     * @return 加密后的字符串
     */
    public static String hmacSha256(String data, String key) {
        try {
            javax.crypto.Mac mac = javax.crypto.Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(DEFAULT_CHARSET), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] hash = mac.doFinal(data.getBytes(DEFAULT_CHARSET));
            return bytesToHex(hash);
        } catch (Exception e) {
            throw new RuntimeException("HMAC-SHA256加密失败", e);
        }
    }

    /**
     * 字节数组转十六进制字符串
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 十六进制字符串转字节数组
     *
     * @param hex 十六进制字符串
     * @return 字节数组
     */
    public static byte[] hexToBytes(String hex) {
        int length = hex.length();
        byte[] result = new byte[length / 2];
        for (int i = 0; i < length; i += 2) {
            result[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return result;
    }

    /**
     * 验证MD5
     *
     * @param data 原始数据
     * @param hash MD5哈希值
     * @return 是否匹配
     */
    public static boolean verifyMd5(String data, String hash) {
        return md5(data).equals(hash);
    }

    /**
     * 验证SHA256
     *
     * @param data 原始数据
     * @param hash SHA256哈希值
     * @return 是否匹配
     */
    public static boolean verifySha256(String data, String hash) {
        return sha256(data).equals(hash);
    }

    /**
     * 简单的字符串混淆
     *
     * @param data 待混淆数据
     * @return 混淆后的字符串
     */
    public static String obfuscate(String data) {
        if (StringUtils.isEmpty(data)) {
            return data;
        }
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < data.length(); i++) {
            result.append((char) (data.charAt(i) ^ 0x5A));
        }
        return base64Encode(result.toString());
    }

    /**
     * 简单的字符串反混淆
     *
     * @param obfuscatedData 混淆后的数据
     * @return 原始字符串
     */
    public static String deobfuscate(String obfuscatedData) {
        if (StringUtils.isEmpty(obfuscatedData)) {
            return obfuscatedData;
        }
        String decoded = base64Decode(obfuscatedData);
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < decoded.length(); i++) {
            result.append((char) (decoded.charAt(i) ^ 0x5A));
        }
        return result.toString();
    }
}
