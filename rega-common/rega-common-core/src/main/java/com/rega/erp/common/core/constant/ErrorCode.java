package com.rega.erp.common.core.constant;

/**
 * 错误码常量
 * 
 * 错误码规范：
 * - 成功：200
 * - 客户端错误：400-499
 * - 服务端错误：500-599
 * - 业务错误：1000-9999
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface ErrorCode {

    // =====================================================
    // HTTP 状态码
    // =====================================================
    
    /** 成功 */
    int SUCCESS = 200;
    
    /** 请求错误 */
    int BAD_REQUEST = 400;
    
    /** 未授权 */
    int UNAUTHORIZED = 401;
    
    /** 禁止访问 */
    int FORBIDDEN = 403;
    
    /** 资源不存在 */
    int NOT_FOUND = 404;
    
    /** 方法不允许 */
    int METHOD_NOT_ALLOWED = 405;
    
    /** 请求超时 */
    int REQUEST_TIMEOUT = 408;
    
    /** 参数错误 */
    int UNPROCESSABLE_ENTITY = 422;
    
    /** 请求过于频繁 */
    int TOO_MANY_REQUESTS = 429;
    
    /** 服务器内部错误 */
    int INTERNAL_SERVER_ERROR = 500;
    
    /** 服务不可用 */
    int SERVICE_UNAVAILABLE = 503;
    
    // =====================================================
    // 系统级错误码 (1000-1999)
    // =====================================================
    
    /** 系统异常 */
    int SYSTEM_ERROR = 1000;
    
    /** 参数校验失败 */
    int PARAM_VALIDATION_ERROR = 1001;
    
    /** 参数类型错误 */
    int PARAM_TYPE_ERROR = 1002;
    
    /** 参数缺失 */
    int PARAM_MISSING_ERROR = 1003;
    
    /** JSON 解析错误 */
    int JSON_PARSE_ERROR = 1004;
    
    /** 文件上传失败 */
    int FILE_UPLOAD_ERROR = 1005;
    
    /** 文件下载失败 */
    int FILE_DOWNLOAD_ERROR = 1006;
    
    /** 文件类型不支持 */
    int FILE_TYPE_NOT_SUPPORTED = 1007;
    
    /** 文件大小超限 */
    int FILE_SIZE_EXCEEDED = 1008;
    
    /** 数据库操作失败 */
    int DATABASE_ERROR = 1009;
    
    /** 缓存操作失败 */
    int CACHE_ERROR = 1010;
    
    /** 网络请求失败 */
    int NETWORK_ERROR = 1011;
    
    /** 配置错误 */
    int CONFIG_ERROR = 1012;
    
    // =====================================================
    // 认证授权错误码 (2000-2999)
    // =====================================================
    
    /** 认证失败 */
    int AUTH_ERROR = 2000;
    
    /** 用户名或密码错误 */
    int LOGIN_ERROR = 2001;
    
    /** 账号被锁定 */
    int ACCOUNT_LOCKED = 2002;
    
    /** 账号被禁用 */
    int ACCOUNT_DISABLED = 2003;
    
    /** 账号已过期 */
    int ACCOUNT_EXPIRED = 2004;
    
    /** 密码已过期 */
    int PASSWORD_EXPIRED = 2005;
    
    /** Token 无效 */
    int TOKEN_INVALID = 2006;
    
    /** Token 已过期 */
    int TOKEN_EXPIRED = 2007;
    
    /** 权限不足 */
    int PERMISSION_DENIED = 2008;
    
    /** 角色不存在 */
    int ROLE_NOT_FOUND = 2009;
    
    /** 权限不存在 */
    int PERMISSION_NOT_FOUND = 2010;
    
    // =====================================================
    // 用户管理错误码 (3000-3999)
    // =====================================================
    
    /** 用户不存在 */
    int USER_NOT_FOUND = 3000;
    
    /** 用户名已存在 */
    int USERNAME_EXISTS = 3001;
    
    /** 邮箱已存在 */
    int EMAIL_EXISTS = 3002;
    
    /** 手机号已存在 */
    int PHONE_EXISTS = 3003;
    
    /** 用户创建失败 */
    int USER_CREATE_ERROR = 3004;
    
    /** 用户更新失败 */
    int USER_UPDATE_ERROR = 3005;
    
    /** 用户删除失败 */
    int USER_DELETE_ERROR = 3006;
    
    /** 密码格式错误 */
    int PASSWORD_FORMAT_ERROR = 3007;
    
    /** 原密码错误 */
    int OLD_PASSWORD_ERROR = 3008;
    
    /** 新密码与原密码相同 */
    int SAME_PASSWORD_ERROR = 3009;
    
    // =====================================================
    // 租户管理错误码 (4000-4999)
    // =====================================================
    
    /** 租户不存在 */
    int TENANT_NOT_FOUND = 4000;
    
    /** 租户编码已存在 */
    int TENANT_CODE_EXISTS = 4001;
    
    /** 租户已过期 */
    int TENANT_EXPIRED = 4002;
    
    /** 租户已禁用 */
    int TENANT_DISABLED = 4003;
    
    /** 租户用户数量超限 */
    int TENANT_USER_LIMIT_EXCEEDED = 4004;
    
    /** 租户存储空间超限 */
    int TENANT_STORAGE_LIMIT_EXCEEDED = 4005;
    
    /** 租户创建失败 */
    int TENANT_CREATE_ERROR = 4006;
    
    /** 租户更新失败 */
    int TENANT_UPDATE_ERROR = 4007;
    
    /** 租户删除失败 */
    int TENANT_DELETE_ERROR = 4008;
    
    // =====================================================
    // 表单管理错误码 (5000-5999)
    // =====================================================
    
    /** 表单不存在 */
    int FORM_NOT_FOUND = 5000;
    
    /** 表单模板不存在 */
    int FORM_TEMPLATE_NOT_FOUND = 5001;
    
    /** 表单字段配置错误 */
    int FORM_FIELD_CONFIG_ERROR = 5002;
    
    /** 表单数据验证失败 */
    int FORM_DATA_VALIDATION_ERROR = 5003;
    
    /** 表单提交失败 */
    int FORM_SUBMIT_ERROR = 5004;
    
    /** 表单状态错误 */
    int FORM_STATUS_ERROR = 5005;
    
    // =====================================================
    // 工作流错误码 (6000-6999)
    // =====================================================
    
    /** 工作流不存在 */
    int WORKFLOW_NOT_FOUND = 6000;
    
    /** 工作流定义错误 */
    int WORKFLOW_DEFINITION_ERROR = 6001;
    
    /** 工作流实例不存在 */
    int WORKFLOW_INSTANCE_NOT_FOUND = 6002;
    
    /** 工作流任务不存在 */
    int WORKFLOW_TASK_NOT_FOUND = 6003;
    
    /** 工作流状态错误 */
    int WORKFLOW_STATUS_ERROR = 6004;
    
    /** 工作流审批失败 */
    int WORKFLOW_APPROVE_ERROR = 6005;
    
    /** 工作流拒绝失败 */
    int WORKFLOW_REJECT_ERROR = 6006;
    
    // =====================================================
    // 报表管理错误码 (7000-7999)
    // =====================================================
    
    /** 报表不存在 */
    int REPORT_NOT_FOUND = 7000;
    
    /** 报表模板不存在 */
    int REPORT_TEMPLATE_NOT_FOUND = 7001;
    
    /** 报表生成失败 */
    int REPORT_GENERATE_ERROR = 7002;
    
    /** 报表导出失败 */
    int REPORT_EXPORT_ERROR = 7003;
    
    /** 报表数据源错误 */
    int REPORT_DATASOURCE_ERROR = 7004;
    
    // =====================================================
    // 业务错误码 (8000-8999)
    // =====================================================
    
    /** 业务规则验证失败 */
    int BUSINESS_RULE_VALIDATION_ERROR = 8000;
    
    /** 数据状态错误 */
    int DATA_STATUS_ERROR = 8001;
    
    /** 数据已存在 */
    int DATA_EXISTS = 8002;
    
    /** 数据不存在 */
    int DATA_NOT_FOUND = 8003;
    
    /** 数据已被删除 */
    int DATA_DELETED = 8004;
    
    /** 数据版本冲突 */
    int DATA_VERSION_CONFLICT = 8005;
    
    /** 操作不允许 */
    int OPERATION_NOT_ALLOWED = 8006;
    
    /** 操作超时 */
    int OPERATION_TIMEOUT = 8007;
    
    /** 操作频率限制 */
    int OPERATION_RATE_LIMIT = 8008;
    
    // =====================================================
    // 第三方服务错误码 (9000-9999)
    // =====================================================
    
    /** 第三方服务不可用 */
    int THIRD_PARTY_SERVICE_UNAVAILABLE = 9000;
    
    /** 第三方服务认证失败 */
    int THIRD_PARTY_AUTH_ERROR = 9001;
    
    /** 第三方服务请求失败 */
    int THIRD_PARTY_REQUEST_ERROR = 9002;
    
    /** 第三方服务响应格式错误 */
    int THIRD_PARTY_RESPONSE_FORMAT_ERROR = 9003;
    
    /** 第三方服务超时 */
    int THIRD_PARTY_TIMEOUT = 9004;
}
