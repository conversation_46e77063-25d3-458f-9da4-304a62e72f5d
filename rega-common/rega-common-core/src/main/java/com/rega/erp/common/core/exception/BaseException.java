package com.rega.erp.common.core.exception;

import lombok.Getter;

/**
 * 基础异常类
 */
@Getter
public class BaseException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /** 错误码 */
    private Integer code;
    
    /** 错误消息 */
    private String message;

    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public BaseException(String message) {
        super(message);
        this.message = message;
    }

    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param message 错误消息
     */
    public BaseException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param cause 异常
     */
    public BaseException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
}
