package com.rega.erp.common.core.util;

import com.rega.erp.common.core.context.TenantContextHolder;

import java.util.function.Supplier;

/**
 * 租户工具类
 *
 * <AUTHOR>
 */
public class TenantUtils {

    /**
     * 默认租户ID
     */
    public static final String DEFAULT_TENANT_ID = "000000";

    /**
     * 租户ID分隔符
     */
    public static final String TENANT_ID_SEPARATOR = "_";

    /**
     * 获取当前租户ID
     *
     * @return 租户ID
     */
    public static String getCurrentTenantId() {
        return TenantContextHolder.getTenantId();
    }

    /**
     * 获取当前租户ID，如果为空则返回默认租户ID
     *
     * @return 租户ID
     */
    public static String getCurrentTenantIdOrDefault() {
        return TenantContextHolder.getTenantId(DEFAULT_TENANT_ID);
    }

    /**
     * 获取当前租户名称
     *
     * @return 租户名称
     */
    public static String getCurrentTenantName() {
        return TenantContextHolder.getTenantName();
    }

    /**
     * 设置租户ID
     *
     * @param tenantId 租户ID
     */
    public static void setTenantId(String tenantId) {
        TenantContextHolder.setTenantId(tenantId);
    }

    /**
     * 设置租户名称
     *
     * @param tenantName 租户名称
     */
    public static void setTenantName(String tenantName) {
        TenantContextHolder.setTenantName(tenantName);
    }

    /**
     * 清除租户信息
     */
    public static void clearTenant() {
        TenantContextHolder.clear();
    }

    /**
     * 判断是否为默认租户
     *
     * @return 是否为默认租户
     */
    public static boolean isDefaultTenant() {
        return DEFAULT_TENANT_ID.equals(getCurrentTenantId());
    }

    /**
     * 判断是否为指定租户
     *
     * @param tenantId 租户ID
     * @return 是否为指定租户
     */
    public static boolean isTenant(String tenantId) {
        return tenantId != null && tenantId.equals(getCurrentTenantId());
    }

    /**
     * 判断是否存在租户信息
     *
     * @return 是否存在租户信息
     */
    public static boolean hasTenant() {
        return TenantContextHolder.hasTenantId();
    }

    /**
     * 在指定租户上下文中执行操作
     *
     * @param tenantId 租户ID
     * @param runnable 执行的操作
     */
    public static void runWithTenant(String tenantId, Runnable runnable) {
        String originalTenantId = getCurrentTenantId();
        try {
            setTenantId(tenantId);
            runnable.run();
        } finally {
            if (originalTenantId != null) {
                setTenantId(originalTenantId);
            } else {
                TenantContextHolder.clearTenantId();
            }
        }
    }

    /**
     * 在指定租户上下文中执行操作并返回结果
     *
     * @param tenantId 租户ID
     * @param supplier 执行的操作
     * @param <T>      返回类型
     * @return 执行结果
     */
    public static <T> T runWithTenant(String tenantId, Supplier<T> supplier) {
        String originalTenantId = getCurrentTenantId();
        try {
            setTenantId(tenantId);
            return supplier.get();
        } finally {
            if (originalTenantId != null) {
                setTenantId(originalTenantId);
            } else {
                TenantContextHolder.clearTenantId();
            }
        }
    }

    /**
     * 在默认租户上下文中执行操作
     *
     * @param runnable 执行的操作
     */
    public static void runWithDefaultTenant(Runnable runnable) {
        runWithTenant(DEFAULT_TENANT_ID, runnable);
    }

    /**
     * 在默认租户上下文中执行操作并返回结果
     *
     * @param supplier 执行的操作
     * @param <T>      返回类型
     * @return 执行结果
     */
    public static <T> T runWithDefaultTenant(Supplier<T> supplier) {
        return runWithTenant(DEFAULT_TENANT_ID, supplier);
    }

    /**
     * 生成带租户前缀的键
     *
     * @param key 原始键
     * @return 带租户前缀的键
     */
    public static String generateTenantKey(String key) {
        String tenantId = getCurrentTenantIdOrDefault();
        return tenantId + TENANT_ID_SEPARATOR + key;
    }

    /**
     * 生成指定租户的键
     *
     * @param tenantId 租户ID
     * @param key      原始键
     * @return 带租户前缀的键
     */
    public static String generateTenantKey(String tenantId, String key) {
        return tenantId + TENANT_ID_SEPARATOR + key;
    }

    /**
     * 从带租户前缀的键中提取租户ID
     *
     * @param tenantKey 带租户前缀的键
     * @return 租户ID
     */
    public static String extractTenantId(String tenantKey) {
        if (StringUtils.isEmpty(tenantKey)) {
            return null;
        }
        int index = tenantKey.indexOf(TENANT_ID_SEPARATOR);
        if (index > 0) {
            return tenantKey.substring(0, index);
        }
        return null;
    }

    /**
     * 从带租户前缀的键中提取原始键
     *
     * @param tenantKey 带租户前缀的键
     * @return 原始键
     */
    public static String extractOriginalKey(String tenantKey) {
        if (StringUtils.isEmpty(tenantKey)) {
            return null;
        }
        int index = tenantKey.indexOf(TENANT_ID_SEPARATOR);
        if (index > 0 && index < tenantKey.length() - 1) {
            return tenantKey.substring(index + 1);
        }
        return tenantKey;
    }

    /**
     * 验证租户ID格式
     *
     * @param tenantId 租户ID
     * @return 是否有效
     */
    public static boolean isValidTenantId(String tenantId) {
        if (StringUtils.isEmpty(tenantId)) {
            return false;
        }
        // 租户ID应该是6位数字
        return tenantId.matches("^\\d{6}$");
    }

    /**
     * 格式化租户ID（补零到6位）
     *
     * @param tenantId 租户ID
     * @return 格式化后的租户ID
     */
    public static String formatTenantId(String tenantId) {
        if (StringUtils.isEmpty(tenantId)) {
            return DEFAULT_TENANT_ID;
        }
        try {
            int id = Integer.parseInt(tenantId);
            return String.format("%06d", id);
        } catch (NumberFormatException e) {
            return DEFAULT_TENANT_ID;
        }
    }

    /**
     * 生成新的租户ID
     *
     * @return 新的租户ID
     */
    public static String generateTenantId() {
        // 生成6位随机数字作为租户ID
        return IdUtils.randomNumeric(6);
    }
}
