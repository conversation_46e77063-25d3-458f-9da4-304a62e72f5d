package com.rega.erp.common.core.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class JsonUtils {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        // 配置ObjectMapper
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        OBJECT_MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        OBJECT_MAPPER.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        
        // 注册Java 8时间模块
        OBJECT_MAPPER.registerModule(new JavaTimeModule());
        
        // 设置日期格式
        OBJECT_MAPPER.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 对象转JSON字符串
     *
     * @param object 对象
     * @return JSON字符串
     */
    public static String toJsonString(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象转JSON字符串失败", e);
            return null;
        }
    }

    /**
     * 对象转格式化的JSON字符串
     *
     * @param object 对象
     * @return 格式化的JSON字符串
     */
    public static String toPrettyJsonString(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象转格式化JSON字符串失败", e);
            return null;
        }
    }

    /**
     * JSON字符串转对象
     *
     * @param jsonString JSON字符串
     * @param clazz      目标类型
     * @param <T>        泛型
     * @return 对象
     */
    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        if (StringUtils.isEmpty(jsonString) || clazz == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonString, clazz);
        } catch (IOException e) {
            log.error("JSON字符串转对象失败", e);
            return null;
        }
    }

    /**
     * JSON字符串转对象（支持泛型）
     *
     * @param jsonString    JSON字符串
     * @param typeReference 类型引用
     * @param <T>           泛型
     * @return 对象
     */
    public static <T> T parseObject(String jsonString, TypeReference<T> typeReference) {
        if (StringUtils.isEmpty(jsonString) || typeReference == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonString, typeReference);
        } catch (IOException e) {
            log.error("JSON字符串转对象失败", e);
            return null;
        }
    }

    /**
     * JSON字符串转List
     *
     * @param jsonString JSON字符串
     * @param clazz      元素类型
     * @param <T>        泛型
     * @return List对象
     */
    public static <T> List<T> parseList(String jsonString, Class<T> clazz) {
        if (StringUtils.isEmpty(jsonString) || clazz == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonString, 
                    OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (IOException e) {
            log.error("JSON字符串转List失败", e);
            return null;
        }
    }

    /**
     * JSON字符串转Map
     *
     * @param jsonString JSON字符串
     * @return Map对象
     */
    public static Map<String, Object> parseMap(String jsonString) {
        if (StringUtils.isEmpty(jsonString)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonString, 
                    new TypeReference<Map<String, Object>>() {});
        } catch (IOException e) {
            log.error("JSON字符串转Map失败", e);
            return null;
        }
    }

    /**
     * JSON字符串转Map（指定值类型）
     *
     * @param jsonString JSON字符串
     * @param valueClass 值类型
     * @param <T>        泛型
     * @return Map对象
     */
    public static <T> Map<String, T> parseMap(String jsonString, Class<T> valueClass) {
        if (StringUtils.isEmpty(jsonString) || valueClass == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonString, 
                    OBJECT_MAPPER.getTypeFactory().constructMapType(Map.class, String.class, valueClass));
        } catch (IOException e) {
            log.error("JSON字符串转Map失败", e);
            return null;
        }
    }

    /**
     * 获取JsonNode
     *
     * @param jsonString JSON字符串
     * @return JsonNode
     */
    public static JsonNode parseTree(String jsonString) {
        if (StringUtils.isEmpty(jsonString)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readTree(jsonString);
        } catch (IOException e) {
            log.error("JSON字符串转JsonNode失败", e);
            return null;
        }
    }

    /**
     * 从JsonNode中获取字符串值
     *
     * @param jsonNode JsonNode
     * @param fieldName 字段名
     * @return 字符串值
     */
    public static String getString(JsonNode jsonNode, String fieldName) {
        if (jsonNode == null || StringUtils.isEmpty(fieldName)) {
            return null;
        }
        JsonNode fieldNode = jsonNode.get(fieldName);
        return fieldNode != null ? fieldNode.asText() : null;
    }

    /**
     * 从JsonNode中获取整数值
     *
     * @param jsonNode JsonNode
     * @param fieldName 字段名
     * @return 整数值
     */
    public static Integer getInteger(JsonNode jsonNode, String fieldName) {
        if (jsonNode == null || StringUtils.isEmpty(fieldName)) {
            return null;
        }
        JsonNode fieldNode = jsonNode.get(fieldName);
        return fieldNode != null ? fieldNode.asInt() : null;
    }

    /**
     * 从JsonNode中获取长整数值
     *
     * @param jsonNode JsonNode
     * @param fieldName 字段名
     * @return 长整数值
     */
    public static Long getLong(JsonNode jsonNode, String fieldName) {
        if (jsonNode == null || StringUtils.isEmpty(fieldName)) {
            return null;
        }
        JsonNode fieldNode = jsonNode.get(fieldName);
        return fieldNode != null ? fieldNode.asLong() : null;
    }

    /**
     * 从JsonNode中获取布尔值
     *
     * @param jsonNode JsonNode
     * @param fieldName 字段名
     * @return 布尔值
     */
    public static Boolean getBoolean(JsonNode jsonNode, String fieldName) {
        if (jsonNode == null || StringUtils.isEmpty(fieldName)) {
            return null;
        }
        JsonNode fieldNode = jsonNode.get(fieldName);
        return fieldNode != null ? fieldNode.asBoolean() : null;
    }

    /**
     * 判断是否为有效的JSON字符串
     *
     * @param jsonString JSON字符串
     * @return 是否有效
     */
    public static boolean isValidJson(String jsonString) {
        if (StringUtils.isEmpty(jsonString)) {
            return false;
        }
        try {
            OBJECT_MAPPER.readTree(jsonString);
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 对象转换（通过JSON序列化和反序列化）
     *
     * @param source 源对象
     * @param targetClass 目标类型
     * @param <T> 泛型
     * @return 目标对象
     */
    public static <T> T convertValue(Object source, Class<T> targetClass) {
        if (source == null || targetClass == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.convertValue(source, targetClass);
        } catch (Exception e) {
            log.error("对象转换失败", e);
            return null;
        }
    }

    /**
     * 对象转换（通过JSON序列化和反序列化，支持泛型）
     *
     * @param source 源对象
     * @param typeReference 类型引用
     * @param <T> 泛型
     * @return 目标对象
     */
    public static <T> T convertValue(Object source, TypeReference<T> typeReference) {
        if (source == null || typeReference == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.convertValue(source, typeReference);
        } catch (Exception e) {
            log.error("对象转换失败", e);
            return null;
        }
    }

    /**
     * 获取ObjectMapper实例
     *
     * @return ObjectMapper
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }
}
