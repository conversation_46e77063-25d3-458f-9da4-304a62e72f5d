package com.rega.erp.common.core.exception;

import com.rega.erp.common.core.constant.HttpStatus;

/**
 * 系统异常
 *
 * <AUTHOR>
 */
public class SystemException extends BaseException {
    
    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public SystemException(String message) {
        super(HttpStatus.ERROR, message);
    }

    /**
     * 构造函数
     *
     * @param code    错误码
     * @param message 错误消息
     */
    public SystemException(int code, String message) {
        super(code, message);
    }

    /**
     * 构造函数
     *
     * @param code    错误码
     * @param message 错误消息
     * @param cause   异常
     */
    public SystemException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }
} 