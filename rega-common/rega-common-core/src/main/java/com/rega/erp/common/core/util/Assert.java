package com.rega.erp.common.core.util;

import com.rega.erp.common.core.constant.ErrorCode;
import com.rega.erp.common.core.constant.ErrorMessage;
import com.rega.erp.common.core.exception.BusinessException;

import java.util.Collection;
import java.util.Map;

/**
 * 断言工具类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class Assert {

    /**
     * 断言表达式为真
     * 
     * @param expression 表达式
     * @param message 错误消息
     */
    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_ERROR, message);
        }
    }

    /**
     * 断言表达式为真
     * 
     * @param expression 表达式
     * @param code 错误码
     * @param message 错误消息
     */
    public static void isTrue(boolean expression, int code, String message) {
        if (!expression) {
            throw new BusinessException(code, message);
        }
    }

    /**
     * 断言表达式为假
     * 
     * @param expression 表达式
     * @param message 错误消息
     */
    public static void isFalse(boolean expression, String message) {
        if (expression) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_ERROR, message);
        }
    }

    /**
     * 断言表达式为假
     * 
     * @param expression 表达式
     * @param code 错误码
     * @param message 错误消息
     */
    public static void isFalse(boolean expression, int code, String message) {
        if (expression) {
            throw new BusinessException(code, message);
        }
    }

    /**
     * 断言对象不为空
     * 
     * @param object 对象
     * @param message 错误消息
     */
    public static void notNull(Object object, String message) {
        if (object == null) {
            throw new BusinessException(ErrorCode.PARAM_MISSING_ERROR, message);
        }
    }

    /**
     * 断言对象不为空
     * 
     * @param object 对象
     * @param code 错误码
     * @param message 错误消息
     */
    public static void notNull(Object object, int code, String message) {
        if (object == null) {
            throw new BusinessException(code, message);
        }
    }

    /**
     * 断言对象为空
     * 
     * @param object 对象
     * @param message 错误消息
     */
    public static void isNull(Object object, String message) {
        if (object != null) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_ERROR, message);
        }
    }

    /**
     * 断言字符串不为空
     * 
     * @param text 字符串
     * @param message 错误消息
     */
    public static void hasText(String text, String message) {
        if (StringUtils.isEmpty(text)) {
            throw new BusinessException(ErrorCode.PARAM_MISSING_ERROR, message);
        }
    }

    /**
     * 断言字符串不为空
     * 
     * @param text 字符串
     * @param code 错误码
     * @param message 错误消息
     */
    public static void hasText(String text, int code, String message) {
        if (StringUtils.isEmpty(text)) {
            throw new BusinessException(code, message);
        }
    }

    /**
     * 断言字符串为空
     * 
     * @param text 字符串
     * @param message 错误消息
     */
    public static void noText(String text, String message) {
        if (StringUtils.isNotEmpty(text)) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_ERROR, message);
        }
    }

    /**
     * 断言集合不为空
     * 
     * @param collection 集合
     * @param message 错误消息
     */
    public static void notEmpty(Collection<?> collection, String message) {
        if (collection == null || collection.isEmpty()) {
            throw new BusinessException(ErrorCode.PARAM_MISSING_ERROR, message);
        }
    }

    /**
     * 断言集合不为空
     * 
     * @param collection 集合
     * @param code 错误码
     * @param message 错误消息
     */
    public static void notEmpty(Collection<?> collection, int code, String message) {
        if (collection == null || collection.isEmpty()) {
            throw new BusinessException(code, message);
        }
    }

    /**
     * 断言Map不为空
     * 
     * @param map Map对象
     * @param message 错误消息
     */
    public static void notEmpty(Map<?, ?> map, String message) {
        if (map == null || map.isEmpty()) {
            throw new BusinessException(ErrorCode.PARAM_MISSING_ERROR, message);
        }
    }

    /**
     * 断言数组不为空
     * 
     * @param array 数组
     * @param message 错误消息
     */
    public static void notEmpty(Object[] array, String message) {
        if (array == null || array.length == 0) {
            throw new BusinessException(ErrorCode.PARAM_MISSING_ERROR, message);
        }
    }

    /**
     * 断言ID有效
     *
     * @param id ID
     * @param message 错误消息
     */
    public static void validId(Long id, String message) {
        if (id == null || id <= 0) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_ERROR, message);
        }
    }

    /**
     * 断言用户存在
     * 
     * @param user 用户对象
     */
    public static void userExists(Object user) {
        notNull(user, ErrorMessage.USER_NOT_FOUND);
    }

    /**
     * 断言租户存在
     * 
     * @param tenant 租户对象
     */
    public static void tenantExists(Object tenant) {
        notNull(tenant, ErrorMessage.TENANT_NOT_FOUND);
    }

    /**
     * 断言数据存在
     * 
     * @param data 数据对象
     */
    public static void dataExists(Object data) {
        notNull(data, ErrorMessage.DATA_NOT_FOUND);
    }

    /**
     * 断言数据不存在
     * 
     * @param data 数据对象
     */
    public static void dataNotExists(Object data) {
        isNull(data, ErrorMessage.DATA_EXISTS);
    }

    /**
     * 断言权限足够
     * 
     * @param hasPermission 是否有权限
     */
    public static void hasPermission(boolean hasPermission) {
        isTrue(hasPermission, ErrorCode.PERMISSION_DENIED, ErrorMessage.PERMISSION_DENIED);
    }

    /**
     * 断言操作允许
     * 
     * @param allowed 是否允许
     */
    public static void operationAllowed(boolean allowed) {
        isTrue(allowed, ErrorCode.OPERATION_NOT_ALLOWED, ErrorMessage.OPERATION_NOT_ALLOWED);
    }

    /**
     * 断言状态正确
     * 
     * @param validStatus 状态是否正确
     * @param message 错误消息
     */
    public static void validStatus(boolean validStatus, String message) {
        isTrue(validStatus, ErrorCode.DATA_STATUS_ERROR, message);
    }
}
