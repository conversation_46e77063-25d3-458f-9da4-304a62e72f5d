package com.rega.erp.common.core.exception;

/**
 * 异常工具类
 *
 * <AUTHOR>
 */
public class ExceptionUtils {

    /**
     * 私有构造函数，防止实例化
     */
    private ExceptionUtils() {
    }

    /**
     * 抛出业务异常
     *
     * @param message 异常消息
     */
    public static void throwBusinessException(String message) {
        throw new BusinessException(message);
    }

    /**
     * 抛出业务异常
     *
     * @param code    错误码
     * @param message 异常消息
     */
    public static void throwBusinessException(Integer code, String message) {
        throw new BusinessException(code, message);
    }
    
    /**
     * 抛出业务异常
     *
     * @param code    错误码
     * @param message 异常消息
     */
    public static void throwBusinessException(int code, String message) {
        throw new BusinessException(code, message);
    }

    /**
     * 抛出系统异常
     *
     * @param message 异常消息
     */
    public static void throwSystemException(String message) {
        throw new SystemException(message);
    }

    /**
     * 抛出系统异常
     *
     * @param code    错误码
     * @param message 异常消息
     */
    public static void throwSystemException(Integer code, String message) {
        throw new SystemException(code, message);
    }
    
    /**
     * 抛出系统异常
     *
     * @param code    错误码
     * @param message 异常消息
     */
    public static void throwSystemException(int code, String message) {
        throw new SystemException(code, message);
    }
}
