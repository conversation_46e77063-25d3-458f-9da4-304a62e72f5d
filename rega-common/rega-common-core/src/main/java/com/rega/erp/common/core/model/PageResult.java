package com.rega.erp.common.core.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 分页响应结果
 *
 * @param <T> 数据类型
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private int code = 200;

    /**
     * 消息
     */
    private String message = "查询成功";

    /**
     * 数据列表
     */
    private List<T> records;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 当前页码
     */
    private long current;

    /**
     * 每页大小
     */
    private long size;

    /**
     * 总页数
     */
    private long pages;

    /**
     * 时间戳
     */
    private long timestamp = System.currentTimeMillis();

    /**
     * 构造函数
     *
     * @param records 数据列表
     * @param total   总记录数
     * @param current 当前页码
     * @param size    每页大小
     */
    public PageResult(List<T> records, long total, long current, long size) {
        this.records = records;
        this.total = total;
        this.current = current;
        this.size = size;
        this.pages = size > 0 ? (total + size - 1) / size : 0;
    }

    /**
     * 成功分页响应
     *
     * @param records 数据列表
     * @param total   总记录数
     * @param current 当前页码
     * @param size    每页大小
     * @param <T>     数据类型
     * @return 分页响应对象
     */
    public static <T> PageResult<T> ok(List<T> records, long total, long current, long size) {
        return new PageResult<>(records, total, current, size);
    }

    /**
     * 成功分页响应
     *
     * @param records 数据列表
     * @param total   总记录数
     * @param current 当前页码
     * @param size    每页大小
     * @param message 消息
     * @param <T>     数据类型
     * @return 分页响应对象
     */
    public static <T> PageResult<T> ok(List<T> records, long total, long current, long size, String message) {
        PageResult<T> result = new PageResult<>(records, total, current, size);
        result.setMessage(message);
        return result;
    }

    /**
     * 空分页响应
     *
     * @param current 当前页码
     * @param size    每页大小
     * @param <T>     数据类型
     * @return 分页响应对象
     */
    public static <T> PageResult<T> empty(long current, long size) {
        return new PageResult<>(List.of(), 0, current, size);
    }

    /**
     * 失败分页响应
     *
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 分页响应对象
     */
    public static <T> PageResult<T> fail(String message) {
        PageResult<T> result = new PageResult<>();
        result.setCode(500);
        result.setMessage(message);
        result.setRecords(List.of());
        result.setTotal(0);
        result.setCurrent(1);
        result.setSize(10);
        result.setPages(0);
        return result;
    }

    /**
     * 失败分页响应
     *
     * @param code    错误码
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 分页响应对象
     */
    public static <T> PageResult<T> fail(int code, String message) {
        PageResult<T> result = fail(message);
        result.setCode(code);
        return result;
    }

    /**
     * 是否有下一页
     *
     * @return true-有下一页，false-没有下一页
     */
    public boolean hasNext() {
        return current < pages;
    }

    /**
     * 是否有上一页
     *
     * @return true-有上一页，false-没有上一页
     */
    public boolean hasPrevious() {
        return current > 1;
    }

    /**
     * 是否为空
     *
     * @return true-空，false-非空
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }

    /**
     * 获取记录数量
     *
     * @return 记录数量
     */
    public int getRecordCount() {
        return records == null ? 0 : records.size();
    }
}
