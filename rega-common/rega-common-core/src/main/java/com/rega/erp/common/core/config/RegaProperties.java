package com.rega.erp.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Rega系统配置属性
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "rega")
public class RegaProperties {

    /**
     * 项目名称
     */
    private String name = "RegaWebERP";

    /**
     * 版本
     */
    private String version = "1.0.0";

    /**
     * 版权年份
     */
    private String copyrightYear = "2024";

    /**
     * 实例演示开关
     */
    private boolean demoEnabled = false;

    /**
     * 上传路径
     */
    private static String profile;

    /**
     * 获取地址开关
     */
    private static boolean addressEnabled = true;

    /**
     * 验证码类型
     */
    private static String captchaType = "math";

    public static String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        RegaProperties.profile = profile;
    }

    public static boolean isAddressEnabled() {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled) {
        RegaProperties.addressEnabled = addressEnabled;
    }

    public static String getCaptchaType() {
        return captchaType;
    }

    public void setCaptchaType(String captchaType) {
        RegaProperties.captchaType = captchaType;
    }

    /**
     * 获取导入上传路径
     */
    public static String getImportPath() {
        return getProfile() + "/import";
    }

    /**
     * 获取头像上传路径
     */
    public static String getAvatarPath() {
        return getProfile() + "/avatar";
    }

    /**
     * 获取下载路径
     */
    public static String getDownloadPath() {
        return getProfile() + "/download/";
    }

    /**
     * 获取上传路径
     */
    public static String getUploadPath() {
        return getProfile() + "/upload";
    }
}
