package com.rega.erp.common.core.config;

import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.DefaultIdGeneratorProvider;
import me.ahoo.cosid.provider.IdGeneratorProvider;
import me.ahoo.cosid.snowflake.DefaultSnowflakeId;
import me.ahoo.cosid.snowflake.SnowflakeId;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ID生成器配置
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnClass(IdGeneratorProvider.class)
public class IdGeneratorConfig {

    /**
     * 默认雪花ID生成器
     */
    @Bean
    @ConditionalOnMissingBean
    public SnowflakeId snowflakeId() {
        // 使用默认配置创建雪花ID生成器
        // 机器ID使用进程ID的低10位
        int machineId = (int) (ProcessHandle.current().pid() & 0x3FF);
        return new DefaultSnowflakeId(machineId);
    }

    /**
     * ID生成器提供者
     */
    @Bean
    @ConditionalOnMissingBean
    public IdGeneratorProvider idGeneratorProvider(SnowflakeId snowflakeId) {
        DefaultIdGeneratorProvider provider = new DefaultIdGeneratorProvider();
        provider.setShare(snowflakeId);
        return provider;
    }
}
