package com.rega.erp.common.core.util;

import me.ahoo.cosid.provider.IdProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * ID 生成工具类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
public class IdUtils {

    private static IdProvider idProvider;

    @Autowired(required = false)
    public void setIdProvider(IdProvider idProvider) {
        IdUtils.idProvider = idProvider;
    }

    /**
     * 生成雪花算法ID
     * 
     * @return 雪花算法ID
     */
    public static Long nextId() {
        if (idProvider != null) {
            return idProvider.getShare().generate();
        }
        // 如果 CosID 不可用，使用时间戳 + 随机数作为备选方案
        return System.currentTimeMillis() * 1000 + (long) (Math.random() * 1000);
    }

    /**
     * 生成雪花算法ID字符串
     * 
     * @return 雪花算法ID字符串
     */
    public static String nextIdStr() {
        return String.valueOf(nextId());
    }

    /**
     * 生成UUID
     * 
     * @return UUID字符串
     */
    public static String uuid() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成简化UUID（去掉横线）
     * 
     * @return 简化UUID字符串
     */
    public static String simpleUuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成指定长度的随机数字字符串
     * 
     * @param length 长度
     * @return 随机数字字符串
     */
    public static String randomNumeric(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append((int) (Math.random() * 10));
        }
        return sb.toString();
    }

    /**
     * 生成指定长度的随机字母数字字符串
     * 
     * @param length 长度
     * @return 随机字母数字字符串
     */
    public static String randomAlphanumeric(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt((int) (Math.random() * chars.length())));
        }
        return sb.toString();
    }

    /**
     * 生成业务编码
     * 格式：前缀 + 日期 + 序号
     * 
     * @param prefix 前缀
     * @param dateFormat 日期格式（如：yyyyMMdd）
     * @param sequenceLength 序号长度
     * @return 业务编码
     */
    public static String generateBusinessCode(String prefix, String dateFormat, int sequenceLength) {
        String date = DateUtils.format(DateUtils.now(), dateFormat);
        String sequence = randomNumeric(sequenceLength);
        return prefix + date + sequence;
    }

    /**
     * 生成订单号
     * 格式：ORDER + yyyyMMddHHmmss + 4位随机数
     * 
     * @return 订单号
     */
    public static String generateOrderNo() {
        return generateBusinessCode("ORDER", "yyyyMMddHHmmss", 4);
    }

    /**
     * 生成流水号
     * 格式：SN + yyyyMMdd + 6位随机数
     * 
     * @return 流水号
     */
    public static String generateSerialNo() {
        return generateBusinessCode("SN", "yyyyMMdd", 6);
    }

    /**
     * 检查ID是否有效
     * 
     * @param id ID
     * @return true-有效，false-无效
     */
    public static boolean isValidId(Long id) {
        return id != null && id > 0;
    }

    /**
     * 检查ID字符串是否有效
     * 
     * @param idStr ID字符串
     * @return true-有效，false-无效
     */
    public static boolean isValidIdStr(String idStr) {
        if (StringUtils.isEmpty(idStr)) {
            return false;
        }
        try {
            Long id = Long.parseLong(idStr);
            return isValidId(id);
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 检查UUID是否有效
     * 
     * @param uuid UUID字符串
     * @return true-有效，false-无效
     */
    public static boolean isValidUuid(String uuid) {
        if (StringUtils.isEmpty(uuid)) {
            return false;
        }
        try {
            UUID.fromString(uuid);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
