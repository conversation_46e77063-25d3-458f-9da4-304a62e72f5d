package com.rega.erp.common.core.util;

import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

/**
 * ID生成工具类
 *
 * <AUTHOR>
 */
public class IdUtils {

    /**
     * 获取随机UUID
     *
     * @return 随机UUID
     */
    public static String randomUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线
     *
     * @return 简化的UUID
     */
    public static String simpleUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 获取随机UUID，使用ThreadLocalRandom
     *
     * @return 随机UUID
     */
    public static String fastUUID() {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        return new UUID(random.nextLong(), random.nextLong()).toString();
    }

    /**
     * 简化的UUID，去掉了横线，使用ThreadLocalRandom
     *
     * @return 简化的UUID
     */
    public static String fastSimpleUUID() {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        return new UUID(random.nextLong(), random.nextLong()).toString().replaceAll("-", "");
    }

    /**
     * 生成雪花ID（使用CosID）
     *
     * @return 雪花ID
     */
    public static long snowflakeId() {
        try {
            return IdGeneratorProvider.SHARE.getShare().generate();
        } catch (Exception e) {
            // 如果CosID不可用，使用时间戳+随机数作为fallback
            return System.currentTimeMillis() * 1000000 + ThreadLocalRandom.current().nextInt(1000000);
        }
    }

    /**
     * 生成雪花ID字符串（使用CosID）
     *
     * @return 雪花ID字符串
     */
    public static String snowflakeIdString() {
        return String.valueOf(snowflakeId());
    }

    /**
     * 生成指定长度的随机数字字符串
     *
     * @param length 长度
     * @return 随机数字字符串
     */
    public static String randomNumeric(int length) {
        StringBuilder sb = new StringBuilder();
        SecureRandom random = new SecureRandom();
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }

    /**
     * 生成指定长度的随机字母字符串
     *
     * @param length 长度
     * @return 随机字母字符串
     */
    public static String randomAlphabetic(int length) {
        StringBuilder sb = new StringBuilder();
        SecureRandom random = new SecureRandom();
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }

    /**
     * 生成指定长度的随机字母数字字符串
     *
     * @param length 长度
     * @return 随机字母数字字符串
     */
    public static String randomAlphanumeric(int length) {
        StringBuilder sb = new StringBuilder();
        SecureRandom random = new SecureRandom();
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }

    /**
     * 生成指定长度的随机字符串（包含特殊字符）
     *
     * @param length 长度
     * @return 随机字符串
     */
    public static String randomString(int length) {
        StringBuilder sb = new StringBuilder();
        SecureRandom random = new SecureRandom();
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()";
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }

    /**
     * 生成订单号
     * 格式：yyyyMMddHHmmss + 6位随机数
     *
     * @return 订单号
     */
    public static String generateOrderNo() {
        return DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS) + randomNumeric(6);
    }

    /**
     * 生成订单号（带前缀）
     * 格式：前缀 + yyyyMMddHHmmss + 6位随机数
     *
     * @param prefix 前缀
     * @return 订单号
     */
    public static String generateOrderNo(String prefix) {
        return prefix + DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS) + randomNumeric(6);
    }

    /**
     * 生成流水号
     * 格式：yyyyMMdd + 8位随机数
     *
     * @return 流水号
     */
    public static String generateSerialNo() {
        return DateUtils.dateTime() + randomNumeric(8);
    }

    /**
     * 生成流水号（带前缀）
     * 格式：前缀 + yyyyMMdd + 8位随机数
     *
     * @param prefix 前缀
     * @return 流水号
     */
    public static String generateSerialNo(String prefix) {
        return prefix + DateUtils.dateTime() + randomNumeric(8);
    }

    /**
     * 生成验证码
     *
     * @param length 长度
     * @return 验证码
     */
    public static String generateVerifyCode(int length) {
        return randomNumeric(length);
    }

    /**
     * 生成6位数字验证码
     *
     * @return 验证码
     */
    public static String generateVerifyCode() {
        return generateVerifyCode(6);
    }

    /**
     * 生成邀请码
     * 8位字母数字组合
     *
     * @return 邀请码
     */
    public static String generateInviteCode() {
        return randomAlphanumeric(8).toUpperCase();
    }

    /**
     * 生成临时密码
     * 8位字母数字组合
     *
     * @return 临时密码
     */
    public static String generateTempPassword() {
        return randomAlphanumeric(8);
    }

    /**
     * 生成API密钥
     * 32位字母数字组合
     *
     * @return API密钥
     */
    public static String generateApiKey() {
        return randomAlphanumeric(32);
    }

    /**
     * 生成密钥
     * 指定长度的字母数字组合
     *
     * @param length 长度
     * @return 密钥
     */
    public static String generateSecret(int length) {
        return randomAlphanumeric(length);
    }

    /**
     * 生成文件名
     * 使用UUID作为文件名，保留原文件扩展名
     *
     * @param originalFilename 原文件名
     * @return 新文件名
     */
    public static String generateFileName(String originalFilename) {
        if (StringUtils.isEmpty(originalFilename)) {
            return simpleUUID();
        }
        String extension = FileUtils.getExtension(originalFilename);
        if (StringUtils.isEmpty(extension)) {
            return simpleUUID();
        }
        return simpleUUID() + "." + extension;
    }

    /**
     * 生成带时间戳的文件名
     *
     * @param originalFilename 原文件名
     * @return 新文件名
     */
    public static String generateFileNameWithTimestamp(String originalFilename) {
        if (StringUtils.isEmpty(originalFilename)) {
            return DateUtils.dateTimeNow();
        }
        String extension = FileUtils.getExtension(originalFilename);
        String nameWithoutExt = FileUtils.getNameWithoutExtension(originalFilename);
        if (StringUtils.isEmpty(extension)) {
            return nameWithoutExt + "_" + DateUtils.dateTimeNow();
        }
        return nameWithoutExt + "_" + DateUtils.dateTimeNow() + "." + extension;
    }

    /**
     * 生成随机长整型ID
     *
     * @return 随机长整型ID
     */
    public static long randomLong() {
        return ThreadLocalRandom.current().nextLong(1000000000000000L, 9999999999999999L);
    }

    /**
     * 生成随机整型ID
     *
     * @return 随机整型ID
     */
    public static int randomInt() {
        return ThreadLocalRandom.current().nextInt(100000000, 999999999);
    }

    /**
     * 生成指定范围内的随机整数
     *
     * @param min 最小值（包含）
     * @param max 最大值（不包含）
     * @return 随机整数
     */
    public static int randomInt(int min, int max) {
        return ThreadLocalRandom.current().nextInt(min, max);
    }

    /**
     * 生成指定范围内的随机长整数
     *
     * @param min 最小值（包含）
     * @param max 最大值（不包含）
     * @return 随机长整数
     */
    public static long randomLong(long min, long max) {
        return ThreadLocalRandom.current().nextLong(min, max);
    }
}
