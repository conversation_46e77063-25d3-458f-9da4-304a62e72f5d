package com.rega.erp.common.core.config;

import com.rega.erp.common.core.handler.GlobalExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * 核心模块自动配置
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Configuration
@Import({
    GlobalExceptionHandler.class
})
public class CoreAutoConfiguration {

    /**
     * 全局异常处理器
     */
    @Bean
    @ConditionalOnMissingBean
    public GlobalExceptionHandler globalExceptionHandler() {
        log.info("RegaWebERP Core Module: GlobalExceptionHandler initialized");
        return new GlobalExceptionHandler();
    }

    /**
     * 初始化日志
     */
    public CoreAutoConfiguration() {
        log.info("RegaWebERP Core Module Auto Configuration Initialized");
    }
}
