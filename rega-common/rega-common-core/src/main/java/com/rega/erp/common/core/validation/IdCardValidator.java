package com.rega.erp.common.core.validation;

import com.rega.erp.common.core.util.ValidatorUtils;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * 身份证号验证器
 *
 * <AUTHOR>
 */
public class IdCardValidator implements ConstraintValidator<IdCard, String> {

    @Override
    public void initialize(IdCard constraintAnnotation) {
        // 初始化方法，可以获取注解参数
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 如果值为空，则不进行验证（可以配合@NotNull使用）
        if (value == null || value.trim().isEmpty()) {
            return true;
        }
        return ValidatorUtils.isIDCard(value);
    }
}
