package com.rega.erp.common.core.util;

/**
 * SQL 工具类 - 处理数据库方言差异
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class SqlUtils {

    /**
     * 数据库类型枚举
     */
    public enum DatabaseType {
        MYSQL, POSTGRESQL, ORACLE, SQL_SERVER
    }

    /**
     * 当前数据库类型（默认 PostgreSQL）
     */
    private static DatabaseType currentDatabaseType = DatabaseType.POSTGRESQL;

    /**
     * 设置当前数据库类型
     * 
     * @param databaseType 数据库类型
     */
    public static void setCurrentDatabaseType(DatabaseType databaseType) {
        currentDatabaseType = databaseType;
    }

    /**
     * 获取当前数据库类型
     * 
     * @return 数据库类型
     */
    public static DatabaseType getCurrentDatabaseType() {
        return currentDatabaseType;
    }

    /**
     * 生成 FIND_IN_SET 兼容 SQL
     * MySQL: find_in_set(value, set)
     * PostgreSQL: set LIKE '%,value,%' OR set LIKE 'value,%' OR set LIKE '%,value' OR set = 'value'
     * 
     * @param value 要查找的值
     * @param setColumn 集合字段名
     * @return 兼容的 SQL 条件
     */
    public static String findInSet(String value, String setColumn) {
        switch (currentDatabaseType) {
            case MYSQL:
                return String.format("find_in_set('%s', %s)", value, setColumn);
            case POSTGRESQL:
                return String.format(
                    "(%s LIKE '%%,%s,%%' OR %s LIKE '%s,%%' OR %s LIKE '%%,%s' OR %s = '%s')",
                    setColumn, value, setColumn, value, setColumn, value, setColumn, value
                );
            case ORACLE:
                return String.format(
                    "(INSTR(',' || %s || ',', ',%s,') > 0)",
                    setColumn, value
                );
            case SQL_SERVER:
                return String.format(
                    "(CHARINDEX(',%s,', ',' + %s + ',') > 0)",
                    value, setColumn
                );
            default:
                // 默认使用 PostgreSQL 语法
                return String.format(
                    "(%s LIKE '%%,%s,%%' OR %s LIKE '%s,%%' OR %s LIKE '%%,%s' OR %s = '%s')",
                    setColumn, value, setColumn, value, setColumn, value, setColumn, value
                );
        }
    }

    /**
     * 生成 FIND_IN_SET 兼容 SQL（数值类型）
     * 
     * @param value 要查找的数值
     * @param setColumn 集合字段名
     * @return 兼容的 SQL 条件
     */
    public static String findInSet(Long value, String setColumn) {
        return findInSet(String.valueOf(value), setColumn);
    }

    /**
     * 生成 FIND_IN_SET 兼容 SQL（整数类型）
     * 
     * @param value 要查找的整数
     * @param setColumn 集合字段名
     * @return 兼容的 SQL 条件
     */
    public static String findInSet(Integer value, String setColumn) {
        return findInSet(String.valueOf(value), setColumn);
    }

    /**
     * 生成限制查询条数的 SQL
     * MySQL/PostgreSQL: LIMIT n
     * Oracle: ROWNUM <= n
     * SQL Server: TOP n
     * 
     * @param limit 限制条数
     * @return 兼容的 SQL
     */
    public static String limit(int limit) {
        switch (currentDatabaseType) {
            case MYSQL:
            case POSTGRESQL:
                return String.format("LIMIT %d", limit);
            case ORACLE:
                return String.format("AND ROWNUM <= %d", limit);
            case SQL_SERVER:
                return String.format("TOP %d", limit);
            default:
                return String.format("LIMIT %d", limit);
        }
    }

    /**
     * 生成分页查询的 SQL
     * 
     * @param offset 偏移量
     * @param limit 限制条数
     * @return 兼容的 SQL
     */
    public static String paginate(int offset, int limit) {
        switch (currentDatabaseType) {
            case MYSQL:
            case POSTGRESQL:
                return String.format("LIMIT %d OFFSET %d", limit, offset);
            case ORACLE:
                return String.format("OFFSET %d ROWS FETCH NEXT %d ROWS ONLY", offset, limit);
            case SQL_SERVER:
                return String.format("OFFSET %d ROWS FETCH NEXT %d ROWS ONLY", offset, limit);
            default:
                return String.format("LIMIT %d OFFSET %d", limit, offset);
        }
    }

    /**
     * 生成字符串连接的 SQL
     * MySQL: CONCAT(str1, str2, ...)
     * PostgreSQL: str1 || str2 || ...
     * Oracle: str1 || str2 || ...
     * SQL Server: str1 + str2 + ...
     * 
     * @param columns 要连接的字段
     * @return 兼容的 SQL
     */
    public static String concat(String... columns) {
        switch (currentDatabaseType) {
            case MYSQL:
                return String.format("CONCAT(%s)", String.join(", ", columns));
            case POSTGRESQL:
            case ORACLE:
                return String.join(" || ", columns);
            case SQL_SERVER:
                return String.join(" + ", columns);
            default:
                return String.join(" || ", columns);
        }
    }

    /**
     * 生成日期格式化的 SQL
     * 
     * @param dateColumn 日期字段
     * @param format 格式字符串
     * @return 兼容的 SQL
     */
    public static String dateFormat(String dateColumn, String format) {
        switch (currentDatabaseType) {
            case MYSQL:
                return String.format("DATE_FORMAT(%s, '%s')", dateColumn, format);
            case POSTGRESQL:
                return String.format("TO_CHAR(%s, '%s')", dateColumn, convertDateFormat(format, DatabaseType.POSTGRESQL));
            case ORACLE:
                return String.format("TO_CHAR(%s, '%s')", dateColumn, convertDateFormat(format, DatabaseType.ORACLE));
            case SQL_SERVER:
                return String.format("FORMAT(%s, '%s')", dateColumn, convertDateFormat(format, DatabaseType.SQL_SERVER));
            default:
                return String.format("TO_CHAR(%s, '%s')", dateColumn, convertDateFormat(format, DatabaseType.POSTGRESQL));
        }
    }

    /**
     * 转换日期格式字符串
     * 
     * @param mysqlFormat MySQL 格式
     * @param targetType 目标数据库类型
     * @return 转换后的格式
     */
    private static String convertDateFormat(String mysqlFormat, DatabaseType targetType) {
        switch (targetType) {
            case POSTGRESQL:
                return mysqlFormat
                    .replace("%Y", "YYYY")
                    .replace("%m", "MM")
                    .replace("%d", "DD")
                    .replace("%H", "HH24")
                    .replace("%i", "MI")
                    .replace("%s", "SS");
            case ORACLE:
                return mysqlFormat
                    .replace("%Y", "YYYY")
                    .replace("%m", "MM")
                    .replace("%d", "DD")
                    .replace("%H", "HH24")
                    .replace("%i", "MI")
                    .replace("%s", "SS");
            case SQL_SERVER:
                return mysqlFormat
                    .replace("%Y", "yyyy")
                    .replace("%m", "MM")
                    .replace("%d", "dd")
                    .replace("%H", "HH")
                    .replace("%i", "mm")
                    .replace("%s", "ss");
            default:
                return mysqlFormat;
        }
    }

    /**
     * 转义 SQL 特殊字符
     * 
     * @param value 要转义的值
     * @return 转义后的值
     */
    public static String escapeSql(String value) {
        if (StringUtils.isEmpty(value)) {
            return value;
        }
        return value.replace("'", "''")
                   .replace("\\", "\\\\")
                   .replace("%", "\\%")
                   .replace("_", "\\_");
    }

    /**
     * 构建 LIKE 查询条件
     * 
     * @param column 字段名
     * @param value 查询值
     * @param matchType 匹配类型：START(开头匹配)、END(结尾匹配)、ANYWHERE(任意位置匹配)
     * @return LIKE 条件
     */
    public static String like(String column, String value, LikeMatchType matchType) {
        if (StringUtils.isEmpty(value)) {
            return "";
        }
        
        String escapedValue = escapeSql(value);
        switch (matchType) {
            case START:
                return String.format("%s LIKE '%s%%'", column, escapedValue);
            case END:
                return String.format("%s LIKE '%%%s'", column, escapedValue);
            case ANYWHERE:
                return String.format("%s LIKE '%%%s%%'", column, escapedValue);
            default:
                return String.format("%s LIKE '%%%s%%'", column, escapedValue);
        }
    }

    /**
     * LIKE 匹配类型
     */
    public enum LikeMatchType {
        START, END, ANYWHERE
    }
}
