package com.rega.erp.common.core.util;

import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 日期工具类测试
 *
 * <AUTHOR>
 */
class DateUtilsTest {

    @Test
    void testGetNow() {
        Date now = DateUtils.getNow();
        assertNotNull(now);
        assertTrue(now.getTime() > 0);
    }

    @Test
    void testGetDate() {
        String date = DateUtils.getDate();
        assertNotNull(date);
        assertTrue(date.matches("\\d{4}-\\d{2}-\\d{2}"));
    }

    @Test
    void testGetTime() {
        String time = DateUtils.getTime();
        assertNotNull(time);
        assertTrue(time.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
    }

    @Test
    void testDateTimeNow() {
        String dateTime = DateUtils.dateTimeNow();
        assertNotNull(dateTime);
        assertTrue(dateTime.matches("\\d{14}"));
    }

    @Test
    void testDatePath() {
        String datePath = DateUtils.datePath();
        assertNotNull(datePath);
        assertTrue(datePath.matches("\\d{4}/\\d{2}/\\d{2}"));
    }

    @Test
    void testDateTime() {
        String dateTime = DateUtils.dateTime();
        assertNotNull(dateTime);
        assertTrue(dateTime.matches("\\d{8}"));
    }

    @Test
    void testParseDate() {
        Date date = DateUtils.parseDate("2023-12-25");
        assertNotNull(date);
        
        Date date2 = DateUtils.parseDate("2023-12-25 10:30:45");
        assertNotNull(date2);
        
        Date date3 = DateUtils.parseDate("invalid");
        assertNull(date3);
    }

    @Test
    void testDifferentDaysByMillisecond() {
        Date date1 = DateUtils.parseDate("2023-12-25");
        Date date2 = DateUtils.parseDate("2023-12-27");
        
        int days = DateUtils.differentDaysByMillisecond(date1, date2);
        assertEquals(2, days);
    }

    @Test
    void testLocalDateTimeConversion() {
        LocalDateTime now = LocalDateTime.now();
        Date date = DateUtils.toDate(now);
        LocalDateTime converted = DateUtils.toLocalDateTime(date);
        
        assertNotNull(date);
        assertNotNull(converted);
        assertEquals(now.getYear(), converted.getYear());
        assertEquals(now.getMonth(), converted.getMonth());
        assertEquals(now.getDayOfMonth(), converted.getDayOfMonth());
    }

    @Test
    void testLocalDateConversion() {
        LocalDate today = LocalDate.now();
        Date date = DateUtils.toDate(today);
        LocalDate converted = DateUtils.toLocalDate(date);
        
        assertNotNull(date);
        assertNotNull(converted);
        assertEquals(today, converted);
    }

    @Test
    void testFormatLocalDateTime() {
        LocalDateTime dateTime = LocalDateTime.of(2023, 12, 25, 10, 30, 45);
        String formatted = DateUtils.formatLocalDateTime(dateTime, "yyyy-MM-dd HH:mm:ss");
        assertEquals("2023-12-25 10:30:45", formatted);
    }

    @Test
    void testFormatLocalDate() {
        LocalDate date = LocalDate.of(2023, 12, 25);
        String formatted = DateUtils.formatLocalDate(date, "yyyy-MM-dd");
        assertEquals("2023-12-25", formatted);
    }

    @Test
    void testParseLocalDateTime() {
        LocalDateTime dateTime = DateUtils.parseLocalDateTime("2023-12-25 10:30:45", "yyyy-MM-dd HH:mm:ss");
        assertNotNull(dateTime);
        assertEquals(2023, dateTime.getYear());
        assertEquals(12, dateTime.getMonthValue());
        assertEquals(25, dateTime.getDayOfMonth());
        assertEquals(10, dateTime.getHour());
        assertEquals(30, dateTime.getMinute());
        assertEquals(45, dateTime.getSecond());
    }

    @Test
    void testParseLocalDate() {
        LocalDate date = DateUtils.parseLocalDate("2023-12-25", "yyyy-MM-dd");
        assertNotNull(date);
        assertEquals(2023, date.getYear());
        assertEquals(12, date.getMonthValue());
        assertEquals(25, date.getDayOfMonth());
    }

    @Test
    void testGetCurrentTimestamp() {
        long timestamp = DateUtils.getCurrentTimestamp();
        assertTrue(timestamp > 0);
    }

    @Test
    void testGetCurrentTimestampSecond() {
        long timestamp = DateUtils.getCurrentTimestampSecond();
        assertTrue(timestamp > 0);
    }

    @Test
    void testTimestampToDate() {
        long timestamp = System.currentTimeMillis();
        Date date = DateUtils.timestampToDate(timestamp);
        assertNotNull(date);
        assertEquals(timestamp, date.getTime());
    }

    @Test
    void testGetStartOfDay() {
        LocalDate date = LocalDate.of(2023, 12, 25);
        LocalDateTime startOfDay = DateUtils.getStartOfDay(date);
        assertNotNull(startOfDay);
        assertEquals(0, startOfDay.getHour());
        assertEquals(0, startOfDay.getMinute());
        assertEquals(0, startOfDay.getSecond());
    }

    @Test
    void testGetEndOfDay() {
        LocalDate date = LocalDate.of(2023, 12, 25);
        LocalDateTime endOfDay = DateUtils.getEndOfDay(date);
        assertNotNull(endOfDay);
        assertEquals(23, endOfDay.getHour());
        assertEquals(59, endOfDay.getMinute());
        assertEquals(59, endOfDay.getSecond());
    }

    @Test
    void testDaysBetween() {
        LocalDate startDate = LocalDate.of(2023, 12, 25);
        LocalDate endDate = LocalDate.of(2023, 12, 27);
        long days = DateUtils.daysBetween(startDate, endDate);
        assertEquals(2, days);
    }

    @Test
    void testHoursBetween() {
        LocalDateTime startDateTime = LocalDateTime.of(2023, 12, 25, 10, 0, 0);
        LocalDateTime endDateTime = LocalDateTime.of(2023, 12, 25, 12, 0, 0);
        long hours = DateUtils.hoursBetween(startDateTime, endDateTime);
        assertEquals(2, hours);
    }

    @Test
    void testMinutesBetween() {
        LocalDateTime startDateTime = LocalDateTime.of(2023, 12, 25, 10, 0, 0);
        LocalDateTime endDateTime = LocalDateTime.of(2023, 12, 25, 10, 30, 0);
        long minutes = DateUtils.minutesBetween(startDateTime, endDateTime);
        assertEquals(30, minutes);
    }

    @Test
    void testIsToday() {
        LocalDate today = LocalDate.now();
        assertTrue(DateUtils.isToday(today));
        
        LocalDate yesterday = LocalDate.now().minusDays(1);
        assertFalse(DateUtils.isToday(yesterday));
        
        Date todayDate = new Date();
        assertTrue(DateUtils.isToday(todayDate));
    }

    @Test
    void testGetDateComponents() {
        Date date = DateUtils.parseDate("2023-12-25 10:30:45");
        assertNotNull(date);
        
        assertEquals(2023, DateUtils.getYear(date));
        assertEquals(12, DateUtils.getMonth(date));
        assertEquals(25, DateUtils.getDay(date));
        assertEquals(10, DateUtils.getHour(date));
        assertEquals(30, DateUtils.getMinute(date));
        assertEquals(45, DateUtils.getSecond(date));
    }
}
