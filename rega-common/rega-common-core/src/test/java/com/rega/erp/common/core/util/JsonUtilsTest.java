package com.rega.erp.common.core.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JSON工具类测试
 *
 * <AUTHOR>
 */
class JsonUtilsTest {

    @Data
    static class TestUser {
        private Long id;
        private String name;
        private String email;
        private LocalDateTime createTime;
    }

    @Test
    void testToJsonString() {
        TestUser user = new TestUser();
        user.setId(1L);
        user.setName("张三");
        user.setEmail("<EMAIL>");
        user.setCreateTime(LocalDateTime.of(2023, 12, 25, 10, 30, 45));

        String json = JsonUtils.toJsonString(user);
        assertNotNull(json);
        assertTrue(json.contains("张三"));
        assertTrue(json.contains("<EMAIL>"));
    }

    @Test
    void testToPrettyJsonString() {
        TestUser user = new TestUser();
        user.setId(1L);
        user.setName("张三");
        user.setEmail("<EMAIL>");

        String json = JsonUtils.toPrettyJsonString(user);
        assertNotNull(json);
        assertTrue(json.contains("\n"));
        assertTrue(json.contains("  "));
    }

    @Test
    void testParseObject() {
        String json = "{\"id\":1,\"name\":\"张三\",\"email\":\"<EMAIL>\"}";
        TestUser user = JsonUtils.parseObject(json, TestUser.class);
        
        assertNotNull(user);
        assertEquals(1L, user.getId());
        assertEquals("张三", user.getName());
        assertEquals("<EMAIL>", user.getEmail());
    }

    @Test
    void testParseObjectWithTypeReference() {
        String json = "{\"key1\":\"value1\",\"key2\":\"value2\"}";
        Map<String, String> map = JsonUtils.parseObject(json, new TypeReference<Map<String, String>>() {});
        
        assertNotNull(map);
        assertEquals("value1", map.get("key1"));
        assertEquals("value2", map.get("key2"));
    }

    @Test
    void testParseList() {
        String json = "[{\"id\":1,\"name\":\"张三\"},{\"id\":2,\"name\":\"李四\"}]";
        List<TestUser> users = JsonUtils.parseList(json, TestUser.class);
        
        assertNotNull(users);
        assertEquals(2, users.size());
        assertEquals("张三", users.get(0).getName());
        assertEquals("李四", users.get(1).getName());
    }

    @Test
    void testParseMap() {
        String json = "{\"key1\":\"value1\",\"key2\":123,\"key3\":true}";
        Map<String, Object> map = JsonUtils.parseMap(json);
        
        assertNotNull(map);
        assertEquals("value1", map.get("key1"));
        assertEquals(123, map.get("key2"));
        assertEquals(true, map.get("key3"));
    }

    @Test
    void testParseMapWithValueClass() {
        String json = "{\"key1\":\"value1\",\"key2\":\"value2\"}";
        Map<String, String> map = JsonUtils.parseMap(json, String.class);
        
        assertNotNull(map);
        assertEquals("value1", map.get("key1"));
        assertEquals("value2", map.get("key2"));
    }

    @Test
    void testParseTree() {
        String json = "{\"name\":\"张三\",\"age\":25,\"address\":{\"city\":\"北京\",\"district\":\"朝阳区\"}}";
        JsonNode jsonNode = JsonUtils.parseTree(json);
        
        assertNotNull(jsonNode);
        assertTrue(jsonNode.has("name"));
        assertTrue(jsonNode.has("address"));
        assertTrue(jsonNode.get("address").has("city"));
    }

    @Test
    void testGetStringFromJsonNode() {
        String json = "{\"name\":\"张三\",\"age\":25}";
        JsonNode jsonNode = JsonUtils.parseTree(json);
        
        String name = JsonUtils.getString(jsonNode, "name");
        assertEquals("张三", name);
        
        String nonExistent = JsonUtils.getString(jsonNode, "nonExistent");
        assertNull(nonExistent);
    }

    @Test
    void testGetIntegerFromJsonNode() {
        String json = "{\"name\":\"张三\",\"age\":25}";
        JsonNode jsonNode = JsonUtils.parseTree(json);
        
        Integer age = JsonUtils.getInteger(jsonNode, "age");
        assertEquals(25, age);
        
        Integer nonExistent = JsonUtils.getInteger(jsonNode, "nonExistent");
        assertNull(nonExistent);
    }

    @Test
    void testGetLongFromJsonNode() {
        String json = "{\"id\":123456789012345,\"name\":\"张三\"}";
        JsonNode jsonNode = JsonUtils.parseTree(json);
        
        Long id = JsonUtils.getLong(jsonNode, "id");
        assertEquals(123456789012345L, id);
    }

    @Test
    void testGetBooleanFromJsonNode() {
        String json = "{\"active\":true,\"deleted\":false}";
        JsonNode jsonNode = JsonUtils.parseTree(json);
        
        Boolean active = JsonUtils.getBoolean(jsonNode, "active");
        assertTrue(active);
        
        Boolean deleted = JsonUtils.getBoolean(jsonNode, "deleted");
        assertFalse(deleted);
    }

    @Test
    void testIsValidJson() {
        assertTrue(JsonUtils.isValidJson("{\"name\":\"张三\"}"));
        assertTrue(JsonUtils.isValidJson("[1,2,3]"));
        assertTrue(JsonUtils.isValidJson("\"string\""));
        assertTrue(JsonUtils.isValidJson("123"));
        assertTrue(JsonUtils.isValidJson("true"));
        
        assertFalse(JsonUtils.isValidJson("{name:\"张三\"}"));
        assertFalse(JsonUtils.isValidJson("invalid json"));
        assertFalse(JsonUtils.isValidJson(""));
        assertFalse(JsonUtils.isValidJson(null));
    }

    @Test
    void testConvertValue() {
        Map<String, Object> map = new HashMap<>();
        map.put("id", 1);
        map.put("name", "张三");
        map.put("email", "<EMAIL>");
        
        TestUser user = JsonUtils.convertValue(map, TestUser.class);
        assertNotNull(user);
        assertEquals(1L, user.getId());
        assertEquals("张三", user.getName());
        assertEquals("<EMAIL>", user.getEmail());
    }

    @Test
    void testConvertValueWithTypeReference() {
        TestUser user = new TestUser();
        user.setId(1L);
        user.setName("张三");
        user.setEmail("<EMAIL>");
        
        Map<String, Object> map = JsonUtils.convertValue(user, new TypeReference<Map<String, Object>>() {});
        assertNotNull(map);
        assertEquals(1, map.get("id"));
        assertEquals("张三", map.get("name"));
        assertEquals("<EMAIL>", map.get("email"));
    }

    @Test
    void testNullValues() {
        assertNull(JsonUtils.toJsonString(null));
        assertNull(JsonUtils.toPrettyJsonString(null));
        assertNull(JsonUtils.parseObject(null, TestUser.class));
        assertNull(JsonUtils.parseObject("", TestUser.class));
        assertNull(JsonUtils.parseList(null, TestUser.class));
        assertNull(JsonUtils.parseMap(null));
        assertNull(JsonUtils.parseTree(null));
        assertNull(JsonUtils.convertValue(null, TestUser.class));
    }

    @Test
    void testInvalidJson() {
        assertNull(JsonUtils.parseObject("invalid json", TestUser.class));
        assertNull(JsonUtils.parseList("invalid json", TestUser.class));
        assertNull(JsonUtils.parseMap("invalid json"));
        assertNull(JsonUtils.parseTree("invalid json"));
    }

    @Test
    void testComplexObject() {
        Map<String, Object> complexObject = new HashMap<>();
        complexObject.put("users", Arrays.asList(
            createTestUser(1L, "张三"),
            createTestUser(2L, "李四")
        ));
        complexObject.put("total", 2);
        complexObject.put("success", true);
        
        String json = JsonUtils.toJsonString(complexObject);
        assertNotNull(json);
        
        Map<String, Object> parsed = JsonUtils.parseMap(json);
        assertNotNull(parsed);
        assertEquals(2, parsed.get("total"));
        assertEquals(true, parsed.get("success"));
        assertTrue(parsed.get("users") instanceof List);
    }

    private TestUser createTestUser(Long id, String name) {
        TestUser user = new TestUser();
        user.setId(id);
        user.setName(name);
        user.setEmail(name.toLowerCase() + "@example.com");
        return user;
    }
}
