package com.rega.erp.common.core.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ID工具类测试
 *
 * <AUTHOR>
 */
class IdUtilsTest {

    @Test
    void testRandomUUID() {
        String uuid1 = IdUtils.randomUUID();
        String uuid2 = IdUtils.randomUUID();
        
        assertNotNull(uuid1);
        assertNotNull(uuid2);
        assertNotEquals(uuid1, uuid2);
        assertTrue(uuid1.contains("-"));
        assertEquals(36, uuid1.length());
    }

    @Test
    void testSimpleUUID() {
        String uuid1 = IdUtils.simpleUUID();
        String uuid2 = IdUtils.simpleUUID();
        
        assertNotNull(uuid1);
        assertNotNull(uuid2);
        assertNotEquals(uuid1, uuid2);
        assertFalse(uuid1.contains("-"));
        assertEquals(32, uuid1.length());
    }

    @Test
    void testFastUUID() {
        String uuid1 = IdUtils.fastUUID();
        String uuid2 = IdUtils.fastUUID();
        
        assertNotNull(uuid1);
        assertNotNull(uuid2);
        assertNotEquals(uuid1, uuid2);
        assertTrue(uuid1.contains("-"));
        assertEquals(36, uuid1.length());
    }

    @Test
    void testFastSimpleUUID() {
        String uuid1 = IdUtils.fastSimpleUUID();
        String uuid2 = IdUtils.fastSimpleUUID();
        
        assertNotNull(uuid1);
        assertNotNull(uuid2);
        assertNotEquals(uuid1, uuid2);
        assertFalse(uuid1.contains("-"));
        assertEquals(32, uuid1.length());
    }

    @Test
    void testSnowflakeId() {
        long id1 = IdUtils.snowflakeId();
        long id2 = IdUtils.snowflakeId();
        
        assertTrue(id1 > 0);
        assertTrue(id2 > 0);
        assertNotEquals(id1, id2);
    }

    @Test
    void testSnowflakeIdString() {
        String id1 = IdUtils.snowflakeIdString();
        String id2 = IdUtils.snowflakeIdString();
        
        assertNotNull(id1);
        assertNotNull(id2);
        assertNotEquals(id1, id2);
        assertTrue(id1.matches("\\d+"));
        assertTrue(id2.matches("\\d+"));
    }

    @Test
    void testRandomNumeric() {
        String numeric1 = IdUtils.randomNumeric(6);
        String numeric2 = IdUtils.randomNumeric(6);
        
        assertNotNull(numeric1);
        assertNotNull(numeric2);
        assertEquals(6, numeric1.length());
        assertEquals(6, numeric2.length());
        assertTrue(numeric1.matches("\\d{6}"));
        assertTrue(numeric2.matches("\\d{6}"));
    }

    @Test
    void testRandomAlphabetic() {
        String alpha1 = IdUtils.randomAlphabetic(8);
        String alpha2 = IdUtils.randomAlphabetic(8);
        
        assertNotNull(alpha1);
        assertNotNull(alpha2);
        assertEquals(8, alpha1.length());
        assertEquals(8, alpha2.length());
        assertTrue(alpha1.matches("[a-zA-Z]{8}"));
        assertTrue(alpha2.matches("[a-zA-Z]{8}"));
    }

    @Test
    void testRandomAlphanumeric() {
        String alphanum1 = IdUtils.randomAlphanumeric(10);
        String alphanum2 = IdUtils.randomAlphanumeric(10);
        
        assertNotNull(alphanum1);
        assertNotNull(alphanum2);
        assertEquals(10, alphanum1.length());
        assertEquals(10, alphanum2.length());
        assertTrue(alphanum1.matches("[a-zA-Z0-9]{10}"));
        assertTrue(alphanum2.matches("[a-zA-Z0-9]{10}"));
    }

    @Test
    void testRandomString() {
        String str1 = IdUtils.randomString(12);
        String str2 = IdUtils.randomString(12);
        
        assertNotNull(str1);
        assertNotNull(str2);
        assertEquals(12, str1.length());
        assertEquals(12, str2.length());
        assertNotEquals(str1, str2);
    }

    @Test
    void testGenerateOrderNo() {
        String orderNo1 = IdUtils.generateOrderNo();
        String orderNo2 = IdUtils.generateOrderNo();
        
        assertNotNull(orderNo1);
        assertNotNull(orderNo2);
        assertNotEquals(orderNo1, orderNo2);
        assertEquals(20, orderNo1.length()); // yyyyMMddHHmmss(14) + 6位随机数
        assertTrue(orderNo1.matches("\\d{20}"));
    }

    @Test
    void testGenerateOrderNoWithPrefix() {
        String orderNo1 = IdUtils.generateOrderNo("ORD");
        String orderNo2 = IdUtils.generateOrderNo("ORD");
        
        assertNotNull(orderNo1);
        assertNotNull(orderNo2);
        assertNotEquals(orderNo1, orderNo2);
        assertTrue(orderNo1.startsWith("ORD"));
        assertTrue(orderNo2.startsWith("ORD"));
        assertEquals(23, orderNo1.length()); // ORD(3) + yyyyMMddHHmmss(14) + 6位随机数
    }

    @Test
    void testGenerateSerialNo() {
        String serialNo1 = IdUtils.generateSerialNo();
        String serialNo2 = IdUtils.generateSerialNo();
        
        assertNotNull(serialNo1);
        assertNotNull(serialNo2);
        assertNotEquals(serialNo1, serialNo2);
        assertEquals(16, serialNo1.length()); // yyyyMMdd(8) + 8位随机数
        assertTrue(serialNo1.matches("\\d{16}"));
    }

    @Test
    void testGenerateSerialNoWithPrefix() {
        String serialNo1 = IdUtils.generateSerialNo("SN");
        String serialNo2 = IdUtils.generateSerialNo("SN");
        
        assertNotNull(serialNo1);
        assertNotNull(serialNo2);
        assertNotEquals(serialNo1, serialNo2);
        assertTrue(serialNo1.startsWith("SN"));
        assertTrue(serialNo2.startsWith("SN"));
        assertEquals(18, serialNo1.length()); // SN(2) + yyyyMMdd(8) + 8位随机数
    }

    @Test
    void testGenerateVerifyCode() {
        String code1 = IdUtils.generateVerifyCode(4);
        String code2 = IdUtils.generateVerifyCode(4);
        
        assertNotNull(code1);
        assertNotNull(code2);
        assertEquals(4, code1.length());
        assertEquals(4, code2.length());
        assertTrue(code1.matches("\\d{4}"));
        assertTrue(code2.matches("\\d{4}"));
        
        // 测试默认6位验证码
        String defaultCode = IdUtils.generateVerifyCode();
        assertNotNull(defaultCode);
        assertEquals(6, defaultCode.length());
        assertTrue(defaultCode.matches("\\d{6}"));
    }

    @Test
    void testGenerateInviteCode() {
        String code1 = IdUtils.generateInviteCode();
        String code2 = IdUtils.generateInviteCode();
        
        assertNotNull(code1);
        assertNotNull(code2);
        assertNotEquals(code1, code2);
        assertEquals(8, code1.length());
        assertTrue(code1.matches("[A-Z0-9]{8}"));
    }

    @Test
    void testGenerateTempPassword() {
        String pwd1 = IdUtils.generateTempPassword();
        String pwd2 = IdUtils.generateTempPassword();
        
        assertNotNull(pwd1);
        assertNotNull(pwd2);
        assertNotEquals(pwd1, pwd2);
        assertEquals(8, pwd1.length());
        assertTrue(pwd1.matches("[a-zA-Z0-9]{8}"));
    }

    @Test
    void testGenerateApiKey() {
        String key1 = IdUtils.generateApiKey();
        String key2 = IdUtils.generateApiKey();
        
        assertNotNull(key1);
        assertNotNull(key2);
        assertNotEquals(key1, key2);
        assertEquals(32, key1.length());
        assertTrue(key1.matches("[a-zA-Z0-9]{32}"));
    }

    @Test
    void testGenerateSecret() {
        String secret1 = IdUtils.generateSecret(16);
        String secret2 = IdUtils.generateSecret(16);
        
        assertNotNull(secret1);
        assertNotNull(secret2);
        assertNotEquals(secret1, secret2);
        assertEquals(16, secret1.length());
        assertTrue(secret1.matches("[a-zA-Z0-9]{16}"));
    }

    @Test
    void testGenerateFileName() {
        String fileName1 = IdUtils.generateFileName("test.jpg");
        String fileName2 = IdUtils.generateFileName("test.jpg");
        
        assertNotNull(fileName1);
        assertNotNull(fileName2);
        assertNotEquals(fileName1, fileName2);
        assertTrue(fileName1.endsWith(".jpg"));
        assertTrue(fileName2.endsWith(".jpg"));
        assertEquals(36, fileName1.length()); // 32位UUID + .jpg(4)
        
        // 测试无扩展名
        String fileNameNoExt = IdUtils.generateFileName("test");
        assertNotNull(fileNameNoExt);
        assertEquals(32, fileNameNoExt.length());
        
        // 测试空文件名
        String fileNameEmpty = IdUtils.generateFileName("");
        assertNotNull(fileNameEmpty);
        assertEquals(32, fileNameEmpty.length());
    }

    @Test
    void testRandomLong() {
        long long1 = IdUtils.randomLong();
        long long2 = IdUtils.randomLong();
        
        assertTrue(long1 >= 1000000000000000L);
        assertTrue(long1 < 9999999999999999L);
        assertTrue(long2 >= 1000000000000000L);
        assertTrue(long2 < 9999999999999999L);
        assertNotEquals(long1, long2);
    }

    @Test
    void testRandomInt() {
        int int1 = IdUtils.randomInt();
        int int2 = IdUtils.randomInt();
        
        assertTrue(int1 >= 100000000);
        assertTrue(int1 < 999999999);
        assertTrue(int2 >= 100000000);
        assertTrue(int2 < 999999999);
        assertNotEquals(int1, int2);
    }

    @Test
    void testRandomIntWithRange() {
        int int1 = IdUtils.randomInt(10, 20);
        int int2 = IdUtils.randomInt(10, 20);
        
        assertTrue(int1 >= 10);
        assertTrue(int1 < 20);
        assertTrue(int2 >= 10);
        assertTrue(int2 < 20);
    }

    @Test
    void testRandomLongWithRange() {
        long long1 = IdUtils.randomLong(100L, 200L);
        long long2 = IdUtils.randomLong(100L, 200L);
        
        assertTrue(long1 >= 100L);
        assertTrue(long1 < 200L);
        assertTrue(long2 >= 100L);
        assertTrue(long2 < 200L);
    }
}
