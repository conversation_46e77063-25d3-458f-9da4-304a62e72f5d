package com.rega.erp.common.i18n.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import com.rega.erp.common.i18n.service.impl.I18nServiceImpl;

import java.util.Locale;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 国际化服务测试类
 *
 * <AUTHOR>
 */
@SpringJUnitConfig
class I18nServiceTest {

    private I18nService i18nService;
    private MessageSource messageSource;

    @BeforeEach
    void setUp() {
        // 创建消息源
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setBasename("classpath:i18n/messages");
        messageSource.setDefaultEncoding("UTF-8");
        messageSource.setFallbackToSystemLocale(false);
        messageSource.setUseCodeAsDefaultMessage(true);
        
        this.messageSource = messageSource;
        this.i18nService = new I18nServiceImpl(messageSource);
    }

    @Test
    void testGetMessage_SimpleChinese() {
        // 设置中文环境
        LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);
        
        String message = i18nService.getMessage("common.success");
        assertEquals("操作成功", message);
        
        message = i18nService.getMessage("user.not.found");
        assertEquals("用户不存在", message);
    }

    @Test
    void testGetMessage_English() {
        // 设置英文环境
        LocaleContextHolder.setLocale(Locale.US);
        
        String message = i18nService.getMessage("common.success");
        assertEquals("Operation successful", message);
        
        message = i18nService.getMessage("user.not.found");
        assertEquals("User not found", message);
    }

    @Test
    void testGetMessage_TraditionalChinese() {
        // 设置繁体中文环境
        LocaleContextHolder.setLocale(Locale.TRADITIONAL_CHINESE);
        
        String message = i18nService.getMessage("common.success");
        assertEquals("操作成功", message);
        
        message = i18nService.getMessage("common.fail");
        assertEquals("操作失敗", message);
    }

    @Test
    void testGetMessage_WithArgs() {
        LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);
        
        String message = i18nService.getMessage("validation.size", new Object[]{1, 10});
        assertEquals("长度必须在1到10之间", message);
        
        message = i18nService.getMessage("form.field.required", new Object[]{"用户名"});
        assertEquals("字段用户名为必填项", message);
    }

    @Test
    void testGetMessage_WithDefaultMessage() {
        LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);
        
        String message = i18nService.getMessage("non.existent.key", null, "默认消息");
        assertEquals("默认消息", message);
    }

    @Test
    void testGetMessage_SpecificLocale() {
        String message = i18nService.getMessage("common.success", Locale.US);
        assertEquals("Operation successful", message);
        
        message = i18nService.getMessage("common.success", Locale.SIMPLIFIED_CHINESE);
        assertEquals("操作成功", message);
    }

    @Test
    void testHasMessage() {
        assertTrue(i18nService.hasMessage("common.success"));
        assertTrue(i18nService.hasMessage("user.not.found"));
        assertFalse(i18nService.hasMessage("non.existent.key"));
    }

    @Test
    void testHasMessage_SpecificLocale() {
        assertTrue(i18nService.hasMessage("common.success", Locale.US));
        assertTrue(i18nService.hasMessage("common.success", Locale.SIMPLIFIED_CHINESE));
        assertTrue(i18nService.hasMessage("common.success", Locale.TRADITIONAL_CHINESE));
        assertFalse(i18nService.hasMessage("non.existent.key", Locale.US));
    }

    @Test
    void testGetCurrentLocale() {
        LocaleContextHolder.setLocale(Locale.US);
        assertEquals(Locale.US, i18nService.getCurrentLocale());
        
        LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);
        assertEquals(Locale.SIMPLIFIED_CHINESE, i18nService.getCurrentLocale());
        
        // 测试默认语言环境
        LocaleContextHolder.resetLocaleContext();
        assertEquals(Locale.SIMPLIFIED_CHINESE, i18nService.getCurrentLocale());
    }

    @Test
    void testValidationMessages() {
        LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);
        
        assertEquals("不能为空", i18nService.getMessage("validation.notNull"));
        assertEquals("不能为空", i18nService.getMessage("validation.notBlank"));
        assertEquals("邮箱格式不正确", i18nService.getMessage("validation.email"));
        assertEquals("手机号格式不正确", i18nService.getMessage("validation.mobile"));
        
        LocaleContextHolder.setLocale(Locale.US);
        
        assertEquals("Cannot be null", i18nService.getMessage("validation.notNull"));
        assertEquals("Cannot be blank", i18nService.getMessage("validation.notBlank"));
        assertEquals("Invalid email format", i18nService.getMessage("validation.email"));
        assertEquals("Invalid mobile number format", i18nService.getMessage("validation.mobile"));
    }

    @Test
    void testBusinessMessages() {
        LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);
        
        assertEquals("用户创建成功", i18nService.getMessage("user.create.success"));
        assertEquals("租户不存在", i18nService.getMessage("tenant.not.found"));
        assertEquals("角色分配成功", i18nService.getMessage("role.assign.success"));
        assertEquals("权限不足", i18nService.getMessage("auth.permission.denied"));
        
        LocaleContextHolder.setLocale(Locale.US);
        
        assertEquals("User created successfully", i18nService.getMessage("user.create.success"));
        assertEquals("Tenant not found", i18nService.getMessage("tenant.not.found"));
        assertEquals("Role assigned successfully", i18nService.getMessage("role.assign.success"));
        assertEquals("Permission denied", i18nService.getMessage("auth.permission.denied"));
    }
}
