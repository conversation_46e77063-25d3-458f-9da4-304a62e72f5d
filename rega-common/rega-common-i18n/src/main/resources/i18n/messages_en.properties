# Common Messages
common.success=Operation successful
common.fail=Operation failed
common.failed=Operation failed
common.error=System error
common.unauthorized=Unauthorized access
common.forbidden=Access forbidden
common.notFound=Resource not found
common.not.found=Resource not found
common.methodNotAllowed=Method not allowed
common.method.not.allowed=Method not allowed
common.badRequest=Bad request parameters
common.bad.request=Bad request parameters
common.timeout=Request timeout
common.serverBusy=System is busy, please try again later
common.conflict=Resource conflict
common.server.error=Internal server error
common.service.unavailable=Service unavailable

# Validation Messages
validation.notNull=Cannot be null
validation.notBlank=Cannot be blank
validation.notEmpty=Cannot be empty
validation.size=Length must be between {min} and {max}
validation.length=Length must be {max}
validation.range=Must be between {min} and {max}
validation.min=Cannot be less than {value}
validation.max=Cannot be greater than {value}
validation.email=Invalid email format
validation.mobile=Invalid mobile number format
validation.phone=Invalid phone number format
validation.idcard=Invalid ID card format
validation.url=Invalid URL format
validation.date=Invalid date format
validation.number=Must be a number
validation.positive=Must be a positive number
validation.negative=Must be a negative number
validation.pattern=Invalid format
validation.future=Must be a future date
validation.past=Must be a past date

# Authentication Related
auth.login.success=Login successful
auth.login.failed=Login failed
auth.logout.success=Logout successful
auth.token.expired=Token expired
auth.token.invalid=Invalid token
auth.captcha.incorrect=Incorrect verification code
auth.captcha.expired=Verification code expired
auth.account.locked=Account locked
auth.account.disabled=Account disabled
auth.password.incorrect=Incorrect password
auth.password.expired=Password expired
auth.permission.denied=Permission denied

# User Related
user.notFound=User not found
user.not.found=User not found
user.disabled=User is disabled
user.locked=User is locked
user.expired=User has expired
user.passwordExpired=Password has expired
user.passwordError=Password error
user.oldPasswordError=Old password error
user.already.exists=User already exists
user.create.success=User created successfully
user.update.success=User updated successfully
user.delete.success=User deleted successfully
user.enable.success=User enabled successfully
user.disable.success=User disabled successfully
user.reset.password.success=Password reset successfully

# Tenant Related
tenant.notFound=Tenant not found
tenant.not.found=Tenant not found
tenant.disabled=Tenant is disabled
tenant.expired=Tenant has expired
tenant.already.exists=Tenant already exists
tenant.create.success=Tenant created successfully
tenant.update.success=Tenant updated successfully
tenant.delete.success=Tenant deleted successfully
tenant.enable.success=Tenant enabled successfully
tenant.disable.success=Tenant disabled successfully

# Role Related
role.not.found=Role not found
role.already.exists=Role already exists
role.create.success=Role created successfully
role.update.success=Role updated successfully
role.delete.success=Role deleted successfully
role.assign.success=Role assigned successfully
role.revoke.success=Role revoked successfully

# Permission Related
permission.not.found=Permission not found
permission.already.exists=Permission already exists
permission.create.success=Permission created successfully
permission.update.success=Permission updated successfully
permission.delete.success=Permission deleted successfully
permission.grant.success=Permission granted successfully
permission.revoke.success=Permission revoked successfully

# Menu Related
menu.not.found=Menu not found
menu.already.exists=Menu already exists
menu.create.success=Menu created successfully
menu.update.success=Menu updated successfully
menu.delete.success=Menu deleted successfully
menu.has.children=Menu has children, cannot be deleted

# Department Related
dept.not.found=Department not found
dept.already.exists=Department already exists
dept.create.success=Department created successfully
dept.update.success=Department updated successfully
dept.delete.success=Department deleted successfully
dept.has.children=Department has children, cannot be deleted
dept.has.users=Department has users, cannot be deleted

# Position Related
post.not.found=Position not found
post.already.exists=Position already exists
post.create.success=Position created successfully
post.update.success=Position updated successfully
post.delete.success=Position deleted successfully
post.has.users=Position has users, cannot be deleted

# Form Related
form.not.found=Form not found
form.already.exists=Form already exists
form.create.success=Form created successfully
form.update.success=Form updated successfully
form.delete.success=Form deleted successfully
form.publish.success=Form published successfully
form.unpublish.success=Form unpublished successfully
form.data.submit.success=Form data submitted successfully
form.data.update.success=Form data updated successfully
form.data.delete.success=Form data deleted successfully
form.schema.invalid=Invalid form schema
form.data.not.found=Form data not found
form.field.required=Field {0} is required
form.field.invalid=Field {0} format is invalid

# Workflow Related
workflow.not.found=Workflow not found
workflow.already.exists=Workflow already exists
workflow.create.success=Workflow created successfully
workflow.update.success=Workflow updated successfully
workflow.delete.success=Workflow deleted successfully
workflow.deploy.success=Workflow deployed successfully
workflow.undeploy.success=Workflow undeployed successfully
workflow.start.success=Workflow started successfully
workflow.suspend.success=Workflow suspended successfully
workflow.activate.success=Workflow activated successfully
workflow.task.complete.success=Task completed successfully
workflow.task.reject.success=Task rejected successfully
workflow.task.transfer.success=Task transferred successfully
workflow.task.delegate.success=Task delegated successfully
workflow.task.claim.success=Task claimed successfully
workflow.process.invalid=Invalid process definition
workflow.task.not.found=Task not found
workflow.instance.not.found=Process instance not found
workflow.definition.not.found=Process definition not found

# Report Related
report.not.found=Report not found
report.already.exists=Report already exists
report.create.success=Report created successfully
report.update.success=Report updated successfully
report.delete.success=Report deleted successfully
report.generate.success=Report generated successfully
report.export.success=Report exported successfully
report.template.invalid=Invalid report template
report.data.empty=Report data is empty
report.parameter.required=Report parameter {0} is required

# File Related
file.not.found=File not found
file.upload.success=File uploaded successfully
file.upload.failed=File upload failed
file.download.success=File downloaded successfully
file.download.failed=File download failed
file.delete.success=File deleted successfully
file.delete.failed=File deletion failed
file.size.exceeded=File size exceeded limit
file.type.not.supported=File type not supported
file.name.invalid=Invalid file name

# Dictionary Related
dict.not.found=Dictionary not found
dict.already.exists=Dictionary already exists
dict.create.success=Dictionary created successfully
dict.update.success=Dictionary updated successfully
dict.delete.success=Dictionary deleted successfully
dict.type.not.found=Dictionary type not found
dict.type.already.exists=Dictionary type already exists
dict.data.not.found=Dictionary data not found
dict.data.already.exists=Dictionary data already exists

# Configuration Related
config.not.found=Configuration not found
config.already.exists=Configuration already exists
config.create.success=Configuration created successfully
config.update.success=Configuration updated successfully
config.delete.success=Configuration deleted successfully
config.refresh.success=Configuration refreshed successfully
config.key.invalid=Invalid configuration key
config.value.invalid=Invalid configuration value

# Log Related
log.not.found=Log not found
log.clear.success=Log cleared successfully
log.export.success=Log exported successfully
log.level.invalid=Invalid log level

# Cache Related
cache.clear.success=Cache cleared successfully
cache.refresh.success=Cache refreshed successfully
cache.key.not.found=Cache key not found

# Scheduled Job Related
job.not.found=Scheduled job not found
job.already.exists=Scheduled job already exists
job.create.success=Scheduled job created successfully
job.update.success=Scheduled job updated successfully
job.delete.success=Scheduled job deleted successfully
job.start.success=Scheduled job started successfully
job.pause.success=Scheduled job paused successfully
job.resume.success=Scheduled job resumed successfully
job.execute.success=Scheduled job executed successfully
job.cron.invalid=Invalid cron expression

# Notice Related
notice.not.found=Notice not found
notice.create.success=Notice created successfully
notice.update.success=Notice updated successfully
notice.delete.success=Notice deleted successfully
notice.send.success=Notice sent successfully
notice.read.success=Notice marked as read successfully
notice.type.invalid=Invalid notice type

# Message Related
message.not.found=Message not found
message.send.success=Message sent successfully
message.read.success=Message marked as read successfully
message.delete.success=Message deleted successfully
message.type.invalid=Invalid message type

# Database Related
database.connection.failed=Database connection failed
database.query.failed=Database query failed
database.update.failed=Database update failed
database.transaction.failed=Database transaction failed
database.constraint.violation=Database constraint violation
database.duplicate.key=Duplicate database key

# Network Related
network.connection.timeout=Network connection timeout
network.request.failed=Network request failed
network.response.invalid=Invalid network response
network.service.unavailable=Network service unavailable