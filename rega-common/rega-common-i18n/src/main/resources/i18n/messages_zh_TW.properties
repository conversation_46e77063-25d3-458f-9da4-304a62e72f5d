# 通用消息
common.success=操作成功
common.fail=操作失敗
common.failed=操作失敗
common.error=系統錯誤
common.unauthorized=未授權的訪問
common.forbidden=禁止訪問
common.notFound=資源不存在
common.not.found=資源不存在
common.methodNotAllowed=方法不允許
common.method.not.allowed=請求方法不允許
common.badRequest=請求參數錯誤
common.bad.request=請求參數錯誤
common.timeout=請求超時
common.serverBusy=系統繁忙，請稍後再試
common.conflict=資源衝突
common.server.error=服務器內部錯誤
common.service.unavailable=服務不可用

# 驗證消息
validation.notNull=不能為空
validation.notBlank=不能為空
validation.notEmpty=不能為空
validation.size=長度必須在{min}到{max}之間
validation.length=長度必須為{max}
validation.range=必須在{min}到{max}之間
validation.min=不能小於{value}
validation.max=不能大於{value}
validation.email=郵箱格式不正確
validation.mobile=手機號格式不正確
validation.phone=電話號碼格式不正確
validation.idcard=身份證號格式不正確
validation.url=URL格式不正確
validation.date=日期格式不正確
validation.number=必須是數字
validation.positive=必須是正數
validation.negative=必須是負數
validation.pattern=格式不正確
validation.future=必須是將來的日期
validation.past=必須是過去的日期
validation.error=驗證失敗

# 認證相關
auth.login.success=登錄成功
auth.login.failed=登錄失敗
auth.logout.success=登出成功
auth.token.expired=令牌已過期
auth.token.invalid=無效的令牌
auth.captcha.incorrect=驗證碼錯誤
auth.captcha.expired=驗證碼已過期
auth.account.locked=賬號已鎖定
auth.account.disabled=賬號已禁用
auth.password.incorrect=密碼錯誤
auth.password.expired=密碼已過期
auth.permission.denied=權限不足

# 用戶相關
user.notFound=用戶不存在
user.not.found=用戶不存在
user.disabled=用戶已禁用
user.locked=用戶已鎖定
user.expired=用戶已過期
user.passwordExpired=密碼已過期
user.passwordError=密碼錯誤
user.oldPasswordError=原密碼錯誤
user.already.exists=用戶已存在
user.create.success=用戶創建成功
user.update.success=用戶更新成功
user.delete.success=用戶刪除成功
user.enable.success=用戶啟用成功
user.disable.success=用戶禁用成功
user.reset.password.success=密碼重置成功

# 租戶相關
tenant.notFound=租戶不存在
tenant.not.found=租戶不存在
tenant.disabled=租戶已禁用
tenant.expired=租戶已過期
tenant.already.exists=租戶已存在
tenant.create.success=租戶創建成功
tenant.update.success=租戶更新成功
tenant.delete.success=租戶刪除成功
tenant.enable.success=租戶啟用成功
tenant.disable.success=租戶禁用成功

# 角色相關
role.not.found=角色不存在
role.already.exists=角色已存在
role.create.success=角色創建成功
role.update.success=角色更新成功
role.delete.success=角色刪除成功
role.assign.success=角色分配成功
role.revoke.success=角色撤銷成功

# 權限相關
permission.not.found=權限不存在
permission.already.exists=權限已存在
permission.create.success=權限創建成功
permission.update.success=權限更新成功
permission.delete.success=權限刪除成功
permission.grant.success=權限授予成功
permission.revoke.success=權限撤銷成功

# 菜單相關
menu.not.found=菜單不存在
menu.already.exists=菜單已存在
menu.create.success=菜單創建成功
menu.update.success=菜單更新成功
menu.delete.success=菜單刪除成功
menu.has.children=菜單存在子菜單，無法刪除

# 部門相關
dept.not.found=部門不存在
dept.already.exists=部門已存在
dept.create.success=部門創建成功
dept.update.success=部門更新成功
dept.delete.success=部門刪除成功
dept.has.children=部門存在子部門，無法刪除
dept.has.users=部門存在用戶，無法刪除

# 崗位相關
post.not.found=崗位不存在
post.already.exists=崗位已存在
post.create.success=崗位創建成功
post.update.success=崗位更新成功
post.delete.success=崗位刪除成功
post.has.users=崗位存在用戶，無法刪除

# 表單相關
form.not.found=表單不存在
form.already.exists=表單已存在
form.create.success=表單創建成功
form.update.success=表單更新成功
form.delete.success=表單刪除成功
form.publish.success=表單發佈成功
form.unpublish.success=表單取消發佈成功
form.data.submit.success=表單數據提交成功
form.data.update.success=表單數據更新成功
form.data.delete.success=表單數據刪除成功
form.schema.invalid=表單架構無效
form.data.not.found=表單數據不存在
form.field.required=字段{0}為必填項
form.field.invalid=字段{0}格式不正確

# 工作流相關
workflow.not.found=工作流不存在
workflow.already.exists=工作流已存在
workflow.create.success=工作流創建成功
workflow.update.success=工作流更新成功
workflow.delete.success=工作流刪除成功
workflow.deploy.success=工作流部署成功
workflow.undeploy.success=工作流取消部署成功
workflow.start.success=工作流啟動成功
workflow.suspend.success=工作流掛起成功
workflow.activate.success=工作流激活成功
workflow.task.complete.success=任務完成成功
workflow.task.reject.success=任務駁回成功
workflow.task.transfer.success=任務轉辦成功
workflow.task.delegate.success=任務委派成功
workflow.task.claim.success=任務簽收成功
workflow.process.invalid=流程定義無效
workflow.task.not.found=任務不存在
workflow.instance.not.found=流程實例不存在
workflow.definition.not.found=流程定義不存在

# 報表相關
report.not.found=報表不存在
report.already.exists=報表已存在
report.create.success=報表創建成功
report.update.success=報表更新成功
report.delete.success=報表刪除成功
report.generate.success=報表生成成功
report.export.success=報表導出成功
report.template.invalid=報表模板無效
report.data.empty=報表數據為空
report.parameter.required=報表參數{0}為必填項

# 文件相關
file.not.found=文件不存在
file.upload.success=文件上傳成功
file.upload.failed=文件上傳失敗
file.download.success=文件下載成功
file.download.failed=文件下載失敗
file.delete.success=文件刪除成功
file.delete.failed=文件刪除失敗
file.size.exceeded=文件大小超過限制
file.type.not.supported=不支持的文件類型
file.name.invalid=文件名無效

# 數據字典相關
dict.not.found=字典不存在
dict.already.exists=字典已存在
dict.create.success=字典創建成功
dict.update.success=字典更新成功
dict.delete.success=字典刪除成功
dict.type.not.found=字典類型不存在
dict.type.already.exists=字典類型已存在
dict.data.not.found=字典數據不存在
dict.data.already.exists=字典數據已存在

# 配置相關
config.not.found=配置不存在
config.already.exists=配置已存在
config.create.success=配置創建成功
config.update.success=配置更新成功
config.delete.success=配置刪除成功
config.refresh.success=配置刷新成功
config.key.invalid=配置鍵無效
config.value.invalid=配置值無效

# 日誌相關
log.not.found=日誌不存在
log.clear.success=日誌清理成功
log.export.success=日誌導出成功
log.level.invalid=日誌級別無效

# 緩存相關
cache.clear.success=緩存清理成功
cache.refresh.success=緩存刷新成功
cache.key.not.found=緩存鍵不存在

# 定時任務相關
job.not.found=定時任務不存在
job.already.exists=定時任務已存在
job.create.success=定時任務創建成功
job.update.success=定時任務更新成功
job.delete.success=定時任務刪除成功
job.start.success=定時任務啟動成功
job.pause.success=定時任務暫停成功
job.resume.success=定時任務恢復成功
job.execute.success=定時任務執行成功
job.cron.invalid=Cron表達式無效

# 通知相關
notice.not.found=通知不存在
notice.create.success=通知創建成功
notice.update.success=通知更新成功
notice.delete.success=通知刪除成功
notice.send.success=通知發送成功
notice.read.success=通知已讀成功
notice.type.invalid=通知類型無效

# 消息相關
message.not.found=消息不存在
message.send.success=消息發送成功
message.read.success=消息已讀成功
message.delete.success=消息刪除成功
message.type.invalid=消息類型無效

# 數據庫相關
database.connection.failed=數據庫連接失敗
database.query.failed=數據庫查詢失敗
database.update.failed=數據庫更新失敗
database.transaction.failed=數據庫事務失敗
database.constraint.violation=數據庫約束違反
database.duplicate.key=數據庫主鍵重複

# 網絡相關
network.connection.timeout=網絡連接超時
network.request.failed=網絡請求失敗
network.response.invalid=網絡響應無效
network.service.unavailable=網絡服務不可用
