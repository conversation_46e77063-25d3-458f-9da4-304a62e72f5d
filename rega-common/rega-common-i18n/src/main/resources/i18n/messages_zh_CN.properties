# 通用消息
common.success=操作成功
common.fail=操作失败
common.error=系统错误
common.unauthorized=未授权
common.forbidden=禁止访问
common.notFound=资源未找到
common.methodNotAllowed=方法不允许
common.badRequest=请求参数错误
common.timeout=请求超时
common.serverBusy=系统繁忙，请稍后再试

# 验证消息
validation.notNull=不能为空
validation.notBlank=不能为空
validation.size=长度必须在{min}到{max}之间
validation.length=长度必须为{max}
validation.range=必须在{min}到{max}之间
validation.email=邮箱格式不正确
validation.mobile=手机号格式不正确
validation.idcard=身份证号格式不正确

# 业务消息
user.notFound=用户不存在
user.disabled=用户已禁用
user.locked=用户已锁定
user.expired=用户已过期
user.passwordExpired=密码已过期
user.passwordError=密码错误
user.oldPasswordError=原密码错误
tenant.notFound=租户不存在
tenant.disabled=租户已禁用
tenant.expired=租户已过期 