package com.rega.erp.common.i18n.exception;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.rega.erp.common.i18n.util.I18nExceptionUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import jakarta.validation.ConstraintViolationException;

import java.util.HashMap;
import java.util.Map;

/**
 * 国际化全局异常处理器
 * 当存在国际化模块时，优先使用此异常处理器
 * 提供国际化的异常消息处理
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
@ConditionalOnClass(name = "com.rega.erp.common.i18n.service.I18nService")
@Order(-1) // 优先级高于普通的异常处理器
public class I18nGlobalExceptionHandler {

    private final I18nExceptionUtils i18nExceptionUtils;

    /**
     * 处理国际化异常
     *
     * @param e 国际化异常
     * @return 错误响应
     */
    @ExceptionHandler(I18nException.class)
    public Map<String, Object> handleI18nException(I18nException e) {
        log.error("I18n exception occurred: {}", e.getMessage(), e);

        Map<String, Object> result = new HashMap<>();
        
        // 设置错误码
        if (e.getCode() != null) {
            result.put("code", e.getCode());
        } else {
            result.put("code", 500);
        }

        // 获取本地化消息
        String message = i18nExceptionUtils.getLocalizedMessage(e);
        result.put("message", message);
        result.put("success", false);

        return result;
    }

    /**
     * 处理参数校验异常
     *
     * @param e 参数校验异常
     * @return 错误响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Map<String, Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error("Method argument not valid exception occurred: {}", e.getMessage(), e);

        Map<String, Object> result = new HashMap<>();
        result.put("code", 400);
        
        String message = i18nExceptionUtils.getLocalizedMessage(e);
        result.put("message", message);
        result.put("success", false);

        return result;
    }

    /**
     * 处理参数绑定异常
     *
     * @param e 参数绑定异常
     * @return 错误响应
     */
    @ExceptionHandler(BindException.class)
    public Map<String, Object> handleBindException(BindException e) {
        log.error("Bind exception occurred: {}", e.getMessage(), e);

        Map<String, Object> result = new HashMap<>();
        result.put("code", 400);
        
        String message = i18nExceptionUtils.getLocalizedMessage(e);
        result.put("message", message);
        result.put("success", false);

        return result;
    }

    /**
     * 处理参数验证异常
     *
     * @param e 参数验证异常
     * @return 错误响应
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public Map<String, Object> handleConstraintViolationException(ConstraintViolationException e) {
        log.error("Constraint violation exception occurred: {}", e.getMessage(), e);

        Map<String, Object> result = new HashMap<>();
        result.put("code", 400);
        
        String message = i18nExceptionUtils.getLocalizedMessage(e);
        result.put("message", message);
        result.put("success", false);

        return result;
    }

    /**
     * 处理其他异常
     *
     * @param e 异常
     * @return 错误响应
     */
    @ExceptionHandler(Exception.class)
    public Map<String, Object> handleException(Exception e) {
        log.error("Unexpected exception occurred: {}", e.getMessage(), e);

        Map<String, Object> result = new HashMap<>();
        result.put("code", 500);
        
        String message = i18nExceptionUtils.getCommonMessage("common.error", "系统异常，请联系管理员");
        result.put("message", message);
        result.put("success", false);

        return result;
    }
}
