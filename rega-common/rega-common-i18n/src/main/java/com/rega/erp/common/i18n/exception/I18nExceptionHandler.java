package com.rega.erp.common.i18n.exception;

import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.rega.erp.common.i18n.service.I18nService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 国际化异常处理器
 * 处理国际化异常并返回本地化的错误消息
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class I18nExceptionHandler {

    private final I18nService i18nService;
    private final MessageSource messageSource;

    /**
     * 处理国际化异常
     *
     * @param e 国际化异常
     * @return 错误响应
     */
    @ExceptionHandler(I18nException.class)
    public Map<String, Object> handleI18nException(I18nException e) {
        log.error("I18n exception occurred: {}", e.getMessage(), e);

        Map<String, Object> result = new HashMap<>();
        
        // 设置错误码
        if (e.getCode() != null) {
            result.put("code", e.getCode());
        } else {
            result.put("code", 500);
        }

        // 获取本地化消息
        String message = getLocalizedMessage(e);
        result.put("message", message);
        result.put("success", false);

        return result;
    }

    /**
     * 获取本地化消息
     *
     * @param e 国际化异常
     * @return 本地化消息
     */
    private String getLocalizedMessage(I18nException e) {
        try {
            String messageKey = e.getMessageKey();
            Object[] args = e.getArgs();
            String defaultMessage = e.getDefaultMessage();

            // 如果有默认消息，使用默认消息作为fallback
            if (defaultMessage != null) {
                return i18nService.getMessage(messageKey, args, defaultMessage);
            } else {
                return i18nService.getMessage(messageKey, args);
            }
        } catch (Exception ex) {
            log.warn("Failed to get localized message for key: {}, using original message", 
                    e.getMessageKey(), ex);
            return e.getMessage();
        }
    }

    /**
     * 获取当前语言环境
     *
     * @return 当前语言环境
     */
    private Locale getCurrentLocale() {
        return LocaleContextHolder.getLocale();
    }
}
