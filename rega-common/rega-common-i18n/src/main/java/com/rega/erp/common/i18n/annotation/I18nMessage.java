package com.rega.erp.common.i18n.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 国际化消息注解
 * 用于标记需要进行国际化处理的消息
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface I18nMessage {

    /**
     * 消息键
     *
     * @return 消息键
     */
    String value() default "";

    /**
     * 消息键
     *
     * @return 消息键
     */
    String key() default "";

    /**
     * 默认消息
     *
     * @return 默认消息
     */
    String defaultMessage() default "";

    /**
     * 参数
     *
     * @return 参数数组
     */
    String[] args() default {};
}
