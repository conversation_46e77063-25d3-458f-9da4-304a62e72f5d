package com.rega.erp.common.i18n.util;

import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import com.rega.erp.common.i18n.service.I18nService;

import java.util.Locale;

/**
 * 国际化工具类
 *
 * <AUTHOR>
 */
@Component
public class I18nUtils {

    private static I18nService i18nService;

    public I18nUtils(I18nService i18nService) {
        I18nUtils.i18nService = i18nService;
    }

    /**
     * 获取国际化消息
     *
     * @param code 消息键
     * @return 国际化消息
     */
    public static String getMessage(String code) {
        if (i18nService == null) {
            return code;
        }
        return i18nService.getMessage(code);
    }

    /**
     * 获取国际化消息
     *
     * @param code 消息键
     * @param args 参数
     * @return 国际化消息
     */
    public static String getMessage(String code, Object[] args) {
        if (i18nService == null) {
            return code;
        }
        return i18nService.getMessage(code, args);
    }

    /**
     * 获取国际化消息
     *
     * @param code           消息键
     * @param args           参数
     * @param defaultMessage 默认消息
     * @return 国际化消息
     */
    public static String getMessage(String code, Object[] args, String defaultMessage) {
        if (i18nService == null) {
            return defaultMessage;
        }
        return i18nService.getMessage(code, args, defaultMessage);
    }
} 