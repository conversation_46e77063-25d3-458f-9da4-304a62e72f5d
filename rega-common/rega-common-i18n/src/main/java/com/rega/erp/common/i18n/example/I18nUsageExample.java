package com.rega.erp.common.i18n.example;

import org.springframework.stereotype.Component;

import com.rega.erp.common.i18n.constant.I18nConstants;
import com.rega.erp.common.i18n.exception.I18nException;
import com.rega.erp.common.i18n.service.I18nService;
import com.rega.erp.common.i18n.util.I18nUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 国际化使用示例
 * 展示如何在项目中使用国际化功能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class I18nUsageExample {

    private final I18nService i18nService;

    /**
     * 示例1：基本使用方式
     */
    public void basicUsage() {
        // 方式1：通过服务获取消息
        String message1 = i18nService.getMessage(I18nConstants.Common.SUCCESS);
        log.info("Success message: {}", message1);

        // 方式2：通过工具类获取消息
        String message2 = I18nUtils.getMessage(I18nConstants.User.NOT_FOUND);
        log.info("User not found message: {}", message2);

        // 方式3：带参数的消息
        String message3 = i18nService.getMessage(I18nConstants.Validation.SIZE, new Object[]{1, 10});
        log.info("Validation message: {}", message3);
    }

    /**
     * 示例2：异常处理中的使用
     */
    public void exceptionUsage() {
        try {
            // 模拟业务逻辑
            validateUser("invalid_user");
        } catch (I18nException e) {
            log.error("Business exception: {}", e.getMessage());
            // 异常会被 I18nGlobalExceptionHandler 自动处理并返回国际化消息
        }
    }

    /**
     * 示例3：在业务方法中使用
     */
    public String createUser(String username) {
        // 验证用户名
        if (username == null || username.trim().isEmpty()) {
            throw new I18nException(I18nConstants.Validation.NOT_BLANK, "用户名不能为空");
        }

        // 检查用户是否已存在
        if (userExists(username)) {
            throw new I18nException(I18nConstants.User.ALREADY_EXISTS, "用户已存在");
        }

        // 创建用户逻辑...
        log.info("Creating user: {}", username);

        // 返回成功消息
        return i18nService.getMessage(I18nConstants.User.CREATE_SUCCESS);
    }

    /**
     * 示例4：带错误码的异常
     */
    public void deleteUser(Long userId) {
        if (userId == null) {
            throw new I18nException(400, I18nConstants.Common.BAD_REQUEST, "用户ID不能为空");
        }

        if (!userExists(userId)) {
            throw new I18nException(404, I18nConstants.User.NOT_FOUND, "用户不存在");
        }

        // 删除用户逻辑...
        log.info("Deleting user: {}", userId);
    }

    /**
     * 示例5：表单验证中的使用
     */
    public void validateUserForm(UserForm form) {
        if (form.getUsername() == null || form.getUsername().trim().isEmpty()) {
            throw new I18nException(
                I18nConstants.Form.FIELD_REQUIRED, 
                new Object[]{"用户名"}, 
                "用户名为必填项"
            );
        }

        if (form.getEmail() != null && !isValidEmail(form.getEmail())) {
            throw new I18nException(
                I18nConstants.Validation.EMAIL, 
                "邮箱格式不正确"
            );
        }

        if (form.getMobile() != null && !isValidMobile(form.getMobile())) {
            throw new I18nException(
                I18nConstants.Validation.MOBILE, 
                "手机号格式不正确"
            );
        }
    }

    /**
     * 示例6：工作流中的使用
     */
    public String startWorkflow(String processKey) {
        try {
            // 启动工作流逻辑...
            log.info("Starting workflow: {}", processKey);
            
            return i18nService.getMessage(I18nConstants.Workflow.START_SUCCESS);
        } catch (Exception e) {
            log.error("Failed to start workflow", e);
            throw new I18nException(
                I18nConstants.Workflow.PROCESS_INVALID, 
                "流程定义无效"
            );
        }
    }

    /**
     * 示例7：文件操作中的使用
     */
    public String uploadFile(String fileName, byte[] content) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new I18nException(I18nConstants.File.NAME_INVALID, "文件名无效");
        }

        if (content == null || content.length == 0) {
            throw new I18nException(I18nConstants.Common.BAD_REQUEST, "文件内容不能为空");
        }

        // 检查文件大小
        if (content.length > 10 * 1024 * 1024) { // 10MB
            throw new I18nException(I18nConstants.File.SIZE_EXCEEDED, "文件大小超过限制");
        }

        // 上传文件逻辑...
        log.info("Uploading file: {}", fileName);

        return i18nService.getMessage(I18nConstants.File.UPLOAD_SUCCESS);
    }

    // 辅助方法
    private void validateUser(String username) {
        if ("invalid_user".equals(username)) {
            throw new I18nException(I18nConstants.User.NOT_FOUND, "用户不存在");
        }
    }

    private boolean userExists(String username) {
        // 模拟检查用户是否存在
        return "existing_user".equals(username);
    }

    private boolean userExists(Long userId) {
        // 模拟检查用户是否存在
        return userId != null && userId > 0;
    }

    private boolean isValidEmail(String email) {
        // 简单的邮箱验证
        return email != null && email.contains("@") && email.contains(".");
    }

    private boolean isValidMobile(String mobile) {
        // 简单的手机号验证
        return mobile != null && mobile.matches("^1[3-9]\\d{9}$");
    }

    /**
     * 用户表单类
     */
    public static class UserForm {
        private String username;
        private String email;
        private String mobile;

        // Getters and setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getMobile() { return mobile; }
        public void setMobile(String mobile) { this.mobile = mobile; }
    }
}
