package com.rega.erp.common.i18n.service;

import java.util.Locale;

/**
 * 国际化服务接口
 *
 * <AUTHOR>
 */
public interface I18nService {

    /**
     * 获取国际化消息
     *
     * @param code 消息键
     * @return 国际化消息
     */
    String getMessage(String code);

    /**
     * 获取国际化消息
     *
     * @param code 消息键
     * @param args 参数
     * @return 国际化消息
     */
    String getMessage(String code, Object[] args);

    /**
     * 获取国际化消息
     *
     * @param code           消息键
     * @param args           参数
     * @param defaultMessage 默认消息
     * @return 国际化消息
     */
    String getMessage(String code, Object[] args, String defaultMessage);

    /**
     * 获取指定语言环境的国际化消息
     *
     * @param code   消息键
     * @param locale 语言环境
     * @return 国际化消息
     */
    String getMessage(String code, Locale locale);

    /**
     * 获取指定语言环境的国际化消息
     *
     * @param code   消息键
     * @param args   参数
     * @param locale 语言环境
     * @return 国际化消息
     */
    String getMessage(String code, Object[] args, Locale locale);

    /**
     * 获取指定语言环境的国际化消息
     *
     * @param code           消息键
     * @param args           参数
     * @param defaultMessage 默认消息
     * @param locale         语言环境
     * @return 国际化消息
     */
    String getMessage(String code, Object[] args, String defaultMessage, Locale locale);

    /**
     * 检查消息键是否存在
     *
     * @param code 消息键
     * @return 是否存在
     */
    boolean hasMessage(String code);

    /**
     * 检查指定语言环境下消息键是否存在
     *
     * @param code   消息键
     * @param locale 语言环境
     * @return 是否存在
     */
    boolean hasMessage(String code, Locale locale);

    /**
     * 获取当前语言环境
     *
     * @return 当前语言环境
     */
    Locale getCurrentLocale();
}