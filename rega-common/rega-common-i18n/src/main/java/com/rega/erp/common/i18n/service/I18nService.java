package com.rega.erp.common.i18n.service;

/**
 * 国际化服务接口
 *
 * <AUTHOR>
 */
public interface I18nService {

    /**
     * 获取国际化消息
     *
     * @param code 消息键
     * @return 国际化消息
     */
    String getMessage(String code);

    /**
     * 获取国际化消息
     *
     * @param code 消息键
     * @param args 参数
     * @return 国际化消息
     */
    String getMessage(String code, Object[] args);

    /**
     * 获取国际化消息
     *
     * @param code           消息键
     * @param args           参数
     * @param defaultMessage 默认消息
     * @return 国际化消息
     */
    String getMessage(String code, Object[] args, String defaultMessage);
} 