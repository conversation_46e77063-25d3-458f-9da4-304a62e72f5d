package com.rega.erp.common.i18n.constant;

/**
 * 国际化消息常量
 *
 * <AUTHOR>
 */
public class I18nConstants {

    /**
     * 通用消息
     */
    public static class Common {
        public static final String SUCCESS = "common.success";
        public static final String FAIL = "common.fail";
        public static final String FAILED = "common.failed";
        public static final String ERROR = "common.error";
        public static final String UNAUTHORIZED = "common.unauthorized";
        public static final String FORBIDDEN = "common.forbidden";
        public static final String NOT_FOUND = "common.not.found";
        public static final String METHOD_NOT_ALLOWED = "common.method.not.allowed";
        public static final String BAD_REQUEST = "common.bad.request";
        public static final String TIMEOUT = "common.timeout";
        public static final String SERVER_BUSY = "common.serverBusy";
        public static final String CONFLICT = "common.conflict";
        public static final String SERVER_ERROR = "common.server.error";
        public static final String SERVICE_UNAVAILABLE = "common.service.unavailable";
    }

    /**
     * 验证消息
     */
    public static class Validation {
        public static final String NOT_NULL = "validation.notNull";
        public static final String NOT_BLANK = "validation.notBlank";
        public static final String NOT_EMPTY = "validation.notEmpty";
        public static final String SIZE = "validation.size";
        public static final String LENGTH = "validation.length";
        public static final String RANGE = "validation.range";
        public static final String MIN = "validation.min";
        public static final String MAX = "validation.max";
        public static final String EMAIL = "validation.email";
        public static final String MOBILE = "validation.mobile";
        public static final String PHONE = "validation.phone";
        public static final String IDCARD = "validation.idcard";
        public static final String URL = "validation.url";
        public static final String DATE = "validation.date";
        public static final String NUMBER = "validation.number";
        public static final String POSITIVE = "validation.positive";
        public static final String NEGATIVE = "validation.negative";
        public static final String PATTERN = "validation.pattern";
        public static final String FUTURE = "validation.future";
        public static final String PAST = "validation.past";
    }

    /**
     * 认证相关
     */
    public static class Auth {
        public static final String LOGIN_SUCCESS = "auth.login.success";
        public static final String LOGIN_FAILED = "auth.login.failed";
        public static final String LOGOUT_SUCCESS = "auth.logout.success";
        public static final String TOKEN_EXPIRED = "auth.token.expired";
        public static final String TOKEN_INVALID = "auth.token.invalid";
        public static final String CAPTCHA_INCORRECT = "auth.captcha.incorrect";
        public static final String CAPTCHA_EXPIRED = "auth.captcha.expired";
        public static final String ACCOUNT_LOCKED = "auth.account.locked";
        public static final String ACCOUNT_DISABLED = "auth.account.disabled";
        public static final String PASSWORD_INCORRECT = "auth.password.incorrect";
        public static final String PASSWORD_EXPIRED = "auth.password.expired";
        public static final String PERMISSION_DENIED = "auth.permission.denied";
    }

    /**
     * 用户相关
     */
    public static class User {
        public static final String NOT_FOUND = "user.not.found";
        public static final String DISABLED = "user.disabled";
        public static final String LOCKED = "user.locked";
        public static final String EXPIRED = "user.expired";
        public static final String PASSWORD_EXPIRED = "user.passwordExpired";
        public static final String PASSWORD_ERROR = "user.passwordError";
        public static final String OLD_PASSWORD_ERROR = "user.oldPasswordError";
        public static final String ALREADY_EXISTS = "user.already.exists";
        public static final String CREATE_SUCCESS = "user.create.success";
        public static final String UPDATE_SUCCESS = "user.update.success";
        public static final String DELETE_SUCCESS = "user.delete.success";
        public static final String ENABLE_SUCCESS = "user.enable.success";
        public static final String DISABLE_SUCCESS = "user.disable.success";
        public static final String RESET_PASSWORD_SUCCESS = "user.reset.password.success";
    }

    /**
     * 租户相关
     */
    public static class Tenant {
        public static final String NOT_FOUND = "tenant.not.found";
        public static final String DISABLED = "tenant.disabled";
        public static final String EXPIRED = "tenant.expired";
        public static final String ALREADY_EXISTS = "tenant.already.exists";
        public static final String CREATE_SUCCESS = "tenant.create.success";
        public static final String UPDATE_SUCCESS = "tenant.update.success";
        public static final String DELETE_SUCCESS = "tenant.delete.success";
        public static final String ENABLE_SUCCESS = "tenant.enable.success";
        public static final String DISABLE_SUCCESS = "tenant.disable.success";
    }

    /**
     * 角色相关
     */
    public static class Role {
        public static final String NOT_FOUND = "role.not.found";
        public static final String ALREADY_EXISTS = "role.already.exists";
        public static final String CREATE_SUCCESS = "role.create.success";
        public static final String UPDATE_SUCCESS = "role.update.success";
        public static final String DELETE_SUCCESS = "role.delete.success";
        public static final String ASSIGN_SUCCESS = "role.assign.success";
        public static final String REVOKE_SUCCESS = "role.revoke.success";
    }

    /**
     * 权限相关
     */
    public static class Permission {
        public static final String NOT_FOUND = "permission.not.found";
        public static final String ALREADY_EXISTS = "permission.already.exists";
        public static final String CREATE_SUCCESS = "permission.create.success";
        public static final String UPDATE_SUCCESS = "permission.update.success";
        public static final String DELETE_SUCCESS = "permission.delete.success";
        public static final String GRANT_SUCCESS = "permission.grant.success";
        public static final String REVOKE_SUCCESS = "permission.revoke.success";
    }

    /**
     * 菜单相关
     */
    public static class Menu {
        public static final String NOT_FOUND = "menu.not.found";
        public static final String ALREADY_EXISTS = "menu.already.exists";
        public static final String CREATE_SUCCESS = "menu.create.success";
        public static final String UPDATE_SUCCESS = "menu.update.success";
        public static final String DELETE_SUCCESS = "menu.delete.success";
        public static final String HAS_CHILDREN = "menu.has.children";
    }

    /**
     * 部门相关
     */
    public static class Dept {
        public static final String NOT_FOUND = "dept.not.found";
        public static final String ALREADY_EXISTS = "dept.already.exists";
        public static final String CREATE_SUCCESS = "dept.create.success";
        public static final String UPDATE_SUCCESS = "dept.update.success";
        public static final String DELETE_SUCCESS = "dept.delete.success";
        public static final String HAS_CHILDREN = "dept.has.children";
        public static final String HAS_USERS = "dept.has.users";
    }

    /**
     * 岗位相关
     */
    public static class Post {
        public static final String NOT_FOUND = "post.not.found";
        public static final String ALREADY_EXISTS = "post.already.exists";
        public static final String CREATE_SUCCESS = "post.create.success";
        public static final String UPDATE_SUCCESS = "post.update.success";
        public static final String DELETE_SUCCESS = "post.delete.success";
        public static final String HAS_USERS = "post.has.users";
    }

    /**
     * 表单相关
     */
    public static class Form {
        public static final String NOT_FOUND = "form.not.found";
        public static final String ALREADY_EXISTS = "form.already.exists";
        public static final String CREATE_SUCCESS = "form.create.success";
        public static final String UPDATE_SUCCESS = "form.update.success";
        public static final String DELETE_SUCCESS = "form.delete.success";
        public static final String PUBLISH_SUCCESS = "form.publish.success";
        public static final String UNPUBLISH_SUCCESS = "form.unpublish.success";
        public static final String DATA_SUBMIT_SUCCESS = "form.data.submit.success";
        public static final String DATA_UPDATE_SUCCESS = "form.data.update.success";
        public static final String DATA_DELETE_SUCCESS = "form.data.delete.success";
        public static final String SCHEMA_INVALID = "form.schema.invalid";
        public static final String DATA_NOT_FOUND = "form.data.not.found";
        public static final String FIELD_REQUIRED = "form.field.required";
        public static final String FIELD_INVALID = "form.field.invalid";
    }

    /**
     * 工作流相关
     */
    public static class Workflow {
        public static final String NOT_FOUND = "workflow.not.found";
        public static final String ALREADY_EXISTS = "workflow.already.exists";
        public static final String CREATE_SUCCESS = "workflow.create.success";
        public static final String UPDATE_SUCCESS = "workflow.update.success";
        public static final String DELETE_SUCCESS = "workflow.delete.success";
        public static final String DEPLOY_SUCCESS = "workflow.deploy.success";
        public static final String UNDEPLOY_SUCCESS = "workflow.undeploy.success";
        public static final String START_SUCCESS = "workflow.start.success";
        public static final String SUSPEND_SUCCESS = "workflow.suspend.success";
        public static final String ACTIVATE_SUCCESS = "workflow.activate.success";
        public static final String TASK_COMPLETE_SUCCESS = "workflow.task.complete.success";
        public static final String TASK_REJECT_SUCCESS = "workflow.task.reject.success";
        public static final String TASK_TRANSFER_SUCCESS = "workflow.task.transfer.success";
        public static final String TASK_DELEGATE_SUCCESS = "workflow.task.delegate.success";
        public static final String TASK_CLAIM_SUCCESS = "workflow.task.claim.success";
        public static final String PROCESS_INVALID = "workflow.process.invalid";
        public static final String TASK_NOT_FOUND = "workflow.task.not.found";
        public static final String INSTANCE_NOT_FOUND = "workflow.instance.not.found";
        public static final String DEFINITION_NOT_FOUND = "workflow.definition.not.found";
    }

    /**
     * 报表相关
     */
    public static class Report {
        public static final String NOT_FOUND = "report.not.found";
        public static final String ALREADY_EXISTS = "report.already.exists";
        public static final String CREATE_SUCCESS = "report.create.success";
        public static final String UPDATE_SUCCESS = "report.update.success";
        public static final String DELETE_SUCCESS = "report.delete.success";
        public static final String GENERATE_SUCCESS = "report.generate.success";
        public static final String EXPORT_SUCCESS = "report.export.success";
        public static final String TEMPLATE_INVALID = "report.template.invalid";
        public static final String DATA_EMPTY = "report.data.empty";
        public static final String PARAMETER_REQUIRED = "report.parameter.required";
    }

    /**
     * 文件相关
     */
    public static class File {
        public static final String NOT_FOUND = "file.not.found";
        public static final String UPLOAD_SUCCESS = "file.upload.success";
        public static final String UPLOAD_FAILED = "file.upload.failed";
        public static final String DOWNLOAD_SUCCESS = "file.download.success";
        public static final String DOWNLOAD_FAILED = "file.download.failed";
        public static final String DELETE_SUCCESS = "file.delete.success";
        public static final String DELETE_FAILED = "file.delete.failed";
        public static final String SIZE_EXCEEDED = "file.size.exceeded";
        public static final String TYPE_NOT_SUPPORTED = "file.type.not.supported";
        public static final String NAME_INVALID = "file.name.invalid";
    }

    /**
     * 数据字典相关
     */
    public static class Dict {
        public static final String NOT_FOUND = "dict.not.found";
        public static final String ALREADY_EXISTS = "dict.already.exists";
        public static final String CREATE_SUCCESS = "dict.create.success";
        public static final String UPDATE_SUCCESS = "dict.update.success";
        public static final String DELETE_SUCCESS = "dict.delete.success";
        public static final String TYPE_NOT_FOUND = "dict.type.not.found";
        public static final String TYPE_ALREADY_EXISTS = "dict.type.already.exists";
        public static final String DATA_NOT_FOUND = "dict.data.not.found";
        public static final String DATA_ALREADY_EXISTS = "dict.data.already.exists";
    }

    /**
     * 配置相关
     */
    public static class Config {
        public static final String NOT_FOUND = "config.not.found";
        public static final String ALREADY_EXISTS = "config.already.exists";
        public static final String CREATE_SUCCESS = "config.create.success";
        public static final String UPDATE_SUCCESS = "config.update.success";
        public static final String DELETE_SUCCESS = "config.delete.success";
        public static final String REFRESH_SUCCESS = "config.refresh.success";
        public static final String KEY_INVALID = "config.key.invalid";
        public static final String VALUE_INVALID = "config.value.invalid";
    }

    /**
     * 日志相关
     */
    public static class Log {
        public static final String NOT_FOUND = "log.not.found";
        public static final String CLEAR_SUCCESS = "log.clear.success";
        public static final String EXPORT_SUCCESS = "log.export.success";
        public static final String LEVEL_INVALID = "log.level.invalid";
    }

    /**
     * 缓存相关
     */
    public static class Cache {
        public static final String CLEAR_SUCCESS = "cache.clear.success";
        public static final String REFRESH_SUCCESS = "cache.refresh.success";
        public static final String KEY_NOT_FOUND = "cache.key.not.found";
    }

    /**
     * 定时任务相关
     */
    public static class Job {
        public static final String NOT_FOUND = "job.not.found";
        public static final String ALREADY_EXISTS = "job.already.exists";
        public static final String CREATE_SUCCESS = "job.create.success";
        public static final String UPDATE_SUCCESS = "job.update.success";
        public static final String DELETE_SUCCESS = "job.delete.success";
        public static final String START_SUCCESS = "job.start.success";
        public static final String PAUSE_SUCCESS = "job.pause.success";
        public static final String RESUME_SUCCESS = "job.resume.success";
        public static final String EXECUTE_SUCCESS = "job.execute.success";
        public static final String CRON_INVALID = "job.cron.invalid";
    }

    /**
     * 通知相关
     */
    public static class Notice {
        public static final String NOT_FOUND = "notice.not.found";
        public static final String CREATE_SUCCESS = "notice.create.success";
        public static final String UPDATE_SUCCESS = "notice.update.success";
        public static final String DELETE_SUCCESS = "notice.delete.success";
        public static final String SEND_SUCCESS = "notice.send.success";
        public static final String READ_SUCCESS = "notice.read.success";
        public static final String TYPE_INVALID = "notice.type.invalid";
    }

    /**
     * 消息相关
     */
    public static class Message {
        public static final String NOT_FOUND = "message.not.found";
        public static final String SEND_SUCCESS = "message.send.success";
        public static final String READ_SUCCESS = "message.read.success";
        public static final String DELETE_SUCCESS = "message.delete.success";
        public static final String TYPE_INVALID = "message.type.invalid";
    }

    /**
     * 数据库相关
     */
    public static class Database {
        public static final String CONNECTION_FAILED = "database.connection.failed";
        public static final String QUERY_FAILED = "database.query.failed";
        public static final String UPDATE_FAILED = "database.update.failed";
        public static final String TRANSACTION_FAILED = "database.transaction.failed";
        public static final String CONSTRAINT_VIOLATION = "database.constraint.violation";
        public static final String DUPLICATE_KEY = "database.duplicate.key";
    }

    /**
     * 网络相关
     */
    public static class Network {
        public static final String CONNECTION_TIMEOUT = "network.connection.timeout";
        public static final String REQUEST_FAILED = "network.request.failed";
        public static final String RESPONSE_INVALID = "network.response.invalid";
        public static final String SERVICE_UNAVAILABLE = "network.service.unavailable";
    }
}
