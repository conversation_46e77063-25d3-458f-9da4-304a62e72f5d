package com.rega.erp.common.i18n.service.impl;

import org.springframework.context.MessageSource;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.rega.erp.common.i18n.service.I18nService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Locale;

/**
 * 国际化服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class I18nServiceImpl implements I18nService {

    private final MessageSource messageSource;

    /**
     * 获取国际化消息
     *
     * @param code 消息键
     * @return 国际化消息
     */
    @Override
    public String getMessage(String code) {
        return getMessage(code, null);
    }

    /**
     * 获取国际化消息
     *
     * @param code 消息键
     * @param args 参数
     * @return 国际化消息
     */
    @Override
    public String getMessage(String code, Object[] args) {
        return getMessage(code, args, code);
    }

    /**
     * 获取国际化消息
     *
     * @param code           消息键
     * @param args           参数
     * @param defaultMessage 默认消息
     * @return 国际化消息
     */
    @Override
    public String getMessage(String code, Object[] args, String defaultMessage) {
        Locale locale = getCurrentLocale();
        return getMessage(code, args, defaultMessage, locale);
    }

    /**
     * 获取指定语言环境的国际化消息
     *
     * @param code   消息键
     * @param locale 语言环境
     * @return 国际化消息
     */
    @Override
    public String getMessage(String code, Locale locale) {
        return getMessage(code, null, code, locale);
    }

    /**
     * 获取指定语言环境的国际化消息
     *
     * @param code   消息键
     * @param args   参数
     * @param locale 语言环境
     * @return 国际化消息
     */
    @Override
    public String getMessage(String code, Object[] args, Locale locale) {
        return getMessage(code, args, code, locale);
    }

    /**
     * 获取指定语言环境的国际化消息
     *
     * @param code           消息键
     * @param args           参数
     * @param defaultMessage 默认消息
     * @param locale         语言环境
     * @return 国际化消息
     */
    @Override
    public String getMessage(String code, Object[] args, String defaultMessage, Locale locale) {
        try {
            return messageSource.getMessage(code, args, defaultMessage, locale);
        } catch (Exception e) {
            log.warn("Failed to get message for code: {}, locale: {}, using default message: {}",
                    code, locale, defaultMessage, e);
            return defaultMessage != null ? defaultMessage : code;
        }
    }

    /**
     * 检查消息键是否存在
     *
     * @param code 消息键
     * @return 是否存在
     */
    @Override
    public boolean hasMessage(String code) {
        return hasMessage(code, getCurrentLocale());
    }

    /**
     * 检查指定语言环境下消息键是否存在
     *
     * @param code   消息键
     * @param locale 语言环境
     * @return 是否存在
     */
    @Override
    public boolean hasMessage(String code, Locale locale) {
        try {
            messageSource.getMessage(code, null, locale);
            return true;
        } catch (NoSuchMessageException e) {
            return false;
        } catch (Exception e) {
            log.warn("Error checking message existence for code: {}, locale: {}", code, locale, e);
            return false;
        }
    }

    /**
     * 获取当前语言环境
     *
     * @return 当前语言环境
     */
    @Override
    public Locale getCurrentLocale() {
        Locale locale = LocaleContextHolder.getLocale();
        return locale != null ? locale : Locale.SIMPLIFIED_CHINESE;
    }
}