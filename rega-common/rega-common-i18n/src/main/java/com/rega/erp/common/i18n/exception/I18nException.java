package com.rega.erp.common.i18n.exception;

/**
 * 国际化异常
 * 支持国际化消息的异常类
 *
 * <AUTHOR>
 */
public class I18nException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 消息键
     */
    private String messageKey;

    /**
     * 消息参数
     */
    private Object[] args;

    /**
     * 默认消息
     */
    private String defaultMessage;

    /**
     * 构造函数
     *
     * @param messageKey 消息键
     */
    public I18nException(String messageKey) {
        super(messageKey);
        this.messageKey = messageKey;
    }

    /**
     * 构造函数
     *
     * @param messageKey     消息键
     * @param defaultMessage 默认消息
     */
    public I18nException(String messageKey, String defaultMessage) {
        super(defaultMessage);
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    /**
     * 构造函数
     *
     * @param messageKey 消息键
     * @param args       消息参数
     */
    public I18nException(String messageKey, Object[] args) {
        super(messageKey);
        this.messageKey = messageKey;
        this.args = args;
    }

    /**
     * 构造函数
     *
     * @param messageKey     消息键
     * @param args           消息参数
     * @param defaultMessage 默认消息
     */
    public I18nException(String messageKey, Object[] args, String defaultMessage) {
        super(defaultMessage);
        this.messageKey = messageKey;
        this.args = args;
        this.defaultMessage = defaultMessage;
    }

    /**
     * 构造函数
     *
     * @param code       错误码
     * @param messageKey 消息键
     */
    public I18nException(Integer code, String messageKey) {
        super(messageKey);
        this.code = code;
        this.messageKey = messageKey;
    }

    /**
     * 构造函数
     *
     * @param code           错误码
     * @param messageKey     消息键
     * @param defaultMessage 默认消息
     */
    public I18nException(Integer code, String messageKey, String defaultMessage) {
        super(defaultMessage);
        this.code = code;
        this.messageKey = messageKey;
        this.defaultMessage = defaultMessage;
    }

    /**
     * 构造函数
     *
     * @param code       错误码
     * @param messageKey 消息键
     * @param args       消息参数
     */
    public I18nException(Integer code, String messageKey, Object[] args) {
        super(messageKey);
        this.code = code;
        this.messageKey = messageKey;
        this.args = args;
    }

    /**
     * 构造函数
     *
     * @param code           错误码
     * @param messageKey     消息键
     * @param args           消息参数
     * @param defaultMessage 默认消息
     */
    public I18nException(Integer code, String messageKey, Object[] args, String defaultMessage) {
        super(defaultMessage);
        this.code = code;
        this.messageKey = messageKey;
        this.args = args;
        this.defaultMessage = defaultMessage;
    }

    /**
     * 构造函数
     *
     * @param messageKey 消息键
     * @param cause      异常原因
     */
    public I18nException(String messageKey, Throwable cause) {
        super(messageKey, cause);
        this.messageKey = messageKey;
    }

    /**
     * 构造函数
     *
     * @param code       错误码
     * @param messageKey 消息键
     * @param cause      异常原因
     */
    public I18nException(Integer code, String messageKey, Throwable cause) {
        super(messageKey, cause);
        this.code = code;
        this.messageKey = messageKey;
    }

    /**
     * 构造函数
     *
     * @param code           错误码
     * @param messageKey     消息键
     * @param args           消息参数
     * @param defaultMessage 默认消息
     * @param cause          异常原因
     */
    public I18nException(Integer code, String messageKey, Object[] args, String defaultMessage, Throwable cause) {
        super(defaultMessage, cause);
        this.code = code;
        this.messageKey = messageKey;
        this.args = args;
        this.defaultMessage = defaultMessage;
    }

    // Getters
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }

    public Object[] getArgs() {
        return args;
    }

    public String getDefaultMessage() {
        return defaultMessage;
    }
}
