package com.rega.erp.common.i18n.interceptor;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.Locale;

/**
 * 国际化拦截器
 * 用于处理请求中的语言设置
 *
 * <AUTHOR>
 */
@Slf4j
public class I18nInterceptor implements HandlerInterceptor {

    /**
     * 语言参数名
     */
    private static final String LANG_PARAM = "lang";

    /**
     * 语言请求头名
     */
    private static final String LANG_HEADER = "Accept-Language";

    /**
     * 默认语言
     */
    private static final Locale DEFAULT_LOCALE = Locale.SIMPLIFIED_CHINESE;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 从请求参数中获取语言设置
        String lang = request.getParameter(LANG_PARAM);
        
        // 如果参数中没有，则从请求头中获取
        if (!StringUtils.hasText(lang)) {
            lang = request.getHeader(LANG_HEADER);
        }
        
        Locale locale = parseLocale(lang);
        LocaleContextHolder.setLocale(locale);
        
        log.debug("Set locale to: {}", locale);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable Exception ex) {
        // 清理线程本地变量
        LocaleContextHolder.resetLocaleContext();
    }

    /**
     * 解析语言设置
     *
     * @param lang 语言字符串
     * @return Locale对象
     */
    private Locale parseLocale(String lang) {
        if (!StringUtils.hasText(lang)) {
            return DEFAULT_LOCALE;
        }

        try {
            // 处理常见的语言格式
            switch (lang.toLowerCase()) {
                case "zh":
                case "zh-cn":
                case "zh_cn":
                    return Locale.SIMPLIFIED_CHINESE;
                case "zh-tw":
                case "zh_tw":
                    return Locale.TRADITIONAL_CHINESE;
                case "en":
                case "en-us":
                case "en_us":
                    return Locale.US;
                case "en-gb":
                case "en_gb":
                    return Locale.UK;
                default:
                    // 尝试解析标准格式
                    if (lang.contains("-")) {
                        String[] parts = lang.split("-");
                        if (parts.length == 2) {
                            return new Locale(parts[0], parts[1]);
                        }
                    } else if (lang.contains("_")) {
                        String[] parts = lang.split("_");
                        if (parts.length == 2) {
                            return new Locale(parts[0], parts[1]);
                        }
                    }
                    return new Locale(lang);
            }
        } catch (Exception e) {
            log.warn("Failed to parse locale: {}, using default locale", lang, e);
            return DEFAULT_LOCALE;
        }
    }
}
