# 通用消息
common.success=操作成功
common.fail=操作失败
common.failed=操作失败
common.error=系统错误
common.unauthorized=未授权的访问
common.forbidden=禁止访问
common.notFound=资源不存在
common.not.found=资源不存在
common.methodNotAllowed=方法不允许
common.method.not.allowed=请求方法不允许
common.badRequest=请求参数错误
common.bad.request=请求参数错误
common.timeout=请求超时
common.serverBusy=系统繁忙，请稍后再试
common.conflict=资源冲突
common.server.error=服务器内部错误
common.service.unavailable=服务不可用

# 验证消息
validation.notNull=不能为空
validation.notBlank=不能为空
validation.notEmpty=不能为空
validation.size=长度必须在{min}到{max}之间
validation.length=长度必须为{max}
validation.range=必须在{min}到{max}之间
validation.min=不能小于{value}
validation.max=不能大于{value}
validation.email=邮箱格式不正确
validation.mobile=手机号格式不正确
validation.phone=电话号码格式不正确
validation.idcard=身份证号格式不正确
validation.url=URL格式不正确
validation.date=日期格式不正确
validation.number=必须是数字
validation.positive=必须是正数
validation.negative=必须是负数
validation.pattern=格式不正确
validation.future=必须是将来的日期
validation.past=必须是过去的日期
validation.error=验证失败

# 认证相关
auth.login.success=登录成功
auth.login.failed=登录失败
auth.logout.success=登出成功
auth.token.expired=令牌已过期
auth.token.invalid=无效的令牌
auth.captcha.incorrect=验证码错误
auth.captcha.expired=验证码已过期
auth.account.locked=账号已锁定
auth.account.disabled=账号已禁用
auth.password.incorrect=密码错误
auth.password.expired=密码已过期
auth.permission.denied=权限不足

# 用户相关
user.notFound=用户不存在
user.not.found=用户不存在
user.disabled=用户已禁用
user.locked=用户已锁定
user.expired=用户已过期
user.passwordExpired=密码已过期
user.passwordError=密码错误
user.oldPasswordError=原密码错误
user.already.exists=用户已存在
user.create.success=用户创建成功
user.update.success=用户更新成功
user.delete.success=用户删除成功
user.enable.success=用户启用成功
user.disable.success=用户禁用成功
user.reset.password.success=密码重置成功

# 租户相关
tenant.notFound=租户不存在
tenant.not.found=租户不存在
tenant.disabled=租户已禁用
tenant.expired=租户已过期
tenant.already.exists=租户已存在
tenant.create.success=租户创建成功
tenant.update.success=租户更新成功
tenant.delete.success=租户删除成功
tenant.enable.success=租户启用成功
tenant.disable.success=租户禁用成功

# 角色相关
role.not.found=角色不存在
role.already.exists=角色已存在
role.create.success=角色创建成功
role.update.success=角色更新成功
role.delete.success=角色删除成功
role.assign.success=角色分配成功
role.revoke.success=角色撤销成功

# 权限相关
permission.not.found=权限不存在
permission.already.exists=权限已存在
permission.create.success=权限创建成功
permission.update.success=权限更新成功
permission.delete.success=权限删除成功
permission.grant.success=权限授予成功
permission.revoke.success=权限撤销成功

# 菜单相关
menu.not.found=菜单不存在
menu.already.exists=菜单已存在
menu.create.success=菜单创建成功
menu.update.success=菜单更新成功
menu.delete.success=菜单删除成功
menu.has.children=菜单存在子菜单，无法删除

# 部门相关
dept.not.found=部门不存在
dept.already.exists=部门已存在
dept.create.success=部门创建成功
dept.update.success=部门更新成功
dept.delete.success=部门删除成功
dept.has.children=部门存在子部门，无法删除
dept.has.users=部门存在用户，无法删除

# 岗位相关
post.not.found=岗位不存在
post.already.exists=岗位已存在
post.create.success=岗位创建成功
post.update.success=岗位更新成功
post.delete.success=岗位删除成功
post.has.users=岗位存在用户，无法删除

# 表单相关
form.not.found=表单不存在
form.already.exists=表单已存在
form.create.success=表单创建成功
form.update.success=表单更新成功
form.delete.success=表单删除成功
form.publish.success=表单发布成功
form.unpublish.success=表单取消发布成功
form.data.submit.success=表单数据提交成功
form.data.update.success=表单数据更新成功
form.data.delete.success=表单数据删除成功
form.schema.invalid=表单架构无效
form.data.not.found=表单数据不存在
form.field.required=字段{0}为必填项
form.field.invalid=字段{0}格式不正确

# 工作流相关
workflow.not.found=工作流不存在
workflow.already.exists=工作流已存在
workflow.create.success=工作流创建成功
workflow.update.success=工作流更新成功
workflow.delete.success=工作流删除成功
workflow.deploy.success=工作流部署成功
workflow.undeploy.success=工作流取消部署成功
workflow.start.success=工作流启动成功
workflow.suspend.success=工作流挂起成功
workflow.activate.success=工作流激活成功
workflow.task.complete.success=任务完成成功
workflow.task.reject.success=任务驳回成功
workflow.task.transfer.success=任务转办成功
workflow.task.delegate.success=任务委派成功
workflow.task.claim.success=任务签收成功
workflow.process.invalid=流程定义无效
workflow.task.not.found=任务不存在
workflow.instance.not.found=流程实例不存在
workflow.definition.not.found=流程定义不存在

# 报表相关
report.not.found=报表不存在
report.already.exists=报表已存在
report.create.success=报表创建成功
report.update.success=报表更新成功
report.delete.success=报表删除成功
report.generate.success=报表生成成功
report.export.success=报表导出成功
report.template.invalid=报表模板无效
report.data.empty=报表数据为空
report.parameter.required=报表参数{0}为必填项

# 文件相关
file.not.found=文件不存在
file.upload.success=文件上传成功
file.upload.failed=文件上传失败
file.download.success=文件下载成功
file.download.failed=文件下载失败
file.delete.success=文件删除成功
file.delete.failed=文件删除失败
file.size.exceeded=文件大小超过限制
file.type.not.supported=不支持的文件类型
file.name.invalid=文件名无效

# 数据字典相关
dict.not.found=字典不存在
dict.already.exists=字典已存在
dict.create.success=字典创建成功
dict.update.success=字典更新成功
dict.delete.success=字典删除成功
dict.type.not.found=字典类型不存在
dict.type.already.exists=字典类型已存在
dict.data.not.found=字典数据不存在
dict.data.already.exists=字典数据已存在

# 配置相关
config.not.found=配置不存在
config.already.exists=配置已存在
config.create.success=配置创建成功
config.update.success=配置更新成功
config.delete.success=配置删除成功
config.refresh.success=配置刷新成功
config.key.invalid=配置键无效
config.value.invalid=配置值无效

# 日志相关
log.not.found=日志不存在
log.clear.success=日志清理成功
log.export.success=日志导出成功
log.level.invalid=日志级别无效

# 缓存相关
cache.clear.success=缓存清理成功
cache.refresh.success=缓存刷新成功
cache.key.not.found=缓存键不存在

# 定时任务相关
job.not.found=定时任务不存在
job.already.exists=定时任务已存在
job.create.success=定时任务创建成功
job.update.success=定时任务更新成功
job.delete.success=定时任务删除成功
job.start.success=定时任务启动成功
job.pause.success=定时任务暂停成功
job.resume.success=定时任务恢复成功
job.execute.success=定时任务执行成功
job.cron.invalid=Cron表达式无效

# 通知相关
notice.not.found=通知不存在
notice.create.success=通知创建成功
notice.update.success=通知更新成功
notice.delete.success=通知删除成功
notice.send.success=通知发送成功
notice.read.success=通知已读成功
notice.type.invalid=通知类型无效

# 消息相关
message.not.found=消息不存在
message.send.success=消息发送成功
message.read.success=消息已读成功
message.delete.success=消息删除成功
message.type.invalid=消息类型无效

# 数据库相关
database.connection.failed=数据库连接失败
database.query.failed=数据库查询失败
database.update.failed=数据库更新失败
database.transaction.failed=数据库事务失败
database.constraint.violation=数据库约束违反
database.duplicate.key=数据库主键重复

# 网络相关
network.connection.timeout=网络连接超时
network.request.failed=网络请求失败
network.response.invalid=网络响应无效
network.service.unavailable=网络服务不可用
