# RegaWebERP 国际化模块

## 概述

rega-common-i18n 是 RegaWebERP 系统的国际化模块，提供完整的多语言支持功能。

## 功能特性

- 🌍 多语言支持（简体中文、繁体中文、英文）
- 🔧 自动语言检测和切换
- 🎯 统一的异常处理和国际化消息
- 📝 丰富的预定义消息常量
- 🛠️ 简单易用的API接口
- 🔄 动态消息加载和缓存
- 📋 完整的验证消息支持

## 支持的语言

| 语言 | 代码 | 文件名 |
|------|------|--------|
| 简体中文 | zh-CN | messages_zh_CN.properties |
| 繁体中文 | zh-TW | messages_zh_TW.properties |
| 英文 | en-US | messages_en.properties |
| 默认 | - | messages.properties |

## 快速开始

### 1. 添加依赖

在你的模块的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>com.rega.erp</groupId>
    <artifactId>rega-common-i18n</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 基本使用

#### 通过服务获取消息

```java
@Autowired
private I18nService i18nService;

// 获取简单消息
String message = i18nService.getMessage("common.success");

// 获取带参数的消息
String message = i18nService.getMessage("validation.size", new Object[]{1, 10});

// 获取带默认消息的消息
String message = i18nService.getMessage("custom.key", null, "默认消息");
```

#### 通过工具类获取消息

```java
// 静态方法调用
String message = I18nUtils.getMessage("user.not.found");
String message = I18nUtils.getMessage("form.field.required", new Object[]{"用户名"});
```

#### 使用常量

```java
// 使用预定义常量
String message = i18nService.getMessage(I18nConstants.Common.SUCCESS);
String message = i18nService.getMessage(I18nConstants.User.NOT_FOUND);
String message = i18nService.getMessage(I18nConstants.Validation.EMAIL);
```

### 3. 异常处理

#### 抛出国际化异常

```java
// 简单异常
throw new I18nException(I18nConstants.User.NOT_FOUND);

// 带参数的异常
throw new I18nException(I18nConstants.Form.FIELD_REQUIRED, new Object[]{"用户名"});

// 带错误码的异常
throw new I18nException(404, I18nConstants.User.NOT_FOUND, "用户不存在");

// 带默认消息的异常
throw new I18nException(I18nConstants.User.NOT_FOUND, null, "用户不存在");
```

#### 自动异常处理

系统会自动处理 `I18nException` 并返回国际化的错误消息：

```json
{
  "code": 404,
  "message": "用户不存在",
  "success": false
}
```

### 4. 语言切换

#### 通过请求参数

```
GET /api/users?lang=en-US
GET /api/users?lang=zh-CN
GET /api/users?lang=zh-TW
```

#### 通过请求头

```
Accept-Language: en-US
Accept-Language: zh-CN
Accept-Language: zh-TW
```

## 消息分类

### 通用消息 (Common)
- `common.success` - 操作成功
- `common.fail` - 操作失败
- `common.error` - 系统错误
- `common.unauthorized` - 未授权
- `common.forbidden` - 禁止访问

### 验证消息 (Validation)
- `validation.notNull` - 不能为空
- `validation.notBlank` - 不能为空白
- `validation.email` - 邮箱格式不正确
- `validation.mobile` - 手机号格式不正确
- `validation.size` - 长度限制

### 业务消息
- **用户相关** (User): 用户管理相关消息
- **租户相关** (Tenant): 租户管理相关消息
- **角色相关** (Role): 角色管理相关消息
- **权限相关** (Permission): 权限管理相关消息
- **表单相关** (Form): 表单操作相关消息
- **工作流相关** (Workflow): 工作流操作相关消息
- **报表相关** (Report): 报表操作相关消息

## 高级用法

### 1. 检查消息是否存在

```java
boolean exists = i18nService.hasMessage("custom.key");
boolean exists = i18nService.hasMessage("custom.key", Locale.US);
```

### 2. 获取当前语言环境

```java
Locale currentLocale = i18nService.getCurrentLocale();
```

### 3. 指定语言环境获取消息

```java
String message = i18nService.getMessage("common.success", Locale.US);
String message = i18nService.getMessage("user.not.found", null, Locale.SIMPLIFIED_CHINESE);
```

## 配置说明

### 消息源配置

```java
@Bean
public MessageSource messageSource() {
    ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
    messageSource.setBasename("classpath:i18n/messages");
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(3600);
    messageSource.setFallbackToSystemLocale(false);
    messageSource.setUseCodeAsDefaultMessage(true);
    return messageSource;
}
```

### 语言解析器配置

```java
@Bean
public LocaleResolver localeResolver() {
    AcceptHeaderLocaleResolver localeResolver = new AcceptHeaderLocaleResolver();
    localeResolver.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
    localeResolver.setSupportedLocales(Arrays.asList(
        Locale.SIMPLIFIED_CHINESE,
        Locale.TRADITIONAL_CHINESE,
        Locale.US
    ));
    return localeResolver;
}
```

## 扩展指南

### 1. 添加新语言

1. 创建新的属性文件：`messages_ja.properties`（日语示例）
2. 翻译所有消息键
3. 在 `LocaleResolver` 中添加支持的语言
4. 在 `I18nInterceptor` 中添加语言解析逻辑

### 2. 添加新消息

1. 在所有语言的属性文件中添加新的消息键值对
2. 在 `I18nConstants` 中添加对应的常量
3. 编写测试用例验证新消息

### 3. 自定义异常处理

```java
@ExceptionHandler(CustomException.class)
public Map<String, Object> handleCustomException(CustomException e) {
    String message = i18nService.getMessage(e.getMessageKey(), e.getArgs(), e.getDefaultMessage());
    // 处理逻辑...
}
```

## 最佳实践

1. **使用常量**: 优先使用 `I18nConstants` 中定义的常量，避免硬编码消息键
2. **异常处理**: 使用 `I18nException` 抛出业务异常，系统会自动处理国际化
3. **参数化消息**: 对于需要动态内容的消息，使用参数化方式
4. **默认消息**: 为自定义消息键提供默认消息，提高系统健壮性
5. **测试覆盖**: 为新增的消息编写测试用例，确保多语言正确性

## 注意事项

- 消息文件使用 UTF-8 编码
- 参数占位符使用 `{0}`, `{1}`, `{2}` 格式
- 默认语言为简体中文
- 系统会自动缓存消息，提高性能
- 异常处理器的优先级高于普通异常处理器
