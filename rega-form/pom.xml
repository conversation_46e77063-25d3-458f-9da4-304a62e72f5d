<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.rega.erp</groupId>
        <artifactId>rega-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>rega-form</artifactId>
    <name>RegaWebERP Form</name>
    <description>RegaWebERP Dynamic Form Engine Module</description>

    <dependencies>
        <!-- 内部依赖 -->
<!--        <dependency>-->
<!--            <groupId>com.rega.erp</groupId>-->
<!--            <artifactId>rega-common-core</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.rega.erp</groupId>-->
<!--            <artifactId>rega-common-db</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.rega.erp</groupId>-->
<!--            <artifactId>rega-common-sdk</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.rega.erp</groupId>-->
<!--            <artifactId>rega-common-security</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.rega.erp</groupId>-->
<!--            <artifactId>rega-common-log</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.rega.erp</groupId>-->
<!--            <artifactId>rega-common-i18n</artifactId>-->
<!--        </dependency>-->

        <!-- Spring相关依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- JSON Schema 相关依赖 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.erosb</groupId>
            <artifactId>everit-json-schema</artifactId>
            <version>1.14.2</version>
        </dependency>

        <!-- API文档 -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 其他工具依赖 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project> 