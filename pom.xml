<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.rega.erp</groupId>
    <artifactId>rega-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>RegaWebERP</name>
    <description>Rega Web ERP System</description>

    <modules>
        <module>rega-common</module>
        <module>rega-system</module>
        <module>rega-tenant</module>
        <module>rega-form</module>
        <module>rega-workflow</module>
        <module>rega-report</module>
        <module>rega-api</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>3.1.0</spring-boot.version>
        <spring-cloud.version>2022.0.3</spring-cloud.version>
        <anyline.version>8.7.2-SNAPSHOT</anyline.version>
        <postgresql.version>42.6.0</postgresql.version>
        <sa-token.version>1.34.0</sa-token.version>
        <knife4j.version>4.1.0</knife4j.version>
        <hutool.version>5.8.18</hutool.version>
        <redisson.version>3.20.0</redisson.version>
        <jetcache.version>2.7.3</jetcache.version>
        <cosid.version>2.5.5</cosid.version>
        <lombok.version>1.18.26</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <ttl.version>2.14.2</ttl.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot 依赖 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- Spring Cloud 依赖 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Anyline ORM 核心依赖 -->
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-spring-boot-starter</artifactId>
                <version>${anyline.version}</version>
            </dependency>
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-data-jdbc</artifactId>
                <version>${anyline.version}</version>
            </dependency>
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-data-jdbc-postgresql</artifactId>
                <version>${anyline.version}</version>
            </dependency>
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-environment-spring-data-jdbc</artifactId>
                <version>${anyline.version}</version>
            </dependency>

            <!-- PostgreSQL驱动 -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>

            <!-- Sa-Token认证 -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-jwt</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${sa-token.version}</version>
            </dependency>

            <!-- Redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- JetCache -->
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis</artifactId>
                <version>${jetcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redisson</artifactId>
                <version>${jetcache.version}</version>
            </dependency>

            <!-- CosID -->
            <dependency>
                <groupId>me.ahoo.cosid</groupId>
                <artifactId>cosid-spring-boot-starter</artifactId>
                <version>${cosid.version}</version>
            </dependency>

            <!-- Knife4j API文档 -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- Hutool工具包 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- MapStruct对象映射 -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <!-- Apache Commons IO -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.11.0</version>
            </dependency>

            <!-- Apache Commons Codec -->
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.15</version>
            </dependency>

            <!-- TransmittableThreadLocal -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${ttl.version}</version>
            </dependency>

            <!-- 项目内部模块依赖 -->
            <dependency>
                <groupId>com.rega.erp</groupId>
                <artifactId>rega-common-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rega.erp</groupId>
                <artifactId>rega-common-anyline</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rega.erp</groupId>
                <artifactId>rega-common-cache</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rega.erp</groupId>
                <artifactId>rega-common-security</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rega.erp</groupId>
                <artifactId>rega-common-log</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rega.erp</groupId>
                <artifactId>rega-common-i18n</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rega.erp</groupId>
                <artifactId>rega-common-db</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rega.erp</groupId>
                <artifactId>rega-common-sdk</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 业务模块依赖 -->
            <dependency>
                <groupId>com.rega.erp</groupId>
                <artifactId>rega-system</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rega.erp</groupId>
                <artifactId>rega-tenant</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rega.erp</groupId>
                <artifactId>rega-form</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rega.erp</groupId>
                <artifactId>rega-workflow</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rega.erp</groupId>
                <artifactId>rega-report</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <repositories>
        <repository>
            <id>huawei-mirror</id>
            <name>HuaweiCloud Mirror</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </repository>
        <repository>
            <id>ali-mirror</id>
            <name>AliYun Mirror</name>
            <url>https://maven.aliyun.com/repository/public/</url>
        </repository>
        <!-- Anyline SNAPSHOT 仓库 -->
        <repository>
            <id>ossrh</id>
            <name>OSS Sonatype Snapshots</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <!-- 插件仓库配置 -->
    <pluginRepositories>
        <pluginRepository>
            <id>huawei-mirror</id>
            <name>HuaweiCloud Mirror</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </pluginRepository>
        <pluginRepository>
            <id>ali-mirror</id>
            <name>AliYun Mirror</name>
            <url>https://maven.aliyun.com/repository/public/</url>
        </pluginRepository>
    </pluginRepositories>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project> 