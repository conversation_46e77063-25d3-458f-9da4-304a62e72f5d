2025-06-17T15:28:01.638+08:00  INFO 80176 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 80176 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/classes started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent)
2025-06-17T15:28:01.647+08:00 DEBUG 80176 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-17T15:28:01.647+08:00  INFO 80176 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-17T15:28:03.211+08:00  INFO 80176 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T15:28:03.212+08:00  INFO 80176 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-17T15:28:03.288+08:00  INFO 80176 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-17T15:28:03.443+08:00  INFO 80176 --- [main] c.r.e.c.c.config.CacheAutoConfiguration  : RegaWebERP Cache Module Auto Configuration Initialized
2025-06-17T15:28:03.658+08:00  INFO 80176 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= caffeine
2025-06-17T15:28:03.665+08:00  INFO 80176 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= redisson
2025-06-17T15:28:03.781+08:00  INFO 80176 --- [main] org.redisson.Version                     : Redisson 3.20.0
2025-06-17T15:28:03.820+08:00  WARN 80176 --- [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-17T15:28:04.048+08:00  INFO 80176 --- [redisson-netty-2-7] o.r.c.pool.MasterPubSubConnectionPool    : 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-17T15:28:04.212+08:00  INFO 80176 --- [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool          : 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-17T15:28:04.261+08:00  INFO 80176 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat period at 15 MINUTES
2025-06-17T15:28:04.306+08:00  INFO 80176 --- [main] m.a.c.m.ManualMachineIdDistributor       : Distribute Remote machineState:[MachineState{machineId=1, lastTimeStamp=-1}] - instanceId:[InstanceId{instanceId=***********:80176, stable=false}] - machineBit:[10] @ namespace:[rega-api].
2025-06-17T15:28:05.584+08:00  WARN 80176 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-06-17T15:28:05.591+08:00  INFO 80176 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat canceled
2025-06-17T15:28:05.619+08:00  INFO 80176 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-17T15:28:05.648+08:00 ERROR 80176 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8000 was already in use.

Action:

Identify and stop the process that's listening on port 8000 or configure this application to listen on another port.

2025-06-17T15:28:21.685+08:00  INFO 80326 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 80326 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/classes started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent)
2025-06-17T15:28:21.686+08:00 DEBUG 80326 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-17T15:28:21.687+08:00  INFO 80326 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-17T15:28:23.826+08:00  INFO 80326 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T15:28:23.827+08:00  INFO 80326 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-17T15:28:23.906+08:00  INFO 80326 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-17T15:28:24.007+08:00  INFO 80326 --- [main] c.r.e.c.c.config.CacheAutoConfiguration  : RegaWebERP Cache Module Auto Configuration Initialized
2025-06-17T15:28:24.232+08:00  INFO 80326 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= caffeine
2025-06-17T15:28:24.237+08:00  INFO 80326 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= redisson
2025-06-17T15:28:24.316+08:00  INFO 80326 --- [main] org.redisson.Version                     : Redisson 3.20.0
2025-06-17T15:28:24.344+08:00  WARN 80326 --- [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-17T15:28:24.618+08:00  INFO 80326 --- [redisson-netty-2-5] o.r.c.pool.MasterPubSubConnectionPool    : 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-17T15:28:24.798+08:00  INFO 80326 --- [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool          : 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-17T15:28:24.872+08:00  INFO 80326 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat period at 15 MINUTES
2025-06-17T15:28:24.938+08:00  INFO 80326 --- [main] m.a.c.m.ManualMachineIdDistributor       : Distribute Remote machineState:[MachineState{machineId=1, lastTimeStamp=-1}] - instanceId:[InstanceId{instanceId=***********:80326, stable=false}] - machineBit:[10] @ namespace:[rega-api].
2025-06-17T15:28:26.307+08:00  INFO 80326 --- [main] com.rega.erp.api.RegaApiApplication      : Started RegaApiApplication in 5.16 seconds (process running for 5.73)
2025-06-17T15:28:26.343+08:00  INFO 80326 --- [main] com.zaxxer.hikari.HikariDataSource       : RegaHikariCP - Starting...
2025-06-17T15:28:26.472+08:00  INFO 80326 --- [main] com.zaxxer.hikari.pool.HikariPool        : RegaHikariCP - Added connection org.postgresql.jdbc.PgConnection@2148849f
2025-06-17T15:28:26.473+08:00  INFO 80326 --- [main] com.zaxxer.hikari.HikariDataSource       : RegaHikariCP - Start completed.
2025-06-17T15:28:26.477+08:00  INFO 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : === 数据库连接信息 ===
2025-06-17T15:28:26.477+08:00  INFO 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : 数据库产品名称: PostgreSQL
2025-06-17T15:28:26.477+08:00  INFO 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : 数据库产品版本: 17.5
2025-06-17T15:28:26.478+08:00  INFO 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : 驱动名称: PostgreSQL JDBC Driver
2025-06-17T15:28:26.478+08:00  INFO 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : 驱动版本: 42.6.0
2025-06-17T15:28:26.478+08:00  INFO 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : 数据库URL: ************************************************************************************************************************************
2025-06-17T15:28:26.478+08:00  INFO 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : 用户名: postgres
2025-06-17T15:28:26.478+08:00  INFO 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✅ 数据库连接测试成功！
2025-06-17T15:28:26.480+08:00  INFO 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : === 数据库表检查 ===
2025-06-17T15:28:26.492+08:00  WARN 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✗ 表 sys_tenant 不存在
2025-06-17T15:28:26.496+08:00  WARN 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✗ 表 sys_user 不存在
2025-06-17T15:28:26.499+08:00  WARN 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✗ 表 sys_role 不存在
2025-06-17T15:28:26.502+08:00  WARN 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✗ 表 sys_permission 不存在
2025-06-17T15:28:26.504+08:00  WARN 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✗ 表 sys_user_role 不存在
2025-06-17T15:28:26.505+08:00  WARN 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✗ 表 sys_role_permission 不存在
2025-06-17T15:28:26.506+08:00  WARN 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✗ 表 sys_config 不存在
2025-06-17T15:28:26.507+08:00  WARN 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✗ 表 sys_operation_log 不存在
2025-06-17T15:28:26.507+08:00  WARN 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : 部分核心表不存在，请执行数据库初始化脚本！
2025-06-17T15:28:26.507+08:00  INFO 80326 --- [main] com.rega.erp.api.config.DatabaseConfig   : 初始化脚本位置: src/main/resources/db/migration/
2025-06-17T15:30:00.014+08:00  INFO 80326 --- [JetCacheDefaultExecutor] c.alicp.jetcache.support.StatInfoLogger  : jetcache stat from 2025-06-17 15:28:24,871 to 2025-06-17 15:30:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-17T15:34:26.406+08:00  INFO 80326 --- [SpringApplicationShutdownHook] c.a.j.support.DefaultMetricsManager      : cache stat canceled
2025-06-17T15:34:26.424+08:00  INFO 80326 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : RegaHikariCP - Shutdown initiated...
2025-06-17T15:34:26.425+08:00  INFO 80326 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : RegaHikariCP - Shutdown completed.
2025-06-17T18:33:50.848+08:00  INFO 66147 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 66147 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/classes started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent)
2025-06-17T18:33:50.850+08:00 DEBUG 66147 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-17T18:33:50.850+08:00  INFO 66147 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-17T18:33:52.074+08:00  INFO 66147 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T18:33:52.075+08:00  INFO 66147 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-17T18:33:52.136+08:00  INFO 66147 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-17T18:33:52.233+08:00  INFO 66147 --- [main] c.r.e.c.c.config.CoreAutoConfiguration   : RegaWebERP Core Module Auto Configuration Initialized
2025-06-17T18:33:52.239+08:00  INFO 66147 --- [main] c.r.e.c.d.t.c.TenantAutoConfiguration    : RegaWebERP Tenant Module Auto Configuration Initialized
2025-06-17T18:33:52.293+08:00  INFO 66147 --- [main] c.r.e.c.c.config.CacheAutoConfiguration  : RegaWebERP Cache Module Auto Configuration Initialized
2025-06-17T18:33:52.466+08:00  INFO 66147 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= caffeine
2025-06-17T18:33:52.469+08:00  INFO 66147 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= redisson
2025-06-17T18:33:52.558+08:00  INFO 66147 --- [main] org.redisson.Version                     : Redisson 3.20.0
2025-06-17T18:33:52.594+08:00  WARN 66147 --- [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-17T18:33:52.729+08:00  INFO 66147 --- [redisson-netty-2-7] o.r.c.pool.MasterPubSubConnectionPool    : 1 connections initialized for localhost/127.0.0.1:6379
2025-06-17T18:33:52.756+08:00  INFO 66147 --- [redisson-netty-2-20] o.r.c.pool.MasterConnectionPool          : 24 connections initialized for localhost/127.0.0.1:6379
2025-06-17T18:33:52.802+08:00  INFO 66147 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat period at 15 MINUTES
2025-06-17T18:33:52.841+08:00  INFO 66147 --- [main] m.a.c.m.ManualMachineIdDistributor       : Distribute Remote machineState:[MachineState{machineId=1, lastTimeStamp=-1}] - instanceId:[InstanceId{instanceId=***********:66147, stable=false}] - machineBit:[10] @ namespace:[rega-api].
2025-06-17T18:33:53.808+08:00  INFO 66147 --- [main] com.rega.erp.api.RegaApiApplication      : Started RegaApiApplication in 3.289 seconds (process running for 3.97)
2025-06-17T18:33:53.836+08:00  INFO 66147 --- [main] com.zaxxer.hikari.HikariDataSource       : RegaHikariCP - Starting...
2025-06-17T18:33:53.937+08:00  INFO 66147 --- [main] com.zaxxer.hikari.pool.HikariPool        : RegaHikariCP - Added connection org.postgresql.jdbc.PgConnection@4bd1b07d
2025-06-17T18:33:53.942+08:00  INFO 66147 --- [main] com.zaxxer.hikari.HikariDataSource       : RegaHikariCP - Start completed.
2025-06-17T18:33:53.946+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : === 数据库连接信息 ===
2025-06-17T18:33:53.946+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : 数据库产品名称: PostgreSQL
2025-06-17T18:33:53.946+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : 数据库产品版本: 17.5
2025-06-17T18:33:53.946+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : 驱动名称: PostgreSQL JDBC Driver
2025-06-17T18:33:53.946+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : 驱动版本: 42.6.0
2025-06-17T18:33:53.946+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : 数据库URL: ************************************************************************************************************************************
2025-06-17T18:33:53.946+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : 用户名: postgres
2025-06-17T18:33:53.946+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✅ 数据库连接测试成功！
2025-06-17T18:33:53.949+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : === 数据库表检查 ===
2025-06-17T18:33:53.955+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✓ 表 sys_tenant 存在
2025-06-17T18:33:53.958+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✓ 表 sys_user 存在
2025-06-17T18:33:53.959+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✓ 表 sys_role 存在
2025-06-17T18:33:53.960+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✓ 表 sys_permission 存在
2025-06-17T18:33:53.961+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✓ 表 sys_user_role 存在
2025-06-17T18:33:53.963+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✓ 表 sys_role_permission 存在
2025-06-17T18:33:53.964+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✓ 表 sys_config 存在
2025-06-17T18:33:53.965+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : ✓ 表 sys_operation_log 存在
2025-06-17T18:33:53.965+08:00  INFO 66147 --- [main] com.rega.erp.api.config.DatabaseConfig   : 所有核心表都已存在，数据库初始化完成！
2025-06-17T18:34:17.679+08:00  INFO 66147 --- [SpringApplicationShutdownHook] c.a.j.support.DefaultMetricsManager      : cache stat canceled
2025-06-17T18:34:17.696+08:00  INFO 66147 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : RegaHikariCP - Shutdown initiated...
2025-06-17T18:34:17.698+08:00  INFO 66147 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : RegaHikariCP - Shutdown completed.
