2025-06-16T15:42:42.419+08:00  INFO 77524 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 77524 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/classes started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent)
2025-06-16T15:42:42.421+08:00 DEBUG 77524 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-16T15:42:42.423+08:00  INFO 77524 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-16T15:42:45.162+08:00  INFO 77524 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:42:45.162+08:00  INFO 77524 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-16T15:42:45.257+08:00  INFO 77524 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-16T15:42:45.384+08:00  INFO 77524 --- [main] c.r.e.c.c.config.CacheAutoConfiguration  : RegaWebERP Cache Module Auto Configuration Initialized
2025-06-16T15:42:45.731+08:00  INFO 77524 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= caffeine
2025-06-16T15:42:45.737+08:00  INFO 77524 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= redisson
2025-06-16T15:42:45.866+08:00  INFO 77524 --- [main] org.redisson.Version                     : Redisson 3.20.0
2025-06-16T15:42:45.909+08:00  WARN 77524 --- [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-16T15:42:46.151+08:00  INFO 77524 --- [redisson-netty-2-6] o.r.c.pool.MasterPubSubConnectionPool    : 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-16T15:42:46.181+08:00  INFO 77524 --- [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool          : 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-16T15:42:46.221+08:00  INFO 77524 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat period at 15 MINUTES
2025-06-16T15:42:46.263+08:00  INFO 77524 --- [main] m.a.c.m.ManualMachineIdDistributor       : Distribute Remote machineState:[MachineState{machineId=1, lastTimeStamp=-1}] - instanceId:[InstanceId{instanceId=***********:77524, stable=false}] - machineBit:[10] @ namespace:[rega-api].
2025-06-16T15:42:47.594+08:00  WARN 77524 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-06-16T15:42:47.608+08:00  INFO 77524 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat canceled
2025-06-16T15:42:47.641+08:00  INFO 77524 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-16T15:42:47.688+08:00 ERROR 77524 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8000 was already in use.

Action:

Identify and stop the process that's listening on port 8000 or configure this application to listen on another port.

