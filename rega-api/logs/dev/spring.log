2025-06-16T15:30:07.278+08:00  INFO 72547 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 72547 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/rega-api-1.0.0-SNAPSHOT.jar started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api)
2025-06-16T15:30:07.280+08:00 DEBUG 72547 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-16T15:30:07.280+08:00  INFO 72547 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-16T15:30:08.417+08:00 ERROR 72547 --- [main] o.s.boot.SpringApplication               : Application run failed

java.lang.IllegalStateException: Error processing condition on com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration.knife4jOperationCustomizer
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:60) ~[spring-boot-autoconfigure-3.1.0.jar!/:3.1.0]
	at org.springframework.context.annotation.ConditionEvaluator.shouldSkip(ConditionEvaluator.java:108) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForBeanMethod(ConfigurationClassBeanDefinitionReader.java:183) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:144) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:120) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:427) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:287) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:344) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:115) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:771) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:589) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:733) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:435) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:311) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1305) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1294) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at com.rega.erp.api.RegaApiApplication.main(RegaApiApplication.java:17) ~[classes!/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:95) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration] from ClassLoader [org.springframework.boot.loader.LaunchedURLClassLoader@5b480cf9]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:483) ~[spring-core-6.0.9.jar!/:6.0.9]
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:360) ~[spring-core-6.0.9.jar!/:6.0.9]
	at org.springframework.util.ReflectionUtils.getUniqueDeclaredMethods(ReflectionUtils.java:417) ~[spring-core-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.lambda$getTypeForFactoryMethod$1(AbstractAutowireCapableBeanFactory.java:748) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1740) ~[na:na]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryMethod(AbstractAutowireCapableBeanFactory.java:747) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:680) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:651) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.isFactoryBean(AbstractBeanFactory.java:1632) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:560) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:532) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.collectBeanNamesForType(OnBeanCondition.java:246) ~[spring-boot-autoconfigure-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:239) ~[spring-boot-autoconfigure-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:229) ~[spring-boot-autoconfigure-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchingBeans(OnBeanCondition.java:182) ~[spring-boot-autoconfigure-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchOutcome(OnBeanCondition.java:157) ~[spring-boot-autoconfigure-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:47) ~[spring-boot-autoconfigure-3.1.0.jar!/:3.1.0]
	... 25 common frames omitted
Caused by: java.lang.NoClassDefFoundError: javax/servlet/Filter
	at java.base/java.lang.ClassLoader.defineClass1(Native Method) ~[na:na]
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1012) ~[na:na]
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:150) ~[na:na]
	at java.base/java.net.URLClassLoader.defineClass(URLClassLoader.java:524) ~[na:na]
	at java.base/java.net.URLClassLoader$1.run(URLClassLoader.java:427) ~[na:na]
	at java.base/java.net.URLClassLoader$1.run(URLClassLoader.java:421) ~[na:na]
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712) ~[na:na]
	at java.base/java.net.URLClassLoader.findClass(URLClassLoader.java:420) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:587) ~[na:na]
	at org.springframework.boot.loader.LaunchedURLClassLoader.loadClass(LaunchedURLClassLoader.java:149) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520) ~[na:na]
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method) ~[na:na]
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402) ~[na:na]
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2504) ~[na:na]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:465) ~[spring-core-6.0.9.jar!/:6.0.9]
	... 41 common frames omitted
Caused by: java.lang.ClassNotFoundException: javax.servlet.Filter
	at java.base/java.net.URLClassLoader.findClass(URLClassLoader.java:445) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:587) ~[na:na]
	at org.springframework.boot.loader.LaunchedURLClassLoader.loadClass(LaunchedURLClassLoader.java:149) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520) ~[na:na]
	... 56 common frames omitted

2025-06-16T15:30:08.432+08:00  WARN 72547 --- [main] o.s.boot.SpringApplication               : Unable to close ApplicationContext

java.lang.IllegalStateException: Failed to introspect Class [com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration] from ClassLoader [org.springframework.boot.loader.LaunchedURLClassLoader@5b480cf9]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:483) ~[spring-core-6.0.9.jar!/:6.0.9]
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:360) ~[spring-core-6.0.9.jar!/:6.0.9]
	at org.springframework.util.ReflectionUtils.getUniqueDeclaredMethods(ReflectionUtils.java:417) ~[spring-core-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.lambda$getTypeForFactoryMethod$1(AbstractAutowireCapableBeanFactory.java:748) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1740) ~[na:na]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryMethod(AbstractAutowireCapableBeanFactory.java:747) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:680) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:651) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.isFactoryBean(AbstractBeanFactory.java:1632) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:560) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:532) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:659) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:651) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1302) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.boot.SpringApplication.getExitCodeFromMappedException(SpringApplication.java:866) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.getExitCodeFromException(SpringApplication.java:854) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.handleExitCode(SpringApplication.java:841) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:781) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:324) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1305) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1294) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at com.rega.erp.api.RegaApiApplication.main(RegaApiApplication.java:17) ~[classes!/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:95) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
Caused by: java.lang.NoClassDefFoundError: javax/servlet/Filter
	at java.base/java.lang.ClassLoader.defineClass1(Native Method) ~[na:na]
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1012) ~[na:na]
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:150) ~[na:na]
	at java.base/java.net.URLClassLoader.defineClass(URLClassLoader.java:524) ~[na:na]
	at java.base/java.net.URLClassLoader$1.run(URLClassLoader.java:427) ~[na:na]
	at java.base/java.net.URLClassLoader$1.run(URLClassLoader.java:421) ~[na:na]
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712) ~[na:na]
	at java.base/java.net.URLClassLoader.findClass(URLClassLoader.java:420) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:587) ~[na:na]
	at org.springframework.boot.loader.LaunchedURLClassLoader.loadClass(LaunchedURLClassLoader.java:149) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520) ~[na:na]
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method) ~[na:na]
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402) ~[na:na]
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2504) ~[na:na]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:465) ~[spring-core-6.0.9.jar!/:6.0.9]
	... 29 common frames omitted
Caused by: java.lang.ClassNotFoundException: javax.servlet.Filter
	at java.base/java.net.URLClassLoader.findClass(URLClassLoader.java:445) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:587) ~[na:na]
	at org.springframework.boot.loader.LaunchedURLClassLoader.loadClass(LaunchedURLClassLoader.java:149) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520) ~[na:na]
	... 44 common frames omitted

2025-06-16T15:30:56.533+08:00  INFO 72869 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 72869 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/rega-api-1.0.0-SNAPSHOT.jar started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api)
2025-06-16T15:30:56.536+08:00 DEBUG 72869 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-16T15:30:56.537+08:00  INFO 72869 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-16T15:30:58.031+08:00  INFO 72869 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:30:58.031+08:00  INFO 72869 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-16T15:30:58.126+08:00  INFO 72869 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-16T15:30:58.285+08:00  INFO 72869 --- [main] c.r.e.c.c.config.CacheAutoConfiguration  : RegaWebERP Cache Module Auto Configuration Initialized
2025-06-16T15:30:58.287+08:00  WARN 72869 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cacheUtils': Unsatisfied dependency expressed through method 'setCacheService' parameter 0: Error creating bean with name 'tenantCacheManager' defined in class path resource [com/rega/erp/common/cache/config/CacheAutoConfiguration.class]: Unsatisfied dependency expressed through method 'tenantCacheManager' parameter 0: Error creating bean with name 'redisCacheService' defined in class path resource [com/rega/erp/common/cache/config/CacheAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redisCacheService' parameter 0: No qualifying bean of type 'org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.Object>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-06-16T15:30:58.289+08:00  INFO 72869 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-16T15:30:58.333+08:00 ERROR 72869 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of method redisCacheService in com.rega.erp.common.cache.config.CacheAutoConfiguration required a bean of type 'org.springframework.data.redis.core.RedisTemplate' that could not be found.


Action:

Consider defining a bean of type 'org.springframework.data.redis.core.RedisTemplate' in your configuration.

2025-06-16T15:32:11.056+08:00  INFO 73324 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 73324 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/rega-api-1.0.0-SNAPSHOT.jar started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api)
2025-06-16T15:32:11.057+08:00 DEBUG 73324 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-16T15:32:11.058+08:00  INFO 73324 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-16T15:32:12.530+08:00  INFO 73324 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:32:12.530+08:00  INFO 73324 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-16T15:32:12.595+08:00  INFO 73324 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-16T15:32:12.724+08:00  WARN 73324 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cacheUtils': Unsatisfied dependency expressed through method 'setCacheService' parameter 0: No qualifying bean of type 'com.rega.erp.common.cache.core.CacheService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-06-16T15:32:12.727+08:00  INFO 73324 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-16T15:32:12.770+08:00 ERROR 73324 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of method setCacheService in com.rega.erp.common.cache.util.CacheUtils required a bean of type 'com.rega.erp.common.cache.core.CacheService' that could not be found.


Action:

Consider defining a bean of type 'com.rega.erp.common.cache.core.CacheService' in your configuration.

2025-06-16T15:33:22.498+08:00  INFO 73992 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 73992 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/rega-api-1.0.0-SNAPSHOT.jar started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api)
2025-06-16T15:33:22.503+08:00 DEBUG 73992 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-16T15:33:22.506+08:00  INFO 73992 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-16T15:33:23.921+08:00  INFO 73992 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:33:23.921+08:00  INFO 73992 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-16T15:33:23.996+08:00  INFO 73992 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-16T15:33:24.558+08:00  INFO 73992 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= caffeine
2025-06-16T15:33:24.562+08:00  INFO 73992 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= redisson
2025-06-16T15:33:24.663+08:00  INFO 73992 --- [main] org.redisson.Version                     : Redisson 3.20.0
2025-06-16T15:33:24.709+08:00  WARN 73992 --- [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-16T15:33:24.871+08:00  INFO 73992 --- [redisson-netty-2-5] o.r.c.pool.MasterPubSubConnectionPool    : 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-16T15:33:24.925+08:00  INFO 73992 --- [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool          : 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-16T15:33:24.961+08:00  WARN 73992 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redissonAutoInit' defined in class path resource [com/alicp/jetcache/autoconfigure/RedissonAutoConfiguration.class]: com/alicp/jetcache/redisson/RedissonCacheBuilder
2025-06-16T15:33:24.977+08:00  INFO 73992 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-16T15:33:25.016+08:00 ERROR 73992 --- [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redissonAutoInit' defined in class path resource [com/alicp/jetcache/autoconfigure/RedissonAutoConfiguration.class]: com/alicp/jetcache/redisson/RedissonCacheBuilder
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1770) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:598) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:941) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:608) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:733) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:435) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:311) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1305) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1294) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at com.rega.erp.api.RegaApiApplication.main(RegaApiApplication.java:17) ~[classes!/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:95) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
Caused by: java.lang.NoClassDefFoundError: com/alicp/jetcache/redisson/RedissonCacheBuilder
	at com.alicp.jetcache.autoconfigure.RedissonAutoConfiguration$RedissonAutoInit.initCache(RedissonAutoConfiguration.java:63) ~[jetcache-autoconfigure-2.7.3.jar!/:na]
	at com.alicp.jetcache.autoconfigure.AbstractCacheAutoInit.process(AbstractCacheAutoInit.java:69) ~[jetcache-autoconfigure-2.7.3.jar!/:na]
	at com.alicp.jetcache.autoconfigure.AbstractCacheAutoInit.afterPropertiesSet(AbstractCacheAutoInit.java:50) ~[jetcache-autoconfigure-2.7.3.jar!/:na]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1816) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1766) ~[spring-beans-6.0.9.jar!/:6.0.9]
	... 24 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.alicp.jetcache.redisson.RedissonCacheBuilder
	at java.base/java.net.URLClassLoader.findClass(URLClassLoader.java:445) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:587) ~[na:na]
	at org.springframework.boot.loader.LaunchedURLClassLoader.loadClass(LaunchedURLClassLoader.java:149) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520) ~[na:na]
	... 29 common frames omitted

2025-06-16T15:33:37.937+08:00  INFO 74097 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 74097 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/rega-api-1.0.0-SNAPSHOT.jar started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api)
2025-06-16T15:33:37.939+08:00 DEBUG 74097 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-16T15:33:37.940+08:00  INFO 74097 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-16T15:33:39.399+08:00  INFO 74097 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:33:39.399+08:00  INFO 74097 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-16T15:33:39.468+08:00  INFO 74097 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-16T15:33:40.060+08:00  INFO 74097 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= caffeine
2025-06-16T15:33:40.069+08:00  INFO 74097 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= redisson
2025-06-16T15:33:40.203+08:00  INFO 74097 --- [main] org.redisson.Version                     : Redisson 3.20.0
2025-06-16T15:33:40.256+08:00  WARN 74097 --- [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-16T15:33:40.503+08:00  INFO 74097 --- [redisson-netty-2-5] o.r.c.pool.MasterPubSubConnectionPool    : 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-16T15:33:40.556+08:00  INFO 74097 --- [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool          : 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-16T15:33:40.595+08:00  WARN 74097 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redissonAutoInit' defined in class path resource [com/alicp/jetcache/autoconfigure/RedissonAutoConfiguration.class]: com/alicp/jetcache/redisson/RedissonCacheBuilder
2025-06-16T15:33:40.615+08:00  INFO 74097 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-16T15:33:40.660+08:00 ERROR 74097 --- [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redissonAutoInit' defined in class path resource [com/alicp/jetcache/autoconfigure/RedissonAutoConfiguration.class]: com/alicp/jetcache/redisson/RedissonCacheBuilder
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1770) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:598) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:941) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:608) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:733) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:435) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:311) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1305) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1294) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at com.rega.erp.api.RegaApiApplication.main(RegaApiApplication.java:17) ~[classes!/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:95) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
Caused by: java.lang.NoClassDefFoundError: com/alicp/jetcache/redisson/RedissonCacheBuilder
	at com.alicp.jetcache.autoconfigure.RedissonAutoConfiguration$RedissonAutoInit.initCache(RedissonAutoConfiguration.java:63) ~[jetcache-autoconfigure-2.7.3.jar!/:na]
	at com.alicp.jetcache.autoconfigure.AbstractCacheAutoInit.process(AbstractCacheAutoInit.java:69) ~[jetcache-autoconfigure-2.7.3.jar!/:na]
	at com.alicp.jetcache.autoconfigure.AbstractCacheAutoInit.afterPropertiesSet(AbstractCacheAutoInit.java:50) ~[jetcache-autoconfigure-2.7.3.jar!/:na]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1816) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1766) ~[spring-beans-6.0.9.jar!/:6.0.9]
	... 24 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.alicp.jetcache.redisson.RedissonCacheBuilder
	at java.base/java.net.URLClassLoader.findClass(URLClassLoader.java:445) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:587) ~[na:na]
	at org.springframework.boot.loader.LaunchedURLClassLoader.loadClass(LaunchedURLClassLoader.java:149) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520) ~[na:na]
	... 29 common frames omitted

2025-06-16T15:35:09.000+08:00  INFO 74662 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 74662 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/rega-api-1.0.0-SNAPSHOT.jar started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api)
2025-06-16T15:35:09.003+08:00 DEBUG 74662 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-16T15:35:09.004+08:00  INFO 74662 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-16T15:35:10.750+08:00  INFO 74662 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:35:10.751+08:00  INFO 74662 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-16T15:35:10.834+08:00  INFO 74662 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-16T15:35:10.979+08:00  INFO 74662 --- [main] c.r.e.c.c.config.CacheAutoConfiguration  : RegaWebERP Cache Module Auto Configuration Initialized
2025-06-16T15:35:10.990+08:00  WARN 74662 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cacheUtils': Unsatisfied dependency expressed through method 'setCacheService' parameter 0: Error creating bean with name 'tenantCacheManager' defined in class path resource [com/rega/erp/common/cache/config/CacheAutoConfiguration.class]: Unsatisfied dependency expressed through method 'tenantCacheManager' parameter 0: Error creating bean with name 'redisCacheService' defined in class path resource [com/rega/erp/common/cache/config/CacheAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redisCacheService' parameter 0: No qualifying bean of type 'org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.Object>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-06-16T15:35:10.995+08:00  INFO 74662 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-16T15:35:11.078+08:00 ERROR 74662 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of method redisCacheService in com.rega.erp.common.cache.config.CacheAutoConfiguration required a bean of type 'org.springframework.data.redis.core.RedisTemplate' that could not be found.


Action:

Consider defining a bean of type 'org.springframework.data.redis.core.RedisTemplate' in your configuration.

2025-06-16T15:36:52.299+08:00  INFO 75356 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 75356 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/rega-api-1.0.0-SNAPSHOT.jar started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api)
2025-06-16T15:36:52.301+08:00 DEBUG 75356 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-16T15:36:52.301+08:00  INFO 75356 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-16T15:36:53.620+08:00  INFO 75356 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:36:53.621+08:00  INFO 75356 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-16T15:36:53.678+08:00  INFO 75356 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-16T15:36:53.799+08:00  WARN 75356 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cacheUtils': Unsatisfied dependency expressed through method 'setCacheService' parameter 0: No qualifying bean of type 'com.rega.erp.common.cache.core.CacheService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-06-16T15:36:53.801+08:00  INFO 75356 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-16T15:36:53.838+08:00 ERROR 75356 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of method setCacheService in com.rega.erp.common.cache.util.CacheUtils required a bean of type 'com.rega.erp.common.cache.core.CacheService' that could not be found.


Action:

Consider defining a bean of type 'com.rega.erp.common.cache.core.CacheService' in your configuration.

2025-06-16T15:37:59.106+08:00  INFO 75806 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 75806 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/rega-api-1.0.0-SNAPSHOT.jar started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api)
2025-06-16T15:37:59.108+08:00 DEBUG 75806 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-16T15:37:59.108+08:00  INFO 75806 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-16T15:38:00.540+08:00  INFO 75806 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:38:00.540+08:00  INFO 75806 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-16T15:38:00.602+08:00  INFO 75806 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-16T15:38:00.710+08:00  INFO 75806 --- [main] c.r.e.c.c.config.CacheAutoConfiguration  : RegaWebERP Cache Module Auto Configuration Initialized
2025-06-16T15:38:01.162+08:00  INFO 75806 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= caffeine
2025-06-16T15:38:01.168+08:00  INFO 75806 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= redisson
2025-06-16T15:38:01.271+08:00  INFO 75806 --- [main] org.redisson.Version                     : Redisson 3.20.0
2025-06-16T15:38:01.320+08:00  WARN 75806 --- [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-16T15:38:01.495+08:00  INFO 75806 --- [redisson-netty-2-5] o.r.c.pool.MasterPubSubConnectionPool    : 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-16T15:38:01.561+08:00  INFO 75806 --- [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool          : 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-16T15:38:01.609+08:00  INFO 75806 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat period at 15 MINUTES
2025-06-16T15:38:01.643+08:00  WARN 75806 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'machineId' defined in class path resource [me/ahoo/cosid/spring/boot/starter/machine/CosIdMachineAutoConfiguration.class]: Unsatisfied dependency expressed through method 'machineId' parameter 0: No qualifying bean of type 'me.ahoo.cosid.machine.MachineIdDistributor' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-06-16T15:38:01.644+08:00  INFO 75806 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat canceled
2025-06-16T15:38:01.664+08:00  INFO 75806 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-16T15:38:01.702+08:00 ERROR 75806 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of method machineId in me.ahoo.cosid.spring.boot.starter.machine.CosIdMachineAutoConfiguration required a bean of type 'me.ahoo.cosid.machine.MachineIdDistributor' that could not be found.


Action:

Consider defining a bean of type 'me.ahoo.cosid.machine.MachineIdDistributor' in your configuration.

2025-06-16T15:39:10.723+08:00  INFO 76241 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 76241 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/rega-api-1.0.0-SNAPSHOT.jar started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api)
2025-06-16T15:39:10.724+08:00 DEBUG 76241 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-16T15:39:10.725+08:00  INFO 76241 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-16T15:39:12.194+08:00  INFO 76241 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:39:12.194+08:00  INFO 76241 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-16T15:39:12.259+08:00  INFO 76241 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-16T15:39:12.378+08:00  INFO 76241 --- [main] c.r.e.c.c.config.CacheAutoConfiguration  : RegaWebERP Cache Module Auto Configuration Initialized
2025-06-16T15:39:12.831+08:00  INFO 76241 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= caffeine
2025-06-16T15:39:12.838+08:00  INFO 76241 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= redisson
2025-06-16T15:39:12.938+08:00  INFO 76241 --- [main] org.redisson.Version                     : Redisson 3.20.0
2025-06-16T15:39:12.988+08:00  WARN 76241 --- [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-16T15:39:13.211+08:00  INFO 76241 --- [redisson-netty-2-7] o.r.c.pool.MasterPubSubConnectionPool    : 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-16T15:39:13.251+08:00  INFO 76241 --- [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool          : 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-16T15:39:13.301+08:00  INFO 76241 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat period at 15 MINUTES
2025-06-16T15:39:13.540+08:00  INFO 76241 --- [main] m.a.c.m.ManualMachineIdDistributor       : Distribute Remote machineState:[MachineState{machineId=1, lastTimeStamp=-1}] - instanceId:[InstanceId{instanceId=***********:76241, stable=false}] - machineBit:[10] @ namespace:[rega-api].
2025-06-16T15:39:13.788+08:00  WARN 76241 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'anyline.environment.configuration.spring': Error creating bean with name 'anyline.environment.spring.data.driver.actuator.jdbc': Lookup method resolution failed
2025-06-16T15:39:13.789+08:00  INFO 76241 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat canceled
2025-06-16T15:39:13.807+08:00  INFO 76241 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-16T15:39:13.851+08:00 ERROR 76241 --- [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'anyline.environment.configuration.spring': Error creating bean with name 'anyline.environment.spring.data.driver.actuator.jdbc': Lookup method resolution failed
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:605) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:941) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:608) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:733) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:435) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:311) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1305) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1294) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at com.rega.erp.api.RegaApiApplication.main(RegaApiApplication.java:17) ~[classes!/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:95) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'anyline.environment.spring.data.driver.actuator.jdbc': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.checkLookupMethods(AutowiredAnnotationBeanPostProcessor.java:471) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:341) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1291) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1186) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:560) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:663) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:651) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1302) ~[spring-context-6.0.9.jar!/:6.0.9]
	at org.anyline.environment.spring.SpringEnvironmentWorker.getBeans(SpringEnvironmentWorker.java:81) ~[anyline-environment-spring-8.7.2-SNAPSHOT.jar!/:na]
	at org.anyline.data.listener.init.DataSourceLoadListener.start(DataSourceLoadListener.java:80) ~[anyline-data-8.7.2-SNAPSHOT.jar!/:na]
	at org.anyline.environment.spring.SpringAutoConfiguration.loaderStart(SpringAutoConfiguration.java:109) ~[anyline-environment-spring-8.7.2-SNAPSHOT.jar!/:na]
	at org.anyline.environment.spring.SpringAutoConfiguration.setLoadListeners(SpringAutoConfiguration.java:61) ~[anyline-environment-spring-8.7.2-SNAPSHOT.jar!/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.inject(AutowiredAnnotationBeanPostProcessor.java:775) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:133) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:482) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1416) ~[spring-beans-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597) ~[spring-beans-6.0.9.jar!/:6.0.9]
	... 23 common frames omitted
Caused by: java.lang.IllegalStateException: Failed to introspect Class [org.anyline.environment.spring.data.jdbc.SpringJDBCActuator] from ClassLoader [org.springframework.boot.loader.LaunchedURLClassLoader@5b480cf9]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:483) ~[spring-core-6.0.9.jar!/:6.0.9]
	at org.springframework.util.ReflectionUtils.doWithLocalMethods(ReflectionUtils.java:320) ~[spring-core-6.0.9.jar!/:6.0.9]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.checkLookupMethods(AutowiredAnnotationBeanPostProcessor.java:449) ~[spring-beans-6.0.9.jar!/:6.0.9]
	... 48 common frames omitted
Caused by: java.lang.NoClassDefFoundError: org/springframework/jdbc/core/CallableStatementCallback
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method) ~[na:na]
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402) ~[na:na]
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2504) ~[na:na]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:465) ~[spring-core-6.0.9.jar!/:6.0.9]
	... 50 common frames omitted
Caused by: java.lang.ClassNotFoundException: org.springframework.jdbc.core.CallableStatementCallback
	at java.base/java.net.URLClassLoader.findClass(URLClassLoader.java:445) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:587) ~[na:na]
	at org.springframework.boot.loader.LaunchedURLClassLoader.loadClass(LaunchedURLClassLoader.java:149) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520) ~[na:na]
	... 54 common frames omitted

2025-06-16T15:39:54.968+08:00  INFO 76542 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 76542 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/rega-api-1.0.0-SNAPSHOT.jar started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api)
2025-06-16T15:39:54.970+08:00 DEBUG 76542 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-16T15:39:54.971+08:00  INFO 76542 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-16T15:39:56.803+08:00  INFO 76542 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:39:56.803+08:00  INFO 76542 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-16T15:39:56.866+08:00  INFO 76542 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-16T15:39:56.992+08:00  INFO 76542 --- [main] c.r.e.c.c.config.CacheAutoConfiguration  : RegaWebERP Cache Module Auto Configuration Initialized
2025-06-16T15:39:57.237+08:00  INFO 76542 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= caffeine
2025-06-16T15:39:57.243+08:00  INFO 76542 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= redisson
2025-06-16T15:39:57.355+08:00  INFO 76542 --- [main] org.redisson.Version                     : Redisson 3.20.0
2025-06-16T15:39:57.404+08:00  WARN 76542 --- [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-16T15:39:57.575+08:00  INFO 76542 --- [redisson-netty-2-5] o.r.c.pool.MasterPubSubConnectionPool    : 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-16T15:39:57.635+08:00  INFO 76542 --- [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool          : 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-16T15:39:57.697+08:00  INFO 76542 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat period at 15 MINUTES
2025-06-16T15:39:57.742+08:00  INFO 76542 --- [main] m.a.c.m.ManualMachineIdDistributor       : Distribute Remote machineState:[MachineState{machineId=1, lastTimeStamp=-1}] - instanceId:[InstanceId{instanceId=***********:76542, stable=false}] - machineBit:[10] @ namespace:[rega-api].
2025-06-16T15:39:58.937+08:00  INFO 76542 --- [main] com.rega.erp.api.RegaApiApplication      : Started RegaApiApplication in 4.423 seconds (process running for 4.845)
2025-06-16T15:43:02.846+08:00  INFO 76542 --- [http-nio-8000-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [Hm_lvt_246a935992138d6770cabe711402315c=1748997863,1749000517] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-06-16T15:43:02.865+08:00  INFO 76542 --- [http-nio-8000-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16T15:44:12.969+08:00  INFO 76542 --- [SpringApplicationShutdownHook] c.a.j.support.DefaultMetricsManager      : cache stat canceled
