2025-06-17T15:03:30.177+08:00  INFO 68863 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 68863 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/rega-api-1.0.0-SNAPSHOT.jar started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api)
2025-06-17T15:03:30.184+08:00 DEBUG 68863 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-17T15:03:30.191+08:00  INFO 68863 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-17T15:03:32.893+08:00  INFO 68863 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T15:03:32.893+08:00  INFO 68863 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-17T15:03:32.987+08:00  INFO 68863 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-17T15:03:33.178+08:00  INFO 68863 --- [main] c.r.e.c.c.config.CacheAutoConfiguration  : RegaWebERP Cache Module Auto Configuration Initialized
2025-06-17T15:03:33.445+08:00  INFO 68863 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= caffeine
2025-06-17T15:03:33.452+08:00  INFO 68863 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= redisson
2025-06-17T15:03:33.556+08:00  INFO 68863 --- [main] org.redisson.Version                     : Redisson 3.20.0
2025-06-17T15:03:33.632+08:00  WARN 68863 --- [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-17T15:03:33.957+08:00  INFO 68863 --- [redisson-netty-2-5] o.r.c.pool.MasterPubSubConnectionPool    : 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-17T15:03:34.049+08:00  INFO 68863 --- [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool          : 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-17T15:03:34.136+08:00  INFO 68863 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat period at 15 MINUTES
2025-06-17T15:03:34.272+08:00  INFO 68863 --- [main] m.a.c.m.ManualMachineIdDistributor       : Distribute Remote machineState:[MachineState{machineId=1, lastTimeStamp=-1}] - instanceId:[InstanceId{instanceId=***********:68863, stable=false}] - machineBit:[10] @ namespace:[rega-api].
2025-06-17T15:03:36.553+08:00  INFO 68863 --- [main] com.rega.erp.api.RegaApiApplication      : Started RegaApiApplication in 7.145 seconds (process running for 7.847)
2025-06-17T15:03:36.607+08:00  INFO 68863 --- [main] com.zaxxer.hikari.HikariDataSource       : RegaHikariCP - Starting...
2025-06-17T15:03:37.830+08:00 ERROR 68863 --- [main] com.zaxxer.hikari.pool.HikariPool        : RegaHikariCP - Exception during pool initialization.

org.postgresql.util.PSQLException: FATAL: password authentication failed for user "postgres"
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:693) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:203) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:258) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:263) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.Driver.makeConnection(Driver.java:443) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.Driver.connect(Driver.java:297) ~[postgresql-42.6.0.jar!/:42.6.0]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:359) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:201) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:470) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:100) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-5.0.1.jar!/:na]
	at com.rega.erp.api.config.DatabaseConfig.testDatabaseConnection(DatabaseConfig.java:36) ~[classes!/:na]
	at com.rega.erp.api.config.DatabaseConfig.run(DatabaseConfig.java:28) ~[classes!/:na]
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:770) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:754) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1305) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1294) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at com.rega.erp.api.RegaApiApplication.main(RegaApiApplication.java:17) ~[classes!/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:95) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[rega-api-1.0.0-SNAPSHOT.jar:na]

2025-06-17T15:03:37.838+08:00 ERROR 68863 --- [main] com.rega.erp.api.config.DatabaseConfig   : 数据库连接测试失败: FATAL: password authentication failed for user "postgres"

org.postgresql.util.PSQLException: FATAL: password authentication failed for user "postgres"
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:693) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:203) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:258) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:263) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.Driver.makeConnection(Driver.java:443) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.Driver.connect(Driver.java:297) ~[postgresql-42.6.0.jar!/:42.6.0]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:359) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:201) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:470) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:100) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-5.0.1.jar!/:na]
	at com.rega.erp.api.config.DatabaseConfig.testDatabaseConnection(DatabaseConfig.java:36) ~[classes!/:na]
	at com.rega.erp.api.config.DatabaseConfig.run(DatabaseConfig.java:28) ~[classes!/:na]
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:770) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:754) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1305) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1294) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at com.rega.erp.api.RegaApiApplication.main(RegaApiApplication.java:17) ~[classes!/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:95) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[rega-api-1.0.0-SNAPSHOT.jar:na]

2025-06-17T15:03:37.869+08:00 ERROR 68863 --- [main] o.s.boot.SpringApplication               : Application run failed

java.lang.IllegalStateException: Failed to execute CommandLineRunner
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:773) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:754) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1305) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1294) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at com.rega.erp.api.RegaApiApplication.main(RegaApiApplication.java:17) ~[classes!/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:95) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
Caused by: java.lang.RuntimeException: 数据库连接失败
	at com.rega.erp.api.config.DatabaseConfig.testDatabaseConnection(DatabaseConfig.java:50) ~[classes!/:na]
	at com.rega.erp.api.config.DatabaseConfig.run(DatabaseConfig.java:28) ~[classes!/:na]
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:770) ~[spring-boot-3.1.0.jar!/:3.1.0]
	... 13 common frames omitted
Caused by: org.postgresql.util.PSQLException: FATAL: password authentication failed for user "postgres"
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:693) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:203) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:258) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:263) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.Driver.makeConnection(Driver.java:443) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.Driver.connect(Driver.java:297) ~[postgresql-42.6.0.jar!/:42.6.0]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:359) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:201) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:470) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:100) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-5.0.1.jar!/:na]
	at com.rega.erp.api.config.DatabaseConfig.testDatabaseConnection(DatabaseConfig.java:36) ~[classes!/:na]
	... 15 common frames omitted

2025-06-17T15:03:38.896+08:00  INFO 68863 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat canceled
2025-06-17T15:26:45.713+08:00  INFO 79604 --- [main] com.rega.erp.api.RegaApiApplication      : Starting RegaApiApplication using Java 17.0.11 with PID 79604 (/Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api/target/rega-api-1.0.0-SNAPSHOT.jar started by wanghy in /Users/<USER>/Desktop/wanghy/projects/RegaMobile/RegaWebERP/rega-parent/rega-api)
2025-06-17T15:26:45.715+08:00 DEBUG 79604 --- [main] com.rega.erp.api.RegaApiApplication      : Running with Spring Boot v3.1.0, Spring v6.0.9
2025-06-17T15:26:45.715+08:00  INFO 79604 --- [main] com.rega.erp.api.RegaApiApplication      : The following 1 profile is active: "dev"
2025-06-17T15:26:47.534+08:00  INFO 79604 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T15:26:47.534+08:00  INFO 79604 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.8]
2025-06-17T15:26:47.606+08:00  INFO 79604 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-17T15:26:47.801+08:00  INFO 79604 --- [main] c.r.e.c.c.config.CacheAutoConfiguration  : RegaWebERP Cache Module Auto Configuration Initialized
2025-06-17T15:26:48.076+08:00  INFO 79604 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= caffeine
2025-06-17T15:26:48.083+08:00  INFO 79604 --- [main] c.a.j.a.AbstractCacheAutoInit            : init cache area default , type= redisson
2025-06-17T15:26:48.176+08:00  INFO 79604 --- [main] org.redisson.Version                     : Redisson 3.20.0
2025-06-17T15:26:48.224+08:00  WARN 79604 --- [main] i.n.r.d.DnsServerAddressStreamProviders  : Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-17T15:26:48.392+08:00  INFO 79604 --- [redisson-netty-2-7] o.r.c.pool.MasterPubSubConnectionPool    : 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-17T15:26:48.454+08:00  INFO 79604 --- [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool          : 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-06-17T15:26:48.508+08:00  INFO 79604 --- [main] c.a.j.support.DefaultMetricsManager      : cache stat period at 15 MINUTES
2025-06-17T15:26:48.731+08:00  INFO 79604 --- [main] m.a.c.m.ManualMachineIdDistributor       : Distribute Remote machineState:[MachineState{machineId=1, lastTimeStamp=-1}] - instanceId:[InstanceId{instanceId=***********:79604, stable=false}] - machineBit:[10] @ namespace:[rega-api].
2025-06-17T15:26:49.848+08:00  INFO 79604 --- [main] com.rega.erp.api.RegaApiApplication      : Started RegaApiApplication in 4.755 seconds (process running for 5.332)
2025-06-17T15:26:49.876+08:00  INFO 79604 --- [main] com.zaxxer.hikari.HikariDataSource       : RegaHikariCP - Starting...
2025-06-17T15:26:51.016+08:00 ERROR 79604 --- [main] com.zaxxer.hikari.pool.HikariPool        : RegaHikariCP - Exception during pool initialization.

org.postgresql.util.PSQLException: FATAL: password authentication failed for user "postgres"
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:693) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:203) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:258) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:263) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.Driver.makeConnection(Driver.java:443) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.Driver.connect(Driver.java:297) ~[postgresql-42.6.0.jar!/:42.6.0]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:359) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:201) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:470) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:100) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-5.0.1.jar!/:na]
	at com.rega.erp.api.config.DatabaseConfig.testDatabaseConnection(DatabaseConfig.java:36) ~[classes!/:na]
	at com.rega.erp.api.config.DatabaseConfig.run(DatabaseConfig.java:28) ~[classes!/:na]
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:770) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:754) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1305) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1294) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at com.rega.erp.api.RegaApiApplication.main(RegaApiApplication.java:17) ~[classes!/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:95) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[rega-api-1.0.0-SNAPSHOT.jar:na]

2025-06-17T15:26:51.020+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : ❌ 数据库连接测试失败: FATAL: password authentication failed for user "postgres"
2025-06-17T15:26:51.020+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 请检查以下配置：
2025-06-17T15:26:51.020+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 1. PostgreSQL 服务是否启动
2025-06-17T15:26:51.020+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 2. 数据库连接信息是否正确（host、port、database）
2025-06-17T15:26:51.021+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 3. 用户名和密码是否正确
2025-06-17T15:26:51.021+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 4. 数据库 'rega_erp' 是否已创建
2025-06-17T15:26:51.021+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 5. 用户是否有访问数据库的权限
2025-06-17T15:26:51.021+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 
2025-06-17T15:26:51.022+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 当前配置信息（来自 application-dev.yml）：
2025-06-17T15:26:51.022+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : - URL: *****************************************
2025-06-17T15:26:51.022+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : - 用户名: postgres
2025-06-17T15:26:51.022+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : - 数据库: rega_erp
2025-06-17T15:26:51.023+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 
2025-06-17T15:26:51.023+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 建议操作：
2025-06-17T15:26:51.023+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 1. 启动 PostgreSQL 服务
2025-06-17T15:26:51.023+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 2. 创建数据库：CREATE DATABASE rega_erp;
2025-06-17T15:26:51.023+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 3. 确认用户密码正确
2025-06-17T15:26:51.023+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 
2025-06-17T15:26:51.023+08:00  WARN 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : ⚠️  数据库连接失败，但应用将继续启动（仅缓存和基础功能可用）
2025-06-17T15:26:51.023+08:00  INFO 79604 --- [main] com.zaxxer.hikari.HikariDataSource       : RegaHikariCP - Starting...
2025-06-17T15:26:52.060+08:00 ERROR 79604 --- [main] com.zaxxer.hikari.pool.HikariPool        : RegaHikariCP - Exception during pool initialization.

org.postgresql.util.PSQLException: FATAL: password authentication failed for user "postgres"
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:693) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:203) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:258) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:263) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.Driver.makeConnection(Driver.java:443) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.Driver.connect(Driver.java:297) ~[postgresql-42.6.0.jar!/:42.6.0]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:359) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:201) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:470) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:100) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-5.0.1.jar!/:na]
	at com.rega.erp.api.config.DatabaseConfig.checkDatabaseTables(DatabaseConfig.java:76) ~[classes!/:na]
	at com.rega.erp.api.config.DatabaseConfig.run(DatabaseConfig.java:29) ~[classes!/:na]
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:770) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:754) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1305) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1294) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at com.rega.erp.api.RegaApiApplication.main(RegaApiApplication.java:17) ~[classes!/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:95) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[rega-api-1.0.0-SNAPSHOT.jar:na]

2025-06-17T15:26:52.065+08:00 ERROR 79604 --- [main] com.rega.erp.api.config.DatabaseConfig   : 数据库表检查失败: FATAL: password authentication failed for user "postgres"

org.postgresql.util.PSQLException: FATAL: password authentication failed for user "postgres"
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:693) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:203) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:258) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:263) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.Driver.makeConnection(Driver.java:443) ~[postgresql-42.6.0.jar!/:42.6.0]
	at org.postgresql.Driver.connect(Driver.java:297) ~[postgresql-42.6.0.jar!/:42.6.0]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:359) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:201) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:470) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:100) ~[HikariCP-5.0.1.jar!/:na]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-5.0.1.jar!/:na]
	at com.rega.erp.api.config.DatabaseConfig.checkDatabaseTables(DatabaseConfig.java:76) ~[classes!/:na]
	at com.rega.erp.api.config.DatabaseConfig.run(DatabaseConfig.java:29) ~[classes!/:na]
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:770) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:754) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1305) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1294) ~[spring-boot-3.1.0.jar!/:3.1.0]
	at com.rega.erp.api.RegaApiApplication.main(RegaApiApplication.java:17) ~[classes!/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:95) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[rega-api-1.0.0-SNAPSHOT.jar:na]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65) ~[rega-api-1.0.0-SNAPSHOT.jar:na]

2025-06-17T15:28:16.967+08:00  INFO 79604 --- [SpringApplicationShutdownHook] c.a.j.support.DefaultMetricsManager      : cache stat canceled
