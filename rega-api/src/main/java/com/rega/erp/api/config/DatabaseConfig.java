package com.rega.erp.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;

/**
 * 数据库配置和连接测试
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class DatabaseConfig implements CommandLineRunner {

    @Autowired
    private DataSource dataSource;

    @Override
    public void run(String... args) throws Exception {
        testDatabaseConnection();
        checkDatabaseTables();
    }

    /**
     * 测试数据库连接
     */
    private void testDatabaseConnection() {
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();

            log.info("=== 数据库连接信息 ===");
            log.info("数据库产品名称: {}", metaData.getDatabaseProductName());
            log.info("数据库产品版本: {}", metaData.getDatabaseProductVersion());
            log.info("驱动名称: {}", metaData.getDriverName());
            log.info("驱动版本: {}", metaData.getDriverVersion());
            log.info("数据库URL: {}", metaData.getURL());
            log.info("用户名: {}", metaData.getUserName());
            log.info("✅ 数据库连接测试成功！");

        } catch (Exception e) {
            log.error("❌ 数据库连接测试失败: {}", e.getMessage());
            log.error("请检查以下配置：");
            log.error("1. PostgreSQL 服务是否启动");
            log.error("2. 数据库连接信息是否正确（host、port、database）");
            log.error("3. 用户名和密码是否正确");
            log.error("4. 数据库 'rega_erp' 是否已创建");
            log.error("5. 用户是否有访问数据库的权限");
            log.error("");
            log.error("当前配置信息（来自 application-dev.yml）：");
            log.error("- URL: *****************************************");
            log.error("- 用户名: postgres");
            log.error("- 数据库: rega_erp");
            log.error("");
            log.error("建议操作：");
            log.error("1. 启动 PostgreSQL 服务");
            log.error("2. 创建数据库：CREATE DATABASE rega_erp;");
            log.error("3. 确认用户密码正确");
            log.error("");
            log.warn("⚠️  数据库连接失败，但应用将继续启动（仅缓存和基础功能可用）");
            // 不抛出异常，让应用继续启动
        }
    }

    /**
     * 检查数据库表是否存在
     */
    private void checkDatabaseTables() {
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            // 检查核心表是否存在
            String[] coreTableNames = {
                "sys_tenant", "sys_user", "sys_role", "sys_permission",
                "sys_user_role", "sys_role_permission", "sys_config", "sys_operation_log"
            };
            
            log.info("=== 数据库表检查 ===");
            boolean allTablesExist = true;
            
            for (String tableName : coreTableNames) {
                try (ResultSet rs = metaData.getTables(null, null, tableName, new String[]{"TABLE"})) {
                    if (rs.next()) {
                        log.info("✓ 表 {} 存在", tableName);
                    } else {
                        log.warn("✗ 表 {} 不存在", tableName);
                        allTablesExist = false;
                    }
                }
            }
            
            if (allTablesExist) {
                log.info("所有核心表都已存在，数据库初始化完成！");
            } else {
                log.warn("部分核心表不存在，请执行数据库初始化脚本！");
                log.info("初始化脚本位置: src/main/resources/db/migration/");
            }
            
        } catch (Exception e) {
            log.error("数据库表检查失败: {}", e.getMessage(), e);
        }
    }
}
