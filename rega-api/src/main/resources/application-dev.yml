server:
  port: 8000

spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ************************************************************************************************************************************
    username: postgres
    password: Abc123
    hikari:
      maximum-pool-size: 20
      connection-timeout: 30000
      idle-timeout: 600000
      keepalive-time: 30000
      max-lifetime: 1800000
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0

logging:
  level:
    com.rega.erp: debug
    org.springframework: warn
  file:
    path: ./logs/dev

knife4j:
  production: false

# Sa-Token配置
sa-token:
  # 是否输出操作日志 
  is-log: true

# CosId配置 - 开发环境
cosid:
  # 开发环境下启用调试日志
  snowflake:
    # 是否输出调试日志
    debug: true

rega:
  security:
    jwt:
      secret: regaerp123456dev
  upload:
    base-path: /rega/upload/dev 