-- RegaWebERP 数据库初始化脚本
-- 版本: V1.0.0
-- 描述: 创建基础表结构（租户、用户、角色、权限等）

-- =====================================================
-- 1. 租户管理表
-- =====================================================

-- 租户信息表
CREATE TABLE IF NOT EXISTS sys_tenant (
    id BIGINT PRIMARY KEY,
    tenant_code VARCHAR(50) NOT NULL UNIQUE COMMENT '租户编码',
    tenant_name VARCHAR(100) NOT NULL COMMENT '租户名称',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    expire_time TIMESTAMP COMMENT '过期时间',
    max_users INTEGER DEFAULT 100 COMMENT '最大用户数',
    max_storage BIGINT DEFAULT 1073741824 COMMENT '最大存储空间（字节）',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted INTEGER NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_tenant_code ON sys_tenant(tenant_code);
CREATE INDEX IF NOT EXISTS idx_sys_tenant_status ON sys_tenant(status);

-- =====================================================
-- 2. 用户管理表
-- =====================================================

-- 用户信息表
CREATE TABLE IF NOT EXISTS sys_user (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) COMMENT '真实姓名',
    nickname VARCHAR(50) COMMENT '昵称',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(200) COMMENT '头像URL',
    gender INTEGER COMMENT '性别：1-男，2-女，0-未知',
    birthday DATE COMMENT '生日',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    last_login_time TIMESTAMP COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    login_count INTEGER DEFAULT 0 COMMENT '登录次数',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted INTEGER NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
);

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_user_tenant_username ON sys_user(tenant_id, username) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_sys_user_tenant_id ON sys_user(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_email ON sys_user(email);
CREATE INDEX IF NOT EXISTS idx_sys_user_phone ON sys_user(phone);

-- =====================================================
-- 3. 角色管理表
-- =====================================================

-- 角色信息表
CREATE TABLE IF NOT EXISTS sys_role (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    role_code VARCHAR(50) NOT NULL COMMENT '角色编码',
    role_name VARCHAR(100) NOT NULL COMMENT '角色名称',
    role_type INTEGER NOT NULL DEFAULT 1 COMMENT '角色类型：1-普通角色，2-系统角色',
    data_scope INTEGER NOT NULL DEFAULT 1 COMMENT '数据权限：1-全部，2-部门，3-个人',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    sort_order INTEGER DEFAULT 0 COMMENT '排序',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted INTEGER NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
);

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_role_tenant_code ON sys_role(tenant_id, role_code) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_sys_role_tenant_id ON sys_role(tenant_id);

-- =====================================================
-- 4. 权限管理表
-- =====================================================

-- 权限资源表
CREATE TABLE IF NOT EXISTS sys_permission (
    id BIGINT PRIMARY KEY,
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    permission_code VARCHAR(100) NOT NULL COMMENT '权限编码',
    permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
    permission_type INTEGER NOT NULL COMMENT '权限类型：1-菜单，2-按钮，3-接口',
    path VARCHAR(200) COMMENT '路由路径',
    component VARCHAR(200) COMMENT '组件路径',
    icon VARCHAR(100) COMMENT '图标',
    method VARCHAR(10) COMMENT 'HTTP方法',
    url VARCHAR(200) COMMENT '接口URL',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    sort_order INTEGER DEFAULT 0 COMMENT '排序',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted INTEGER NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
);

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_permission_code ON sys_permission(permission_code) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_sys_permission_parent_id ON sys_permission(parent_id);
CREATE INDEX IF NOT EXISTS idx_sys_permission_type ON sys_permission(permission_type);

-- =====================================================
-- 5. 关联关系表
-- =====================================================

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS sys_user_role (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建人'
);

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_user_role ON sys_user_role(tenant_id, user_id, role_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_role_user_id ON sys_user_role(user_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_role_role_id ON sys_user_role(role_id);

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS sys_role_permission (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建人'
);

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_role_permission ON sys_role_permission(tenant_id, role_id, permission_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_permission_role_id ON sys_role_permission(role_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_permission_permission_id ON sys_role_permission(permission_id);

-- =====================================================
-- 6. 系统配置表
-- =====================================================

-- 系统配置表
CREATE TABLE IF NOT EXISTS sys_config (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT DEFAULT 0 COMMENT '租户ID，0表示系统级配置',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type INTEGER NOT NULL DEFAULT 1 COMMENT '配置类型：1-字符串，2-数字，3-布尔，4-JSON',
    description VARCHAR(200) COMMENT '配置描述',
    is_system INTEGER NOT NULL DEFAULT 0 COMMENT '是否系统配置：1-是，0-否',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted INTEGER NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
);

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_config_tenant_key ON sys_config(tenant_id, config_key);

-- =====================================================
-- 7. 操作日志表
-- =====================================================

-- 操作日志表
CREATE TABLE IF NOT EXISTS sys_operation_log (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT COMMENT '租户ID',
    user_id BIGINT COMMENT '用户ID',
    username VARCHAR(50) COMMENT '用户名',
    operation VARCHAR(100) COMMENT '操作名称',
    method VARCHAR(200) COMMENT '请求方法',
    params TEXT COMMENT '请求参数',
    result TEXT COMMENT '返回结果',
    ip VARCHAR(50) COMMENT 'IP地址',
    location VARCHAR(100) COMMENT '操作地点',
    user_agent VARCHAR(500) COMMENT '用户代理',
    status INTEGER COMMENT '操作状态：1-成功，0-失败',
    error_msg TEXT COMMENT '错误信息',
    cost_time INTEGER COMMENT '耗时（毫秒）',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_operation_log_tenant_id ON sys_operation_log(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sys_operation_log_user_id ON sys_operation_log(user_id);
CREATE INDEX IF NOT EXISTS idx_sys_operation_log_create_time ON sys_operation_log(create_time);

-- =====================================================
-- 8. 添加表注释
-- =====================================================

COMMENT ON TABLE sys_tenant IS '租户信息表';
COMMENT ON TABLE sys_user IS '用户信息表';
COMMENT ON TABLE sys_role IS '角色信息表';
COMMENT ON TABLE sys_permission IS '权限资源表';
COMMENT ON TABLE sys_user_role IS '用户角色关联表';
COMMENT ON TABLE sys_role_permission IS '角色权限关联表';
COMMENT ON TABLE sys_config IS '系统配置表';
COMMENT ON TABLE sys_operation_log IS '操作日志表';
