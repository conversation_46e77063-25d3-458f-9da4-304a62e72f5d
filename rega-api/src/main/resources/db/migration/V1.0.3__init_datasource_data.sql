-- RegaWebERP 多数据源初始化数据
-- 版本: V1.0.3
-- 描述: 初始化数据源配置模板和默认数据源

-- =====================================================
-- 1. 初始化数据源配置模板
-- =====================================================

-- PostgreSQL 模板
INSERT INTO sys_datasource_template (
    id, template_name, template_code, datasource_type, driver_class_name,
    url_template, default_port, validation_query, connection_properties, pool_config,
    description, is_system, status, create_by, update_by
) VALUES (
    1, 'PostgreSQL 标准模板', 'postgresql_standard', 'postgresql', 'org.postgresql.Driver',
    'jdbc:postgresql://{host}:{port}/{database}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai',
    5432, 'SELECT 1',
    '{"useUnicode": true, "characterEncoding": "utf8", "useSSL": false, "serverTimezone": "Asia/Shanghai"}',
    '{"initialSize": 5, "minIdle": 5, "maxActive": 20, "maxWait": 60000, "testOnBorrow": true, "testOnReturn": false, "testWhileIdle": true}',
    'PostgreSQL 数据库标准连接模板', true, 1, 1, 1
) ON CONFLICT (id) DO NOTHING;

-- MySQL 模板
INSERT INTO sys_datasource_template (
    id, template_name, template_code, datasource_type, driver_class_name,
    url_template, default_port, validation_query, connection_properties, pool_config,
    description, is_system, status, create_by, update_by
) VALUES (
    2, 'MySQL 标准模板', 'mysql_standard', 'mysql', 'com.mysql.cj.jdbc.Driver',
    'jdbc:mysql://{host}:{port}/{database}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai',
    3306, 'SELECT 1',
    '{"useUnicode": true, "characterEncoding": "utf8", "useSSL": false, "serverTimezone": "Asia/Shanghai"}',
    '{"initialSize": 5, "minIdle": 5, "maxActive": 20, "maxWait": 60000, "testOnBorrow": true, "testOnReturn": false, "testWhileIdle": true}',
    'MySQL 数据库标准连接模板', true, 1, 1, 1
) ON CONFLICT (id) DO NOTHING;

-- Oracle 模板
INSERT INTO sys_datasource_template (
    id, template_name, template_code, datasource_type, driver_class_name,
    url_template, default_port, validation_query, connection_properties, pool_config,
    description, is_system, status, create_by, update_by
) VALUES (
    3, 'Oracle 标准模板', 'oracle_standard', 'oracle', 'oracle.jdbc.OracleDriver',
    'jdbc:oracle:thin:@{host}:{port}:{database}',
    1521, 'SELECT 1 FROM DUAL',
    '{}',
    '{"initialSize": 5, "minIdle": 5, "maxActive": 20, "maxWait": 60000, "testOnBorrow": true, "testOnReturn": false, "testWhileIdle": true}',
    'Oracle 数据库标准连接模板', true, 1, 1, 1
) ON CONFLICT (id) DO NOTHING;

-- SQL Server 模板
INSERT INTO sys_datasource_template (
    id, template_name, template_code, datasource_type, driver_class_name,
    url_template, default_port, validation_query, connection_properties, pool_config,
    description, is_system, status, create_by, update_by
) VALUES (
    4, 'SQL Server 标准模板', 'sqlserver_standard', 'sqlserver', 'com.microsoft.sqlserver.jdbc.SQLServerDriver',
    'jdbc:sqlserver://{host}:{port};databaseName={database}',
    1433, 'SELECT 1',
    '{}',
    '{"initialSize": 5, "minIdle": 5, "maxActive": 20, "maxWait": 60000, "testOnBorrow": true, "testOnReturn": false, "testWhileIdle": true}',
    'SQL Server 数据库标准连接模板', true, 1, 1, 1
) ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- 2. 初始化默认数据源
-- =====================================================

-- 主数据源（从配置文件读取）
INSERT INTO sys_datasource (
    id, datasource_name, datasource_key, datasource_type, driver_class_name,
    url, username, password, initial_size, min_idle, max_active, max_wait,
    test_on_borrow, test_on_return, test_while_idle, validation_query,
    time_between_eviction_runs_millis, min_evictable_idle_time_millis,
    pool_prepared_statements, max_pool_prepared_statement_per_connection_size,
    status, is_default, health_check_enabled, health_check_interval,
    max_retry_count, description, create_by, update_by
) VALUES (
    1, '主数据源', 'primary', 'postgresql', 'org.postgresql.Driver',
    '*****************************************', 'rega_user', 'encrypted_password_here',
    5, 5, 20, 60000, true, false, true, 'SELECT 1',
    60000, 300000, true, 20, 1, true, true, 300000, 3,
    '系统主数据源，用于存储系统核心数据', 1, 1
) ON CONFLICT (id) DO NOTHING;

-- 演示租户专用数据源
INSERT INTO sys_datasource (
    id, datasource_name, datasource_key, datasource_type, driver_class_name,
    url, username, password, initial_size, min_idle, max_active, max_wait,
    test_on_borrow, test_on_return, test_while_idle, validation_query,
    time_between_eviction_runs_millis, min_evictable_idle_time_millis,
    pool_prepared_statements, max_pool_prepared_statement_per_connection_size,
    status, is_default, health_check_enabled, health_check_interval,
    max_retry_count, description, create_by, update_by
) VALUES (
    2, '演示租户数据源', 'demo_tenant', 'postgresql', 'org.postgresql.Driver',
    '**********************************************', 'rega_demo_user', 'encrypted_password_here',
    3, 3, 10, 60000, true, false, true, 'SELECT 1',
    60000, 300000, true, 20, 1, false, true, 300000, 3,
    '演示租户专用数据源，用于数据源隔离演示', 1, 1
) ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- 3. 初始化租户数据源关联
-- =====================================================

-- 系统租户使用主数据源（字段隔离）
INSERT INTO sys_tenant_datasource (
    id, tenant_id, datasource_id, isolation_type, is_primary,
    priority, read_weight, write_weight, status, create_by, update_by
) VALUES (
    1, 1, 1, 1, true, 0, 1, 1, 1, 1, 1
) ON CONFLICT (tenant_id, datasource_id) DO NOTHING;

-- 演示租户使用主数据源（字段隔离）
INSERT INTO sys_tenant_datasource (
    id, tenant_id, datasource_id, isolation_type, is_primary,
    priority, read_weight, write_weight, status, create_by, update_by
) VALUES (
    2, 2, 1, 1, true, 0, 1, 1, 1, 1, 1
) ON CONFLICT (tenant_id, datasource_id) DO NOTHING;

-- 演示租户也可以使用专用数据源（数据源隔离，备用）
INSERT INTO sys_tenant_datasource (
    id, tenant_id, datasource_id, isolation_type, is_primary,
    priority, read_weight, write_weight, status, create_by, update_by
) VALUES (
    3, 2, 2, 2, false, 1, 1, 1, 0, 1, 1
) ON CONFLICT (tenant_id, datasource_id) DO NOTHING;

-- =====================================================
-- 4. 初始化系统配置（数据源相关）
-- =====================================================

-- 数据源相关配置
INSERT INTO sys_config (id, tenant_id, config_key, config_value, config_type, description, is_system, create_by, update_by)
VALUES 
(10, 0, 'datasource.health.check.enabled', 'true', 3, '是否启用数据源健康检查', 1, 1, 1),
(11, 0, 'datasource.health.check.interval', '300000', 2, '数据源健康检查间隔（毫秒）', 1, 1, 1),
(12, 0, 'datasource.connection.timeout', '30000', 2, '数据源连接超时时间（毫秒）', 1, 1, 1),
(13, 0, 'datasource.max.retry.count', '3', 2, '数据源连接失败最大重试次数', 1, 1, 1),
(14, 0, 'datasource.monitor.retention.days', '30', 2, '数据源监控数据保留天数', 1, 1, 1),
(15, 0, 'datasource.log.retention.days', '90', 2, '数据源操作日志保留天数', 1, 1, 1),
(16, 0, 'tenant.default.isolation.type', '1', 2, '租户默认隔离类型：1-字段隔离，2-数据源隔离', 1, 1, 1),
(17, 0, 'tenant.max.datasource.count', '5', 2, '每个租户最大数据源数量', 1, 1, 1)
ON CONFLICT (tenant_id, config_key) DO NOTHING;

-- =====================================================
-- 5. 初始化权限（数据源管理相关）
-- =====================================================

-- 数据源管理菜单
INSERT INTO sys_permission (
    id, parent_id, permission_code, permission_name, permission_type,
    path, component, icon, status, sort_order, remark, create_by, update_by
) VALUES 
(51, 1, 'system:datasource', '数据源管理', 1, '/system/datasource', 'system/datasource/index', 'database', 1, 5, '数据源管理菜单', 1, 1)
ON CONFLICT (id) DO NOTHING;

-- 数据源管理权限
INSERT INTO sys_permission (
    id, parent_id, permission_code, permission_name, permission_type,
    method, url, status, sort_order, remark, create_by, update_by
) VALUES 
(511, 51, 'system:datasource:list', '数据源查询', 3, 'GET', '/api/system/datasource/list', 1, 1, '数据源列表查询权限', 1, 1),
(512, 51, 'system:datasource:add', '数据源新增', 3, 'POST', '/api/system/datasource', 1, 2, '数据源新增权限', 1, 1),
(513, 51, 'system:datasource:edit', '数据源修改', 3, 'PUT', '/api/system/datasource/*', 1, 3, '数据源修改权限', 1, 1),
(514, 51, 'system:datasource:delete', '数据源删除', 3, 'DELETE', '/api/system/datasource/*', 1, 4, '数据源删除权限', 1, 1),
(515, 51, 'system:datasource:test', '数据源测试', 3, 'POST', '/api/system/datasource/*/test', 1, 5, '数据源连接测试权限', 1, 1),
(516, 51, 'system:datasource:monitor', '数据源监控', 3, 'GET', '/api/system/datasource/*/monitor', 1, 6, '数据源监控查看权限', 1, 1),
(517, 51, 'system:datasource:assign', '分配数据源', 3, 'PUT', '/api/system/tenant/*/datasource', 1, 7, '租户数据源分配权限', 1, 1)
ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- 6. 为超级管理员分配数据源管理权限
-- =====================================================

-- 超级管理员拥有数据源管理权限
INSERT INTO sys_role_permission (id, tenant_id, role_id, permission_id, create_by)
SELECT 
    (SELECT COALESCE(MAX(id), 0) FROM sys_role_permission) + ROW_NUMBER() OVER (ORDER BY p.id) as id,
    1 as tenant_id,
    1 as role_id,
    p.id as permission_id,
    1 as create_by
FROM sys_permission p
WHERE p.permission_code LIKE 'system:datasource%'
AND NOT EXISTS (
    SELECT 1 FROM sys_role_permission rp 
    WHERE rp.tenant_id = 1 AND rp.role_id = 1 AND rp.permission_id = p.id
);

-- =====================================================
-- 7. 创建数据源监控初始记录
-- =====================================================

-- 为默认数据源创建初始监控记录
INSERT INTO sys_datasource_monitor (
    id, datasource_id, check_time, status, response_time,
    active_connections, idle_connections, total_connections,
    error_message, create_time
) VALUES 
(1, 1, CURRENT_TIMESTAMP, 1, 50, 2, 3, 5, NULL, CURRENT_TIMESTAMP),
(2, 2, CURRENT_TIMESTAMP, 1, 45, 1, 2, 3, NULL, CURRENT_TIMESTAMP)
ON CONFLICT (id) DO NOTHING;
