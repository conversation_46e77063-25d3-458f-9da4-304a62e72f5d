# Common Messages
common.success=Operation successful
common.failed=Operation failed
common.error=System error
common.unauthorized=Unauthorized access
common.forbidden=Access forbidden
common.not.found=Resource not found
common.bad.request=Bad request parameters
common.method.not.allowed=Method not allowed
common.conflict=Resource conflict
common.server.error=Internal server error
common.service.unavailable=Service unavailable

# Authentication Related
auth.login.success=Login successful
auth.login.failed=Login failed
auth.logout.success=Logout successful
auth.token.expired=Token expired
auth.token.invalid=Invalid token
auth.captcha.incorrect=Incorrect verification code
auth.captcha.expired=Verification code expired
auth.account.locked=Account locked
auth.account.disabled=Account disabled
auth.password.incorrect=Incorrect password
auth.password.expired=Password expired

# Tenant Related
tenant.not.found=Tenant not found
tenant.already.exists=Tenant already exists
tenant.disabled=Tenant disabled
tenant.create.success=Tenant created successfully
tenant.update.success=Tenant updated successfully
tenant.delete.success=Tenant deleted successfully
tenant.enable.success=Tenant enabled successfully
tenant.disable.success=Tenant disabled successfully

# Form Related
form.not.found=Form not found
form.already.exists=Form already exists
form.create.success=Form created successfully
form.update.success=Form updated successfully
form.delete.success=Form deleted successfully
form.publish.success=Form published successfully
form.data.submit.success=Form data submitted successfully
form.data.update.success=Form data updated successfully
form.schema.invalid=Invalid form schema

# Workflow Related
workflow.not.found=Workflow not found
workflow.already.exists=Workflow already exists
workflow.create.success=Workflow created successfully
workflow.update.success=Workflow updated successfully
workflow.delete.success=Workflow deleted successfully
workflow.deploy.success=Workflow deployed successfully
workflow.start.success=Workflow started successfully
workflow.task.complete.success=Task completed successfully
workflow.task.reject.success=Task rejected successfully
workflow.task.transfer.success=Task transferred successfully
workflow.process.invalid=Invalid process definition

# Report Related
report.not.found=Report not found
report.already.exists=Report already exists
report.create.success=Report created successfully
report.update.success=Report updated successfully
report.delete.success=Report deleted successfully
report.generate.success=Report generated successfully
report.export.success=Report exported successfully 