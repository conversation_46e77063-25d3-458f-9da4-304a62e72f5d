server:
  servlet:
    context-path: /api

spring:
  application:
    name: rega-api
  profiles:
    active: dev
  # 数据源配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # PostgreSQL 数据库连接
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:rega_erp}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:rega_user}
    password: ${DB_PASSWORD:rega_pass}
    driver-class-name: org.postgresql.Driver

    # HikariCP 连接池配置
    hikari:
      # 连接池名称
      pool-name: RegaHikariCP
      # 最小空闲连接数
      minimum-idle: 5
      # 最大连接池大小
      maximum-pool-size: 20
      # 连接超时时间（毫秒）
      connection-timeout: 30000
      # 空闲连接超时时间（毫秒）
      idle-timeout: 600000
      # 连接最大生命周期（毫秒）
      max-lifetime: 1800000
      # 连接测试查询
      connection-test-query: SELECT 1
      # 是否自动提交
      auto-commit: true
      # 连接初始化SQL
      connection-init-sql: SELECT 1
      # 验证超时时间（毫秒）
      validation-timeout: 5000
      # 是否在连接泄漏时记录日志
      leak-detection-threshold: 60000

# Anyline ORM 配置
anyline:
  # 数据源配置
  datasource:
    default: primary
    list:
      primary:
        url: ${spring.datasource.url}
        username: ${spring.datasource.username}
        password: ${spring.datasource.password}
        driver-class-name: ${spring.datasource.driver-class-name}

  # 元数据缓存配置
  metadata:
    cache:
      enable: true
      expire: 3600  # 缓存1小时

  # SQL 日志配置
  sql:
    log:
      enable: true
      level: DEBUG

  # 主键生成策略
  primary:
    generator: snowflake
  redis:
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms

  # Redis 配置（单机模式）
  data:
    redis:
      # 地址
      host: 127.0.0.1
      # 端口（默认 6379）
      port: 6379
      # 密码（未设置密码时请注释掉）
      #password: ${REDIS_PWD:123456}
      # 数据库索引
      database: 0
      # 连接超时时间
      timeout: 10s
      # 是否开启 SSL
      ssl:
        enabled: false
  ## Redisson 配置
  redisson:
    enabled: true
    mode: SINGLE
## JetCache 配置
jetcache:
  # 统计间隔（默认 0，表示不统计）
  statIntervalMinutes: 15
  ## 本地/进程级/一级缓存配置
  local:
    default:
      # 缓存类型
      type: caffeine
      # key 转换器的全局配置
      keyConvertor: jackson
      # 以毫秒为单位指定超时时间的全局配置
      expireAfterWriteInMillis: 7200000
      # 每个缓存实例的最大元素的全局配置，仅 local 类型的缓存需要指定
      limit: 1000
  ## 远程/分布式/二级缓存配置
  remote:
    default:
      # 缓存类型
      type: redisson
      # key 转换器的全局配置（用于将复杂的 KEY 类型转换为缓存实现可以接受的类型）
      keyConvertor: jackson
      # 以毫秒为单位指定超时时间的全局配置
      expireAfterWriteInMillis: 7200000
      # 2.7+ 支持两级缓存更新以后失效其他 JVM 中的 local cache，但多个服务共用 Redis 同一个 channel 可能会造成广播风暴，需要在这里指定channel。
      # 你可以决定多个不同的服务是否共用同一个 channel，如果没有指定则不开启。
      broadcastChannel: ${spring.application.name}
      # 序列化器的全局配置，仅 remote 类型的缓存需要指定
      valueEncoder: java
      valueDecoder: java


knife4j:
  enable: true
  setting:
    language: zh-CN
    swagger-model-name: 实体类列表
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: Copyright © 2024 RegaERP

logging:
  level:
    org.springframework: warn

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: rega-token
  # token有效期，单位s 默认30天, -1代表永不过期 
  timeout: 2592000
  # token临时有效期 (指定时间内无操作就过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录) 
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token) 
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出操作日志 
  is-log: false
  # 是否从cookie中读取token
  is-read-cookie: true
  # 是否从请求体里读取token
  is-read-body: false
  # 是否从header中读取token
  is-read-header: true

# Sa-Token扩展配置
sa-token.extension:
  # 安全配置：排除（放行）路径配置
  security.excludes:
    - /error
    # 静态资源
    - /*.html
    - /*/*.html
    - /*/*.css
    - /*/*.js
    - /websocket/**
    # 接口文档相关资源
    - /favicon.ico
    - /doc.html
    - /webjars/**
    - /swagger-ui/**
    - /swagger-resources/**
    - /*/api-docs/**
    # 本地存储资源
    - /file/**
    # 认证接口
    - /auth/login
    - /auth/captcha

# CosId 分布式ID生成器配置
cosid:
  # 命名空间，用于隔离不同应用的ID生成
  namespace: ${spring.application.name}
  # 机器号配置
  machine:
    # 是否启用机器号分配
    enabled: true
    # 机器号分配器配置
    distributor:
      # 分配器类型（MANUAL：手动指定机器号）
      type: MANUAL
      # 手动指定机器号（1-1023）
      manual:
        machine-id: 1
    # 机器号守护者配置
    guarder:
      # 是否启用机器号守护（防止机器号被占用）
      enabled: true
  # 雪花算法配置
  snowflake:
    # 是否启用雪花算法
    enabled: true
    # 时区设置
    zone-id: Asia/Shanghai
    # 纪元时间戳，单位毫秒（2020-01-01 00:00:00）
    epoch: 1577203200000
    # 共享配置
    share:
      # 是否启用时钟回拨同步（解决时钟回拨问题）
      clock-sync: true
      # 是否使用友好模式（对外部系统更友好）
      friendly: true
    # 提供者配置
    provider:
      # 安全的JavaScript提供者（JS友好的ID格式）
      safe-js:
        # 机器位数（7位可支持128个机器节点）
        machine-bit: 7
        # 序列位数（9位每毫秒可生成512个ID）
        sequence-bit: 9

rega:
  security:
    jwt:
      expiration: 86400
    ignore-urls:
      - /error
      - /v3/api-docs/**
      - /doc.html
      - /webjars/**
      - /swagger-resources/**
      - /actuator/**
      - /druid/**
      - /auth/login
      - /auth/captcha
      - /*.html
      - /*/*.html
      - /*/*.css
      - /*/*.js
      - /favicon.ico
      - /file/**
  tenant:
    enable: true
    ignore-tables:
      - sys_tenant
      - sys_user
      - sys_role
      - sys_menu
      - sys_dict
  upload:
    allowed-types: jpg,jpeg,png,gif,doc,docx,xls,xlsx,pdf
    max-size: 10

# 文件上传配置
spring.servlet:
  multipart:
    enabled: true
    # 单文件上传大小限制
    max-file-size: 10MB
    # 单次总上传文件大小限制
    max-request-size: 20MB