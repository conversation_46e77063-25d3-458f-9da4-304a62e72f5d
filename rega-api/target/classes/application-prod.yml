server:
  port: 8000

spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:postgres}:${DB_PORT:5432}/${DB_NAME:rega_erp}?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf8&stringtype=unspecified
    username: ${DB_USER:postgres}
    password: ${DB_PWD:password}
    hikari:
      maximum-pool-size: 50
      connection-timeout: 30000
      idle-timeout: 600000
      keepalive-time: 30000
      max-lifetime: 1800000
  redis:
    host: ${REDIS_HOST:redis}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DB:0}

logging:
  level:
    com.rega.erp: info
    org.springframework: error
  file:
    path: /var/logs
    name: rega-api.log
    max-size: 100MB
    max-history: 30

knife4j:
  production: true

# Sa-Token配置
sa-token:
  # token有效期，单位s，生产环境缩短为24小时
  timeout: 86400
  # 是否输出操作日志 
  is-log: false

# CosId配置 - 生产环境
cosid:
  # 生产环境下关闭调试日志
  snowflake:
    # 是否输出调试日志
    debug: false
    # 生产环境下加强时钟回拨处理
    share:
      # 时钟回拨处理策略（DELAY：延迟等待）
      clock-backwards: DELAY

rega:
  security:
    jwt:
      secret: ${JWT_SECRET:regaerp123456prod}
  upload:
    base-path: /data/rega/upload 