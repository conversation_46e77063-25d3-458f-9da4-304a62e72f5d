# 通用消息
common.success=操作成功
common.failed=操作失败
common.error=系统错误
common.unauthorized=未授权的访问
common.forbidden=禁止访问
common.not.found=资源不存在
common.bad.request=请求参数错误
common.method.not.allowed=请求方法不允许
common.conflict=资源冲突
common.server.error=服务器内部错误
common.service.unavailable=服务不可用

# 认证相关
auth.login.success=登录成功
auth.login.failed=登录失败
auth.logout.success=登出成功
auth.token.expired=令牌已过期
auth.token.invalid=无效的令牌
auth.captcha.incorrect=验证码错误
auth.captcha.expired=验证码已过期
auth.account.locked=账号已锁定
auth.account.disabled=账号已禁用
auth.password.incorrect=密码错误
auth.password.expired=密码已过期

# 租户相关
tenant.not.found=租户不存在
tenant.already.exists=租户已存在
tenant.disabled=租户已禁用
tenant.create.success=租户创建成功
tenant.update.success=租户更新成功
tenant.delete.success=租户删除成功
tenant.enable.success=租户启用成功
tenant.disable.success=租户禁用成功

# 表单相关
form.not.found=表单不存在
form.already.exists=表单已存在
form.create.success=表单创建成功
form.update.success=表单更新成功
form.delete.success=表单删除成功
form.publish.success=表单发布成功
form.data.submit.success=表单数据提交成功
form.data.update.success=表单数据更新成功
form.schema.invalid=表单架构无效

# 工作流相关
workflow.not.found=工作流不存在
workflow.already.exists=工作流已存在
workflow.create.success=工作流创建成功
workflow.update.success=工作流更新成功
workflow.delete.success=工作流删除成功
workflow.deploy.success=工作流部署成功
workflow.start.success=工作流启动成功
workflow.task.complete.success=任务完成成功
workflow.task.reject.success=任务驳回成功
workflow.task.transfer.success=任务转办成功
workflow.process.invalid=流程定义无效

# 报表相关
report.not.found=报表不存在
report.already.exists=报表已存在
report.create.success=报表创建成功
report.update.success=报表更新成功
report.delete.success=报表删除成功
report.generate.success=报表生成成功
report.export.success=报表导出成功 