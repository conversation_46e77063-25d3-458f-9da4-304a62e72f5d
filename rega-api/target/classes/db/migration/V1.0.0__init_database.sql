-- RegaWebERP 数据库初始化脚本
-- 版本: V1.0.0
-- 描述: 创建基础表结构（租户、用户、角色、权限等）

-- =====================================================
-- 1. 租户管理表
-- =====================================================

-- 租户信息表
CREATE TABLE IF NOT EXISTS sys_tenant (
    id BIGINT PRIMARY KEY,
    tenant_code VARCHAR(50) NOT NULL UNIQUE,
    tenant_name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(50),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    status INTEGER NOT NULL DEFAULT 1,
    expire_time TIMESTAMP,
    max_users INTEGER DEFAULT 100,
    max_storage BIGINT DEFAULT 1073741824,
    remark VARCHAR(500),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0
);

-- 添加 sys_tenant 表字段备注
COMMENT ON COLUMN sys_tenant.id IS '主键ID';
COMMENT ON COLUMN sys_tenant.tenant_code IS '租户编码';
COMMENT ON COLUMN sys_tenant.tenant_name IS '租户名称';
COMMENT ON COLUMN sys_tenant.contact_person IS '联系人';
COMMENT ON COLUMN sys_tenant.contact_phone IS '联系电话';
COMMENT ON COLUMN sys_tenant.contact_email IS '联系邮箱';
COMMENT ON COLUMN sys_tenant.status IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN sys_tenant.expire_time IS '过期时间';
COMMENT ON COLUMN sys_tenant.max_users IS '最大用户数';
COMMENT ON COLUMN sys_tenant.max_storage IS '最大存储空间（字节）';
COMMENT ON COLUMN sys_tenant.remark IS '备注';
COMMENT ON COLUMN sys_tenant.create_time IS '创建时间';
COMMENT ON COLUMN sys_tenant.update_time IS '更新时间';
COMMENT ON COLUMN sys_tenant.create_by IS '创建人';
COMMENT ON COLUMN sys_tenant.update_by IS '更新人';
COMMENT ON COLUMN sys_tenant.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_tenant_code ON sys_tenant(tenant_code);
CREATE INDEX IF NOT EXISTS idx_sys_tenant_status ON sys_tenant(status);

-- =====================================================
-- 2. 用户管理表
-- =====================================================

-- 用户信息表
CREATE TABLE IF NOT EXISTS sys_user (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(100) NOT NULL,
    real_name VARCHAR(50),
    nickname VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    avatar VARCHAR(200),
    gender INTEGER,
    birthday DATE,
    status INTEGER NOT NULL DEFAULT 1,
    last_login_time TIMESTAMP,
    last_login_ip VARCHAR(50),
    login_count INTEGER DEFAULT 0,
    remark VARCHAR(500),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0
);

-- 添加 sys_user 表字段备注
COMMENT ON COLUMN sys_user.id IS '主键ID';
COMMENT ON COLUMN sys_user.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_user.username IS '用户名';
COMMENT ON COLUMN sys_user.password IS '密码';
COMMENT ON COLUMN sys_user.real_name IS '真实姓名';
COMMENT ON COLUMN sys_user.nickname IS '昵称';
COMMENT ON COLUMN sys_user.email IS '邮箱';
COMMENT ON COLUMN sys_user.phone IS '手机号';
COMMENT ON COLUMN sys_user.avatar IS '头像URL';
COMMENT ON COLUMN sys_user.gender IS '性别：1-男，2-女，0-未知';
COMMENT ON COLUMN sys_user.birthday IS '生日';
COMMENT ON COLUMN sys_user.status IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN sys_user.last_login_time IS '最后登录时间';
COMMENT ON COLUMN sys_user.last_login_ip IS '最后登录IP';
COMMENT ON COLUMN sys_user.login_count IS '登录次数';
COMMENT ON COLUMN sys_user.remark IS '备注';
COMMENT ON COLUMN sys_user.create_time IS '创建时间';
COMMENT ON COLUMN sys_user.update_time IS '更新时间';
COMMENT ON COLUMN sys_user.create_by IS '创建人';
COMMENT ON COLUMN sys_user.update_by IS '更新人';
COMMENT ON COLUMN sys_user.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_user_tenant_username ON sys_user(tenant_id, username) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_sys_user_tenant_id ON sys_user(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_email ON sys_user(email);
CREATE INDEX IF NOT EXISTS idx_sys_user_phone ON sys_user(phone);

-- =====================================================
-- 3. 角色管理表
-- =====================================================

-- 角色信息表
CREATE TABLE IF NOT EXISTS sys_role (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    role_code VARCHAR(50) NOT NULL,
    role_name VARCHAR(100) NOT NULL,
    role_type INTEGER NOT NULL DEFAULT 1,
    data_scope INTEGER NOT NULL DEFAULT 1,
    status INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    remark VARCHAR(500),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0
);

-- 添加 sys_role 表字段备注
COMMENT ON COLUMN sys_role.id IS '主键ID';
COMMENT ON COLUMN sys_role.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_role.role_code IS '角色编码';
COMMENT ON COLUMN sys_role.role_name IS '角色名称';
COMMENT ON COLUMN sys_role.role_type IS '角色类型：1-普通角色，2-系统角色';
COMMENT ON COLUMN sys_role.data_scope IS '数据权限：1-全部，2-部门，3-个人';
COMMENT ON COLUMN sys_role.status IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN sys_role.sort_order IS '排序';
COMMENT ON COLUMN sys_role.remark IS '备注';
COMMENT ON COLUMN sys_role.create_time IS '创建时间';
COMMENT ON COLUMN sys_role.update_time IS '更新时间';
COMMENT ON COLUMN sys_role.create_by IS '创建人';
COMMENT ON COLUMN sys_role.update_by IS '更新人';
COMMENT ON COLUMN sys_role.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_role_tenant_code ON sys_role(tenant_id, role_code) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_sys_role_tenant_id ON sys_role(tenant_id);

-- =====================================================
-- 4. 权限管理表
-- =====================================================

-- 权限资源表
CREATE TABLE IF NOT EXISTS sys_permission (
    id BIGINT PRIMARY KEY,
    parent_id BIGINT DEFAULT 0,
    permission_code VARCHAR(100) NOT NULL,
    permission_name VARCHAR(100) NOT NULL,
    permission_type INTEGER NOT NULL,
    path VARCHAR(200),
    component VARCHAR(200),
    icon VARCHAR(100),
    method VARCHAR(10),
    url VARCHAR(200),
    status INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    remark VARCHAR(500),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0
);

-- 添加 sys_permission 表字段备注
COMMENT ON COLUMN sys_permission.id IS '主键ID';
COMMENT ON COLUMN sys_permission.parent_id IS '父权限ID';
COMMENT ON COLUMN sys_permission.permission_code IS '权限编码';
COMMENT ON COLUMN sys_permission.permission_name IS '权限名称';
COMMENT ON COLUMN sys_permission.permission_type IS '权限类型：1-菜单，2-按钮，3-接口';
COMMENT ON COLUMN sys_permission.path IS '路由路径';
COMMENT ON COLUMN sys_permission.component IS '组件路径';
COMMENT ON COLUMN sys_permission.icon IS '图标';
COMMENT ON COLUMN sys_permission.method IS 'HTTP方法';
COMMENT ON COLUMN sys_permission.url IS '接口URL';
COMMENT ON COLUMN sys_permission.status IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN sys_permission.sort_order IS '排序';
COMMENT ON COLUMN sys_permission.remark IS '备注';
COMMENT ON COLUMN sys_permission.create_time IS '创建时间';
COMMENT ON COLUMN sys_permission.update_time IS '更新时间';
COMMENT ON COLUMN sys_permission.create_by IS '创建人';
COMMENT ON COLUMN sys_permission.update_by IS '更新人';
COMMENT ON COLUMN sys_permission.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_permission_code ON sys_permission(permission_code) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_sys_permission_parent_id ON sys_permission(parent_id);
CREATE INDEX IF NOT EXISTS idx_sys_permission_type ON sys_permission(permission_type);

-- =====================================================
-- 5. 关联关系表
-- =====================================================

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS sys_user_role (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT
);

-- 添加 sys_user_role 表字段备注
COMMENT ON COLUMN sys_user_role.id IS '主键ID';
COMMENT ON COLUMN sys_user_role.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_user_role.user_id IS '用户ID';
COMMENT ON COLUMN sys_user_role.role_id IS '角色ID';
COMMENT ON COLUMN sys_user_role.create_time IS '创建时间';
COMMENT ON COLUMN sys_user_role.create_by IS '创建人';

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_user_role ON sys_user_role(tenant_id, user_id, role_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_role_user_id ON sys_user_role(user_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_role_role_id ON sys_user_role(role_id);

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS sys_role_permission (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT
);

-- 添加 sys_role_permission 表字段备注
COMMENT ON COLUMN sys_role_permission.id IS '主键ID';
COMMENT ON COLUMN sys_role_permission.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_role_permission.role_id IS '角色ID';
COMMENT ON COLUMN sys_role_permission.permission_id IS '权限ID';
COMMENT ON COLUMN sys_role_permission.create_time IS '创建时间';
COMMENT ON COLUMN sys_role_permission.create_by IS '创建人';

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_role_permission ON sys_role_permission(tenant_id, role_id, permission_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_permission_role_id ON sys_role_permission(role_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_permission_permission_id ON sys_role_permission(permission_id);

-- =====================================================
-- 6. 系统配置表
-- =====================================================

-- 系统配置表
CREATE TABLE IF NOT EXISTS sys_config (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT DEFAULT 0,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT,
    config_type INTEGER NOT NULL DEFAULT 1,
    description VARCHAR(200),
    is_system INTEGER NOT NULL DEFAULT 0,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0
);

-- 添加 sys_config 表字段备注
COMMENT ON COLUMN sys_config.id IS '主键ID';
COMMENT ON COLUMN sys_config.tenant_id IS '租户ID，0表示系统级配置';
COMMENT ON COLUMN sys_config.config_key IS '配置键';
COMMENT ON COLUMN sys_config.config_value IS '配置值';
COMMENT ON COLUMN sys_config.config_type IS '配置类型：1-字符串，2-数字，3-布尔，4-JSON';
COMMENT ON COLUMN sys_config.description IS '配置描述';
COMMENT ON COLUMN sys_config.is_system IS '是否系统配置：1-是，0-否';
COMMENT ON COLUMN sys_config.create_time IS '创建时间';
COMMENT ON COLUMN sys_config.update_time IS '更新时间';
COMMENT ON COLUMN sys_config.create_by IS '创建人';
COMMENT ON COLUMN sys_config.update_by IS '更新人';
COMMENT ON COLUMN sys_config.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_config_tenant_key ON sys_config(tenant_id, config_key);

-- =====================================================
-- 7. 操作日志表
-- =====================================================

-- 操作日志表
CREATE TABLE IF NOT EXISTS sys_operation_log (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT,
    user_id BIGINT,
    username VARCHAR(50),
    operation VARCHAR(100),
    method VARCHAR(200),
    params TEXT,
    result TEXT,
    ip VARCHAR(50),
    location VARCHAR(100),
    user_agent VARCHAR(500),
    status INTEGER,
    error_msg TEXT,
    cost_time INTEGER,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加 sys_operation_log 表字段备注
COMMENT ON COLUMN sys_operation_log.id IS '主键ID';
COMMENT ON COLUMN sys_operation_log.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_operation_log.user_id IS '用户ID';
COMMENT ON COLUMN sys_operation_log.username IS '用户名';
COMMENT ON COLUMN sys_operation_log.operation IS '操作名称';
COMMENT ON COLUMN sys_operation_log.method IS '请求方法';
COMMENT ON COLUMN sys_operation_log.params IS '请求参数';
COMMENT ON COLUMN sys_operation_log.result IS '返回结果';
COMMENT ON COLUMN sys_operation_log.ip IS 'IP地址';
COMMENT ON COLUMN sys_operation_log.location IS '操作地点';
COMMENT ON COLUMN sys_operation_log.user_agent IS '用户代理';
COMMENT ON COLUMN sys_operation_log.status IS '操作状态：1-成功，0-失败';
COMMENT ON COLUMN sys_operation_log.error_msg IS '错误信息';
COMMENT ON COLUMN sys_operation_log.cost_time IS '耗时（毫秒）';
COMMENT ON COLUMN sys_operation_log.create_time IS '创建时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_operation_log_tenant_id ON sys_operation_log(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sys_operation_log_user_id ON sys_operation_log(user_id);
CREATE INDEX IF NOT EXISTS idx_sys_operation_log_create_time ON sys_operation_log(create_time);

-- =====================================================
-- 8. 添加表注释
-- =====================================================

COMMENT ON TABLE sys_tenant IS '租户信息表';
COMMENT ON TABLE sys_user IS '用户信息表';
COMMENT ON TABLE sys_role IS '角色信息表';
COMMENT ON TABLE sys_permission IS '权限资源表';
COMMENT ON TABLE sys_user_role IS '用户角色关联表';
COMMENT ON TABLE sys_role_permission IS '角色权限关联表';
COMMENT ON TABLE sys_config IS '系统配置表';
COMMENT ON TABLE sys_operation_log IS '操作日志表';
