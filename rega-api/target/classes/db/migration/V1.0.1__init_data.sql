-- RegaWebERP 基础数据初始化脚本
-- 版本: V1.0.1
-- 描述: 初始化基础数据（默认租户、管理员用户、基础权限等）

-- =====================================================
-- 1. 初始化默认租户
-- =====================================================

-- 插入默认租户（系统租户）
INSERT INTO sys_tenant (
    id, tenant_code, tenant_name, contact_person, contact_phone, contact_email,
    status, max_users, max_storage, remark, create_by, update_by
) VALUES (
    1, 'system', '系统租户', '系统管理员', '13800138000', '<EMAIL>',
    1, 1000, 10737418240, '系统默认租户，用于系统管理', 1, 1
) ON CONFLICT (id) DO NOTHING;

-- 插入演示租户
INSERT INTO sys_tenant (
    id, tenant_code, tenant_name, contact_person, contact_phone, contact_email,
    status, max_users, max_storage, remark, create_by, update_by
) VALUES (
    2, 'demo', '演示租户', '演示用户', '13900139000', '<EMAIL>',
    1, 100, 1073741824, '演示租户，用于功能演示', 1, 1
) ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- 2. 初始化基础权限
-- =====================================================

-- 系统管理菜单
INSERT INTO sys_permission (
    id, parent_id, permission_code, permission_name, permission_type,
    path, component, icon, status, sort_order, remark, create_by, update_by
) VALUES 
(1, 0, 'system', '系统管理', 1, '/system', 'Layout', 'system', 1, 1, '系统管理菜单', 1, 1),
(2, 1, 'system:user', '用户管理', 1, '/system/user', 'system/user/index', 'user', 1, 1, '用户管理菜单', 1, 1),
(3, 1, 'system:role', '角色管理', 1, '/system/role', 'system/role/index', 'role', 1, 2, '角色管理菜单', 1, 1),
(4, 1, 'system:permission', '权限管理', 1, '/system/permission', 'system/permission/index', 'permission', 1, 3, '权限管理菜单', 1, 1),
(5, 1, 'system:tenant', '租户管理', 1, '/system/tenant', 'system/tenant/index', 'tenant', 1, 4, '租户管理菜单', 1, 1)
ON CONFLICT (id) DO NOTHING;

-- 用户管理权限
INSERT INTO sys_permission (
    id, parent_id, permission_code, permission_name, permission_type,
    method, url, status, sort_order, remark, create_by, update_by
) VALUES 
(11, 2, 'system:user:list', '用户查询', 3, 'GET', '/api/system/user/list', 1, 1, '用户列表查询权限', 1, 1),
(12, 2, 'system:user:add', '用户新增', 3, 'POST', '/api/system/user', 1, 2, '用户新增权限', 1, 1),
(13, 2, 'system:user:edit', '用户修改', 3, 'PUT', '/api/system/user/*', 1, 3, '用户修改权限', 1, 1),
(14, 2, 'system:user:delete', '用户删除', 3, 'DELETE', '/api/system/user/*', 1, 4, '用户删除权限', 1, 1),
(15, 2, 'system:user:reset', '重置密码', 3, 'PUT', '/api/system/user/*/reset-password', 1, 5, '重置用户密码权限', 1, 1)
ON CONFLICT (id) DO NOTHING;

-- 角色管理权限
INSERT INTO sys_permission (
    id, parent_id, permission_code, permission_name, permission_type,
    method, url, status, sort_order, remark, create_by, update_by
) VALUES 
(21, 3, 'system:role:list', '角色查询', 3, 'GET', '/api/system/role/list', 1, 1, '角色列表查询权限', 1, 1),
(22, 3, 'system:role:add', '角色新增', 3, 'POST', '/api/system/role', 1, 2, '角色新增权限', 1, 1),
(23, 3, 'system:role:edit', '角色修改', 3, 'PUT', '/api/system/role/*', 1, 3, '角色修改权限', 1, 1),
(24, 3, 'system:role:delete', '角色删除', 3, 'DELETE', '/api/system/role/*', 1, 4, '角色删除权限', 1, 1),
(25, 3, 'system:role:assign', '分配权限', 3, 'PUT', '/api/system/role/*/permissions', 1, 5, '角色权限分配权限', 1, 1)
ON CONFLICT (id) DO NOTHING;

-- 权限管理权限
INSERT INTO sys_permission (
    id, parent_id, permission_code, permission_name, permission_type,
    method, url, status, sort_order, remark, create_by, update_by
) VALUES 
(31, 4, 'system:permission:list', '权限查询', 3, 'GET', '/api/system/permission/list', 1, 1, '权限列表查询权限', 1, 1),
(32, 4, 'system:permission:add', '权限新增', 3, 'POST', '/api/system/permission', 1, 2, '权限新增权限', 1, 1),
(33, 4, 'system:permission:edit', '权限修改', 3, 'PUT', '/api/system/permission/*', 1, 3, '权限修改权限', 1, 1),
(34, 4, 'system:permission:delete', '权限删除', 3, 'DELETE', '/api/system/permission/*', 1, 4, '权限删除权限', 1, 1)
ON CONFLICT (id) DO NOTHING;

-- 租户管理权限
INSERT INTO sys_permission (
    id, parent_id, permission_code, permission_name, permission_type,
    method, url, status, sort_order, remark, create_by, update_by
) VALUES 
(41, 5, 'system:tenant:list', '租户查询', 3, 'GET', '/api/system/tenant/list', 1, 1, '租户列表查询权限', 1, 1),
(42, 5, 'system:tenant:add', '租户新增', 3, 'POST', '/api/system/tenant', 1, 2, '租户新增权限', 1, 1),
(43, 5, 'system:tenant:edit', '租户修改', 3, 'PUT', '/api/system/tenant/*', 1, 3, '租户修改权限', 1, 1),
(44, 5, 'system:tenant:delete', '租户删除', 3, 'DELETE', '/api/system/tenant/*', 1, 4, '租户删除权限', 1, 1)
ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- 3. 初始化基础角色
-- =====================================================

-- 超级管理员角色（系统租户）
INSERT INTO sys_role (
    id, tenant_id, role_code, role_name, role_type, data_scope,
    status, sort_order, remark, create_by, update_by
) VALUES (
    1, 1, 'super_admin', '超级管理员', 2, 1,
    1, 1, '系统超级管理员，拥有所有权限', 1, 1
) ON CONFLICT (id) DO NOTHING;

-- 租户管理员角色（演示租户）
INSERT INTO sys_role (
    id, tenant_id, role_code, role_name, role_type, data_scope,
    status, sort_order, remark, create_by, update_by
) VALUES (
    2, 2, 'tenant_admin', '租户管理员', 1, 1,
    1, 1, '租户管理员，拥有租户内所有权限', 1, 1
) ON CONFLICT (id) DO NOTHING;

-- 普通用户角色（演示租户）
INSERT INTO sys_role (
    id, tenant_id, role_code, role_name, role_type, data_scope,
    status, sort_order, remark, create_by, update_by
) VALUES (
    3, 2, 'user', '普通用户', 1, 3,
    1, 2, '普通用户，基础权限', 1, 1
) ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- 4. 初始化管理员用户
-- =====================================================

-- 系统超级管理员（密码：admin123）
INSERT INTO sys_user (
    id, tenant_id, username, password, real_name, nickname, email, phone,
    gender, status, remark, create_by, update_by
) VALUES (
    1, 1, 'admin', '$2a$10$7JB720yubVSOfvVWbfXCNOaWpDDvNVjpBYJvuisgfQddu/QgQqayG',
    '系统管理员', '超级管理员', '<EMAIL>', '13800138000',
    1, 1, '系统超级管理员账号', 1, 1
) ON CONFLICT (id) DO NOTHING;

-- 演示租户管理员（密码：demo123）
INSERT INTO sys_user (
    id, tenant_id, username, password, real_name, nickname, email, phone,
    gender, status, remark, create_by, update_by
) VALUES (
    2, 2, 'demo', '$2a$10$N.ZQk9YdGSVdwjjjef.lLOrbQEaRSa.QxV83jjfrYnhXrXNn4CLiq',
    '演示管理员', '演示用户', '<EMAIL>', '13900139000',
    1, 1, '演示租户管理员账号', 1, 1
) ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- 5. 分配角色权限
-- =====================================================

-- 超级管理员拥有所有权限
INSERT INTO sys_role_permission (id, tenant_id, role_id, permission_id, create_by)
SELECT 
    ROW_NUMBER() OVER (ORDER BY p.id) as id,
    1 as tenant_id,
    1 as role_id,
    p.id as permission_id,
    1 as create_by
FROM sys_permission p
WHERE NOT EXISTS (
    SELECT 1 FROM sys_role_permission rp 
    WHERE rp.tenant_id = 1 AND rp.role_id = 1 AND rp.permission_id = p.id
);

-- 租户管理员拥有租户内管理权限（除租户管理外）
INSERT INTO sys_role_permission (id, tenant_id, role_id, permission_id, create_by)
SELECT 
    (SELECT COALESCE(MAX(id), 0) FROM sys_role_permission) + ROW_NUMBER() OVER (ORDER BY p.id) as id,
    2 as tenant_id,
    2 as role_id,
    p.id as permission_id,
    1 as create_by
FROM sys_permission p
WHERE p.permission_code NOT LIKE 'system:tenant%'
AND NOT EXISTS (
    SELECT 1 FROM sys_role_permission rp 
    WHERE rp.tenant_id = 2 AND rp.role_id = 2 AND rp.permission_id = p.id
);

-- =====================================================
-- 6. 分配用户角色
-- =====================================================

-- 系统管理员分配超级管理员角色
INSERT INTO sys_user_role (id, tenant_id, user_id, role_id, create_by)
VALUES (1, 1, 1, 1, 1)
ON CONFLICT (tenant_id, user_id, role_id) DO NOTHING;

-- 演示用户分配租户管理员角色
INSERT INTO sys_user_role (id, tenant_id, user_id, role_id, create_by)
VALUES (2, 2, 2, 2, 1)
ON CONFLICT (tenant_id, user_id, role_id) DO NOTHING;

-- =====================================================
-- 7. 初始化系统配置
-- =====================================================

-- 系统基础配置
INSERT INTO sys_config (id, tenant_id, config_key, config_value, config_type, description, is_system, create_by, update_by)
VALUES 
(1, 0, 'system.name', 'RegaWebERP', 1, '系统名称', 1, 1, 1),
(2, 0, 'system.version', '1.0.0', 1, '系统版本', 1, 1, 1),
(3, 0, 'system.copyright', '© 2024 RegaWebERP Team', 1, '版权信息', 1, 1, 1),
(4, 0, 'user.default.password', '123456', 1, '用户默认密码', 1, 1, 1),
(5, 0, 'upload.max.size', '10485760', 2, '文件上传最大大小（字节）', 1, 1, 1)
ON CONFLICT (tenant_id, config_key) DO NOTHING;
