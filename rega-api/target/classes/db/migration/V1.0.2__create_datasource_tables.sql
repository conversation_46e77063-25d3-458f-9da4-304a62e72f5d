-- RegaWebERP 多数据源管理表结构
-- 版本: V1.0.2
-- 描述: 创建多数据源管理相关表（数据源配置、租户数据源关联等）

-- =====================================================
-- 1. 数据源配置表
-- =====================================================

-- 数据源配置表
CREATE TABLE IF NOT EXISTS sys_datasource (
    id BIGINT PRIMARY KEY,
    datasource_name VARCHAR(100) NOT NULL COMMENT '数据源名称',
    datasource_key VARCHAR(50) NOT NULL UNIQUE COMMENT '数据源标识键',
    datasource_type VARCHAR(20) NOT NULL DEFAULT 'postgresql' COMMENT '数据源类型：postgresql, mysql, oracle等',
    driver_class_name VARCHAR(200) NOT NULL COMMENT '驱动类名',
    url VARCHAR(500) NOT NULL COMMENT '数据库连接URL',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(200) NOT NULL COMMENT '密码（加密存储）',
    initial_size INTEGER DEFAULT 5 COMMENT '初始连接数',
    min_idle INTEGER DEFAULT 5 COMMENT '最小空闲连接数',
    max_active INTEGER DEFAULT 20 COMMENT '最大活跃连接数',
    max_wait BIGINT DEFAULT 60000 COMMENT '获取连接最大等待时间（毫秒）',
    test_on_borrow BOOLEAN DEFAULT true COMMENT '获取连接时是否测试',
    test_on_return BOOLEAN DEFAULT false COMMENT '归还连接时是否测试',
    test_while_idle BOOLEAN DEFAULT true COMMENT '空闲时是否测试连接',
    validation_query VARCHAR(100) DEFAULT 'SELECT 1' COMMENT '验证查询SQL',
    time_between_eviction_runs_millis BIGINT DEFAULT 60000 COMMENT '空闲连接回收器运行间隔（毫秒）',
    min_evictable_idle_time_millis BIGINT DEFAULT 300000 COMMENT '连接在池中保持空闲的最小时间（毫秒）',
    pool_prepared_statements BOOLEAN DEFAULT true COMMENT '是否缓存PreparedStatement',
    max_pool_prepared_statement_per_connection_size INTEGER DEFAULT 20 COMMENT '每个连接上PSCache的大小',
    connection_properties TEXT COMMENT '连接属性（JSON格式）',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    is_default BOOLEAN DEFAULT false COMMENT '是否默认数据源',
    health_check_enabled BOOLEAN DEFAULT true COMMENT '是否启用健康检查',
    health_check_interval BIGINT DEFAULT 300000 COMMENT '健康检查间隔（毫秒）',
    max_retry_count INTEGER DEFAULT 3 COMMENT '连接失败最大重试次数',
    description VARCHAR(500) COMMENT '数据源描述',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted INTEGER NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_datasource_key ON sys_datasource(datasource_key);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_type ON sys_datasource(datasource_type);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_status ON sys_datasource(status);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_default ON sys_datasource(is_default);

-- =====================================================
-- 2. 租户数据源关联表
-- =====================================================

-- 租户数据源关联表
CREATE TABLE IF NOT EXISTS sys_tenant_datasource (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    datasource_id BIGINT NOT NULL COMMENT '数据源ID',
    isolation_type INTEGER NOT NULL DEFAULT 1 COMMENT '隔离类型：1-字段隔离，2-数据源隔离',
    is_primary BOOLEAN DEFAULT false COMMENT '是否主数据源',
    priority INTEGER DEFAULT 0 COMMENT '优先级，数字越小优先级越高',
    read_weight INTEGER DEFAULT 1 COMMENT '读权重',
    write_weight INTEGER DEFAULT 1 COMMENT '写权重',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人'
);

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_tenant_datasource ON sys_tenant_datasource(tenant_id, datasource_id);
CREATE INDEX IF NOT EXISTS idx_sys_tenant_datasource_tenant_id ON sys_tenant_datasource(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sys_tenant_datasource_datasource_id ON sys_tenant_datasource(datasource_id);
CREATE INDEX IF NOT EXISTS idx_sys_tenant_datasource_primary ON sys_tenant_datasource(is_primary);

-- =====================================================
-- 3. 数据源监控表
-- =====================================================

-- 数据源监控表
CREATE TABLE IF NOT EXISTS sys_datasource_monitor (
    id BIGINT PRIMARY KEY,
    datasource_id BIGINT NOT NULL COMMENT '数据源ID',
    check_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '检查时间',
    status INTEGER NOT NULL COMMENT '状态：1-正常，0-异常',
    response_time BIGINT COMMENT '响应时间（毫秒）',
    active_connections INTEGER COMMENT '活跃连接数',
    idle_connections INTEGER COMMENT '空闲连接数',
    total_connections INTEGER COMMENT '总连接数',
    error_message TEXT COMMENT '错误信息',
    cpu_usage DECIMAL(5,2) COMMENT 'CPU使用率',
    memory_usage DECIMAL(5,2) COMMENT '内存使用率',
    disk_usage DECIMAL(5,2) COMMENT '磁盘使用率',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_datasource_monitor_datasource_id ON sys_datasource_monitor(datasource_id);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_monitor_check_time ON sys_datasource_monitor(check_time);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_monitor_status ON sys_datasource_monitor(status);

-- =====================================================
-- 4. 数据源操作日志表
-- =====================================================

-- 数据源操作日志表
CREATE TABLE IF NOT EXISTS sys_datasource_log (
    id BIGINT PRIMARY KEY,
    datasource_id BIGINT COMMENT '数据源ID',
    tenant_id BIGINT COMMENT '租户ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型：CREATE, UPDATE, DELETE, CONNECT, DISCONNECT',
    operation_desc VARCHAR(200) COMMENT '操作描述',
    operator_id BIGINT COMMENT '操作人ID',
    operator_name VARCHAR(50) COMMENT '操作人姓名',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    status INTEGER NOT NULL COMMENT '操作状态：1-成功，0-失败',
    error_message TEXT COMMENT '错误信息',
    cost_time BIGINT COMMENT '耗时（毫秒）',
    request_params TEXT COMMENT '请求参数（JSON格式）',
    response_data TEXT COMMENT '响应数据（JSON格式）',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_datasource_log_datasource_id ON sys_datasource_log(datasource_id);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_log_tenant_id ON sys_datasource_log(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_log_operation_type ON sys_datasource_log(operation_type);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_log_create_time ON sys_datasource_log(create_time);

-- =====================================================
-- 5. 数据源配置模板表
-- =====================================================

-- 数据源配置模板表
CREATE TABLE IF NOT EXISTS sys_datasource_template (
    id BIGINT PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_code VARCHAR(50) NOT NULL UNIQUE COMMENT '模板编码',
    datasource_type VARCHAR(20) NOT NULL COMMENT '数据源类型',
    driver_class_name VARCHAR(200) NOT NULL COMMENT '驱动类名',
    url_template VARCHAR(500) NOT NULL COMMENT 'URL模板',
    default_port INTEGER COMMENT '默认端口',
    validation_query VARCHAR(100) COMMENT '验证查询SQL',
    connection_properties TEXT COMMENT '默认连接属性（JSON格式）',
    pool_config TEXT COMMENT '连接池配置（JSON格式）',
    description VARCHAR(500) COMMENT '模板描述',
    is_system BOOLEAN DEFAULT false COMMENT '是否系统模板',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted INTEGER NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_datasource_template_code ON sys_datasource_template(template_code);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_template_type ON sys_datasource_template(datasource_type);

-- =====================================================
-- 6. 添加外键约束
-- =====================================================

-- 租户数据源关联表外键
ALTER TABLE sys_tenant_datasource 
ADD CONSTRAINT fk_tenant_datasource_tenant_id 
FOREIGN KEY (tenant_id) REFERENCES sys_tenant(id) ON DELETE CASCADE;

ALTER TABLE sys_tenant_datasource 
ADD CONSTRAINT fk_tenant_datasource_datasource_id 
FOREIGN KEY (datasource_id) REFERENCES sys_datasource(id) ON DELETE CASCADE;

-- 数据源监控表外键
ALTER TABLE sys_datasource_monitor 
ADD CONSTRAINT fk_datasource_monitor_datasource_id 
FOREIGN KEY (datasource_id) REFERENCES sys_datasource(id) ON DELETE CASCADE;

-- 数据源操作日志表外键
ALTER TABLE sys_datasource_log 
ADD CONSTRAINT fk_datasource_log_datasource_id 
FOREIGN KEY (datasource_id) REFERENCES sys_datasource(id) ON DELETE SET NULL;

ALTER TABLE sys_datasource_log 
ADD CONSTRAINT fk_datasource_log_tenant_id 
FOREIGN KEY (tenant_id) REFERENCES sys_tenant(id) ON DELETE SET NULL;

-- =====================================================
-- 7. 添加表注释
-- =====================================================

COMMENT ON TABLE sys_datasource IS '数据源配置表';
COMMENT ON TABLE sys_tenant_datasource IS '租户数据源关联表';
COMMENT ON TABLE sys_datasource_monitor IS '数据源监控表';
COMMENT ON TABLE sys_datasource_log IS '数据源操作日志表';
COMMENT ON TABLE sys_datasource_template IS '数据源配置模板表';

-- =====================================================
-- 8. 更新租户表，添加隔离类型字段
-- =====================================================

-- 为租户表添加隔离类型字段
ALTER TABLE sys_tenant 
ADD COLUMN IF NOT EXISTS isolation_type INTEGER DEFAULT 1 COMMENT '隔离类型：1-字段隔离，2-数据源隔离';

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_sys_tenant_isolation_type ON sys_tenant(isolation_type);

-- 更新现有租户的隔离类型
UPDATE sys_tenant SET isolation_type = 1 WHERE isolation_type IS NULL;
