-- RegaWebERP 多数据源管理表结构
-- 版本: V1.0.2
-- 描述: 创建多数据源管理相关表（数据源配置、租户数据源关联等）

-- =====================================================
-- 1. 数据源配置表
-- =====================================================

-- 数据源配置表
CREATE TABLE IF NOT EXISTS sys_datasource (
    id BIGINT PRIMARY KEY,
    datasource_name VARCHAR(100) NOT NULL,
    datasource_key VARCHAR(50) NOT NULL UNIQUE,
    datasource_type VARCHAR(20) NOT NULL DEFAULT 'postgresql',
    driver_class_name VARCHAR(200) NOT NULL,
    url VARCHAR(500) NOT NULL,
    username VARCHAR(100) NOT NULL,
    password VARCHAR(200) NOT NULL,
    initial_size INTEGER DEFAULT 5,
    min_idle INTEGER DEFAULT 5,
    max_active INTEGER DEFAULT 20,
    max_wait BIGINT DEFAULT 60000,
    test_on_borrow BOOLEAN DEFAULT true,
    test_on_return BOOLEAN DEFAULT false,
    test_while_idle BOOLEAN DEFAULT true,
    validation_query VARCHAR(100) DEFAULT 'SELECT 1',
    time_between_eviction_runs_millis BIGINT DEFAULT 60000,
    min_evictable_idle_time_millis BIGINT DEFAULT 300000,
    pool_prepared_statements BOOLEAN DEFAULT true,
    max_pool_prepared_statement_per_connection_size INTEGER DEFAULT 20,
    connection_properties TEXT,
    status INTEGER NOT NULL DEFAULT 1,
    is_default BOOLEAN DEFAULT false,
    health_check_enabled BOOLEAN DEFAULT true,
    health_check_interval BIGINT DEFAULT 300000,
    max_retry_count INTEGER DEFAULT 3,
    description VARCHAR(500),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0
);

-- 添加 sys_datasource 表字段备注
COMMENT ON COLUMN sys_datasource.id IS '主键ID';
COMMENT ON COLUMN sys_datasource.datasource_name IS '数据源名称';
COMMENT ON COLUMN sys_datasource.datasource_key IS '数据源标识键';
COMMENT ON COLUMN sys_datasource.datasource_type IS '数据源类型：postgresql, mysql, oracle等';
COMMENT ON COLUMN sys_datasource.driver_class_name IS '驱动类名';
COMMENT ON COLUMN sys_datasource.url IS '数据库连接URL';
COMMENT ON COLUMN sys_datasource.username IS '用户名';
COMMENT ON COLUMN sys_datasource.password IS '密码（加密存储）';
COMMENT ON COLUMN sys_datasource.initial_size IS '初始连接数';
COMMENT ON COLUMN sys_datasource.min_idle IS '最小空闲连接数';
COMMENT ON COLUMN sys_datasource.max_active IS '最大活跃连接数';
COMMENT ON COLUMN sys_datasource.max_wait IS '获取连接最大等待时间（毫秒）';
COMMENT ON COLUMN sys_datasource.test_on_borrow IS '获取连接时是否测试';
COMMENT ON COLUMN sys_datasource.test_on_return IS '归还连接时是否测试';
COMMENT ON COLUMN sys_datasource.test_while_idle IS '空闲时是否测试连接';
COMMENT ON COLUMN sys_datasource.validation_query IS '验证查询SQL';
COMMENT ON COLUMN sys_datasource.time_between_eviction_runs_millis IS '空闲连接回收器运行间隔（毫秒）';
COMMENT ON COLUMN sys_datasource.min_evictable_idle_time_millis IS '连接在池中保持空闲的最小时间（毫秒）';
COMMENT ON COLUMN sys_datasource.pool_prepared_statements IS '是否缓存PreparedStatement';
COMMENT ON COLUMN sys_datasource.max_pool_prepared_statement_per_connection_size IS '每个连接上PSCache的大小';
COMMENT ON COLUMN sys_datasource.connection_properties IS '连接属性（JSON格式）';
COMMENT ON COLUMN sys_datasource.status IS '状态：1-启用，0-禁用';
COMMENT ON COLUMN sys_datasource.is_default IS '是否默认数据源';
COMMENT ON COLUMN sys_datasource.health_check_enabled IS '是否启用健康检查';
COMMENT ON COLUMN sys_datasource.health_check_interval IS '健康检查间隔（毫秒）';
COMMENT ON COLUMN sys_datasource.max_retry_count IS '连接失败最大重试次数';
COMMENT ON COLUMN sys_datasource.description IS '数据源描述';
COMMENT ON COLUMN sys_datasource.create_time IS '创建时间';
COMMENT ON COLUMN sys_datasource.update_time IS '更新时间';
COMMENT ON COLUMN sys_datasource.create_by IS '创建人';
COMMENT ON COLUMN sys_datasource.update_by IS '更新人';
COMMENT ON COLUMN sys_datasource.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_datasource_key ON sys_datasource(datasource_key);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_type ON sys_datasource(datasource_type);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_status ON sys_datasource(status);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_default ON sys_datasource(is_default);

-- =====================================================
-- 2. 租户数据源关联表
-- =====================================================

-- 租户数据源关联表
CREATE TABLE IF NOT EXISTS sys_tenant_datasource (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    datasource_id BIGINT NOT NULL,
    isolation_type INTEGER NOT NULL DEFAULT 1,
    is_primary BOOLEAN DEFAULT false,
    priority INTEGER DEFAULT 0,
    read_weight INTEGER DEFAULT 1,
    write_weight INTEGER DEFAULT 1,
    status INTEGER NOT NULL DEFAULT 1,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT
);

-- 添加 sys_tenant_datasource 表字段备注
COMMENT ON COLUMN sys_tenant_datasource.id IS '主键ID';
COMMENT ON COLUMN sys_tenant_datasource.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_tenant_datasource.datasource_id IS '数据源ID';
COMMENT ON COLUMN sys_tenant_datasource.isolation_type IS '隔离类型：1-字段隔离，2-数据源隔离';
COMMENT ON COLUMN sys_tenant_datasource.is_primary IS '是否主数据源';
COMMENT ON COLUMN sys_tenant_datasource.priority IS '优先级，数字越小优先级越高';
COMMENT ON COLUMN sys_tenant_datasource.read_weight IS '读权重';
COMMENT ON COLUMN sys_tenant_datasource.write_weight IS '写权重';
COMMENT ON COLUMN sys_tenant_datasource.status IS '状态：1-启用，0-禁用';
COMMENT ON COLUMN sys_tenant_datasource.create_time IS '创建时间';
COMMENT ON COLUMN sys_tenant_datasource.update_time IS '更新时间';
COMMENT ON COLUMN sys_tenant_datasource.create_by IS '创建人';
COMMENT ON COLUMN sys_tenant_datasource.update_by IS '更新人';

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_tenant_datasource ON sys_tenant_datasource(tenant_id, datasource_id);
CREATE INDEX IF NOT EXISTS idx_sys_tenant_datasource_tenant_id ON sys_tenant_datasource(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sys_tenant_datasource_datasource_id ON sys_tenant_datasource(datasource_id);
CREATE INDEX IF NOT EXISTS idx_sys_tenant_datasource_primary ON sys_tenant_datasource(is_primary);

-- =====================================================
-- 3. 数据源监控表
-- =====================================================

-- 数据源监控表
CREATE TABLE IF NOT EXISTS sys_datasource_monitor (
    id BIGINT PRIMARY KEY,
    datasource_id BIGINT NOT NULL,
    check_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status INTEGER NOT NULL,
    response_time BIGINT,
    active_connections INTEGER,
    idle_connections INTEGER,
    total_connections INTEGER,
    error_message TEXT,
    cpu_usage DECIMAL(5,2),
    memory_usage DECIMAL(5,2),
    disk_usage DECIMAL(5,2),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加 sys_datasource_monitor 表字段备注
COMMENT ON COLUMN sys_datasource_monitor.id IS '主键ID';
COMMENT ON COLUMN sys_datasource_monitor.datasource_id IS '数据源ID';
COMMENT ON COLUMN sys_datasource_monitor.check_time IS '检查时间';
COMMENT ON COLUMN sys_datasource_monitor.status IS '状态：1-正常，0-异常';
COMMENT ON COLUMN sys_datasource_monitor.response_time IS '响应时间（毫秒）';
COMMENT ON COLUMN sys_datasource_monitor.active_connections IS '活跃连接数';
COMMENT ON COLUMN sys_datasource_monitor.idle_connections IS '空闲连接数';
COMMENT ON COLUMN sys_datasource_monitor.total_connections IS '总连接数';
COMMENT ON COLUMN sys_datasource_monitor.error_message IS '错误信息';
COMMENT ON COLUMN sys_datasource_monitor.cpu_usage IS 'CPU使用率';
COMMENT ON COLUMN sys_datasource_monitor.memory_usage IS '内存使用率';
COMMENT ON COLUMN sys_datasource_monitor.disk_usage IS '磁盘使用率';
COMMENT ON COLUMN sys_datasource_monitor.create_time IS '创建时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_datasource_monitor_datasource_id ON sys_datasource_monitor(datasource_id);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_monitor_check_time ON sys_datasource_monitor(check_time);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_monitor_status ON sys_datasource_monitor(status);

-- =====================================================
-- 4. 数据源操作日志表
-- =====================================================

-- 数据源操作日志表
CREATE TABLE IF NOT EXISTS sys_datasource_log (
    id BIGINT PRIMARY KEY,
    datasource_id BIGINT,
    tenant_id BIGINT,
    operation_type VARCHAR(50) NOT NULL,
    operation_desc VARCHAR(200),
    operator_id BIGINT,
    operator_name VARCHAR(50),
    ip_address VARCHAR(50),
    user_agent VARCHAR(500),
    status INTEGER NOT NULL,
    error_message TEXT,
    cost_time BIGINT,
    request_params TEXT,
    response_data TEXT,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加 sys_datasource_log 表字段备注
COMMENT ON COLUMN sys_datasource_log.id IS '主键ID';
COMMENT ON COLUMN sys_datasource_log.datasource_id IS '数据源ID';
COMMENT ON COLUMN sys_datasource_log.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_datasource_log.operation_type IS '操作类型：CREATE, UPDATE, DELETE, CONNECT, DISCONNECT';
COMMENT ON COLUMN sys_datasource_log.operation_desc IS '操作描述';
COMMENT ON COLUMN sys_datasource_log.operator_id IS '操作人ID';
COMMENT ON COLUMN sys_datasource_log.operator_name IS '操作人姓名';
COMMENT ON COLUMN sys_datasource_log.ip_address IS 'IP地址';
COMMENT ON COLUMN sys_datasource_log.user_agent IS '用户代理';
COMMENT ON COLUMN sys_datasource_log.status IS '操作状态：1-成功，0-失败';
COMMENT ON COLUMN sys_datasource_log.error_message IS '错误信息';
COMMENT ON COLUMN sys_datasource_log.cost_time IS '耗时（毫秒）';
COMMENT ON COLUMN sys_datasource_log.request_params IS '请求参数（JSON格式）';
COMMENT ON COLUMN sys_datasource_log.response_data IS '响应数据（JSON格式）';
COMMENT ON COLUMN sys_datasource_log.create_time IS '创建时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_datasource_log_datasource_id ON sys_datasource_log(datasource_id);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_log_tenant_id ON sys_datasource_log(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_log_operation_type ON sys_datasource_log(operation_type);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_log_create_time ON sys_datasource_log(create_time);

-- =====================================================
-- 5. 数据源配置模板表
-- =====================================================

-- 数据源配置模板表
CREATE TABLE IF NOT EXISTS sys_datasource_template (
    id BIGINT PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL,
    template_code VARCHAR(50) NOT NULL UNIQUE,
    datasource_type VARCHAR(20) NOT NULL,
    driver_class_name VARCHAR(200) NOT NULL,
    url_template VARCHAR(500) NOT NULL,
    default_port INTEGER,
    validation_query VARCHAR(100),
    connection_properties TEXT,
    pool_config TEXT,
    description VARCHAR(500),
    is_system BOOLEAN DEFAULT false,
    status INTEGER NOT NULL DEFAULT 1,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0
);

-- 添加 sys_datasource_template 表字段备注
COMMENT ON COLUMN sys_datasource_template.id IS '主键ID';
COMMENT ON COLUMN sys_datasource_template.template_name IS '模板名称';
COMMENT ON COLUMN sys_datasource_template.template_code IS '模板编码';
COMMENT ON COLUMN sys_datasource_template.datasource_type IS '数据源类型';
COMMENT ON COLUMN sys_datasource_template.driver_class_name IS '驱动类名';
COMMENT ON COLUMN sys_datasource_template.url_template IS 'URL模板';
COMMENT ON COLUMN sys_datasource_template.default_port IS '默认端口';
COMMENT ON COLUMN sys_datasource_template.validation_query IS '验证查询SQL';
COMMENT ON COLUMN sys_datasource_template.connection_properties IS '默认连接属性（JSON格式）';
COMMENT ON COLUMN sys_datasource_template.pool_config IS '连接池配置（JSON格式）';
COMMENT ON COLUMN sys_datasource_template.description IS '模板描述';
COMMENT ON COLUMN sys_datasource_template.is_system IS '是否系统模板';
COMMENT ON COLUMN sys_datasource_template.status IS '状态：1-启用，0-禁用';
COMMENT ON COLUMN sys_datasource_template.create_time IS '创建时间';
COMMENT ON COLUMN sys_datasource_template.update_time IS '更新时间';
COMMENT ON COLUMN sys_datasource_template.create_by IS '创建人';
COMMENT ON COLUMN sys_datasource_template.update_by IS '更新人';
COMMENT ON COLUMN sys_datasource_template.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_datasource_template_code ON sys_datasource_template(template_code);
CREATE INDEX IF NOT EXISTS idx_sys_datasource_template_type ON sys_datasource_template(datasource_type);

-- =====================================================
-- 6. 添加外键约束
-- =====================================================

-- 租户数据源关联表外键
ALTER TABLE sys_tenant_datasource 
ADD CONSTRAINT fk_tenant_datasource_tenant_id 
FOREIGN KEY (tenant_id) REFERENCES sys_tenant(id) ON DELETE CASCADE;

ALTER TABLE sys_tenant_datasource 
ADD CONSTRAINT fk_tenant_datasource_datasource_id 
FOREIGN KEY (datasource_id) REFERENCES sys_datasource(id) ON DELETE CASCADE;

-- 数据源监控表外键
ALTER TABLE sys_datasource_monitor 
ADD CONSTRAINT fk_datasource_monitor_datasource_id 
FOREIGN KEY (datasource_id) REFERENCES sys_datasource(id) ON DELETE CASCADE;

-- 数据源操作日志表外键
ALTER TABLE sys_datasource_log 
ADD CONSTRAINT fk_datasource_log_datasource_id 
FOREIGN KEY (datasource_id) REFERENCES sys_datasource(id) ON DELETE SET NULL;

ALTER TABLE sys_datasource_log 
ADD CONSTRAINT fk_datasource_log_tenant_id 
FOREIGN KEY (tenant_id) REFERENCES sys_tenant(id) ON DELETE SET NULL;

-- =====================================================
-- 7. 添加表注释
-- =====================================================

COMMENT ON TABLE sys_datasource IS '数据源配置表';
COMMENT ON TABLE sys_tenant_datasource IS '租户数据源关联表';
COMMENT ON TABLE sys_datasource_monitor IS '数据源监控表';
COMMENT ON TABLE sys_datasource_log IS '数据源操作日志表';
COMMENT ON TABLE sys_datasource_template IS '数据源配置模板表';

-- =====================================================
-- 8. 更新租户表，添加隔离类型字段
-- =====================================================

-- 为租户表添加隔离类型字段
ALTER TABLE sys_tenant
ADD COLUMN IF NOT EXISTS isolation_type INTEGER DEFAULT 1;

-- 添加字段备注
COMMENT ON COLUMN sys_tenant.isolation_type IS '隔离类型：1-字段隔离，2-数据源隔离';

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_sys_tenant_isolation_type ON sys_tenant(isolation_type);

-- 更新现有租户的隔离类型
UPDATE sys_tenant SET isolation_type = 1 WHERE isolation_type IS NULL;
