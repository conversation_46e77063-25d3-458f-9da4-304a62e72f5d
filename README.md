# RegaWebERP 企业资源规划系统

## 📋 项目概述

RegaWebERP 是一个基于 Spring Boot 3.x 的现代化企业资源规划（ERP）系统，采用微服务架构设计，支持多租户、动态表单、工作流引擎等核心功能。

## ✨ 核心特性

### 🎯 系统特性
- **多租户架构**：支持 SaaS 模式的多租户数据隔离
- **动态表单**：可视化表单设计器，支持复杂业务表单
- **工作流引擎**：灵活的业务流程管理和审批流程
- **权限管理**：基于 RBAC 的细粒度权限控制
- **统一缓存**：多级缓存架构，支持 Redis + 本地缓存
- **API 优先**：完整的 RESTful API 和在线文档

### 🏗️ 技术架构
```
RegaWebERP/
├── rega-api/              # API 应用启动模块
├── rega-common/           # 公共模块
│   ├── rega-common-core/     # 核心工具和异常处理
│   ├── rega-common-cache/    # 统一缓存服务
│   ├── rega-common-security/ # 安全认证模块
│   ├── rega-common-db/       # 数据库访问层
│   ├── rega-common-log/      # 日志管理
│   ├── rega-common-i18n/     # 国际化支持
│   └── rega-common-sdk/      # 业务开发 SDK
├── rega-system/           # 系统管理模块
├── rega-tenant/           # 多租户管理
├── rega-form/             # 动态表单引擎
├── rega-workflow/         # 工作流引擎
└── rega-report/           # 报表管理
```

## 🛠️ 技术栈

| 技术 | 版本 | 说明 |
|------|------|------|
| Java | 17 | 编程语言 |
| Spring Boot | 3.1.x | 应用框架 |
| Sa-Token | 1.34.0 | 认证与权限框架 |
| Anyline | 8.7.x | ORM 框架 |
| PostgreSQL | 17.x+ | 数据库 |
| Redis | 7.x+ | 分布式缓存 |
| JetCache | 2.7.x | 多级缓存框架 |
| Redisson | 3.20.x | Redis 客户端与分布式锁 |
| CosID | 2.5.x | 分布式 ID 生成器 |
| Maven | 3.6.3+ | 项目管理 |

## 🚀 快速开始

### 1. 环境要求

- JDK 17+
- Maven 3.6.3+
- PostgreSQL 17+
- Redis 7+

### 2. 克隆项目

```bash
git clone https://github.com/your-org/RegaWebERP.git
cd RegaWebERP/rega-parent
```

### 3. 配置数据库

```yaml
# application.yml
spring:
  datasource:
    url: *****************************************
    username: your_username
    password: your_password
  data:
    redis:
      host: localhost
      port: 6379
```

### 4. 启动应用

```bash
# 编译项目
mvn clean package -DskipTests

# 启动应用
cd rega-api
java -jar target/rega-api-1.0.0-SNAPSHOT.jar
```

### 5. 访问应用

- **应用地址**: http://localhost:8080/api
- **API 文档**: http://localhost:8080/api/doc.html
- **健康检查**: http://localhost:8080/api/actuator/health

## 📊 项目状态

### ✅ 已完成
- **基础架构** - Spring Boot 应用成功启动和运行
- **缓存模块** - 统一缓存服务，支持 Redis + JetCache 多级缓存
- **安全框架** - Sa-Token 认证授权框架集成
- **数据库层** - Anyline ORM 框架集成
- **ID 生成器** - CosID 分布式 ID 生成服务
- **项目结构** - 模块化架构设计和依赖管理

### 🚧 开发中
- **数据库连接** - PostgreSQL 数据源配置
- **异常处理** - 统一异常处理机制
- **API 文档** - Knife4j 接口文档集成

### 📋 待开发
- **用户管理** - 用户注册、登录、权限管理
- **多租户** - 租户管理和数据隔离
- **动态表单** - 表单设计器和表单引擎
- **工作流** - 流程设计和执行引擎
- **报表系统** - 报表设计和生成

## 📖 文档

### 核心文档
- [开发路线图](docs/development-roadmap.md) - 详细的开发计划和时间安排
- [后端规范](docs/backend-specification.md) - 技术架构和开发规范
- [功能清单](docs/feature-list.md) - 完整的功能需求列表

### 技术文档
- [架构简化报告](docs/ARCHITECTURE_SIMPLIFICATION.md) - 项目架构优化记录
- [缓存模块总结](docs/CACHE_MODULE_SUMMARY.md) - 缓存模块设计和实现
- [开发规则](docs/rules.md) - 代码规范和最佳实践

## 🏗️ 模块说明

| 模块名称 | 状态 | 说明 |
|---------|------|------|
| rega-api | ✅ 完成 | API 应用启动模块，整合各业务模块 |
| rega-common-core | ✅ 完成 | 核心工具类、异常处理、常量定义 |
| rega-common-cache | ✅ 完成 | 统一缓存服务，支持多级缓存和租户隔离 |
| rega-common-security | 🚧 开发中 | 安全认证、授权、JWT 处理 |
| rega-common-db | 🚧 开发中 | 数据库配置、多租户数据隔离 |
| rega-common-sdk | 📋 待开发 | 基于 Anyline 的业务开发 SDK |
| rega-system | 📋 待开发 | 系统管理功能，用户、角色、权限管理 |
| rega-tenant | 📋 待开发 | 多租户管理功能，租户管理和数据隔离 |
| rega-form | 📋 待开发 | 动态表单引擎，表单设计和渲染 |
| rega-workflow | 📋 待开发 | 工作流引擎，流程设计和执行 |
| rega-report | 📋 待开发 | 报表功能，报表设计和生成 |

## ⚙️ 配置说明

### 应用配置

```yaml
server:
  servlet:
    context-path: /api

spring:
  application:
    name: rega-api
  datasource:
    url: *****************************************
    username: ${DB_USERNAME:rega_user}
    password: ${DB_PASSWORD:rega_pass}
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DB:0}
```

### 缓存配置

```yaml
rega:
  cache:
    enabled: true
    tenant-isolation: true

jetcache:
  statIntervalMinutes: 15
  local:
    default:
      type: caffeine
      limit: 1000
      expireAfterWriteInMillis: 7200000
  remote:
    default:
      type: redisson
      expireAfterWriteInMillis: 7200000
```

### 安全配置

```yaml
sa-token:
  token-name: rega-token
  timeout: 2592000
  is-concurrent: true
  token-style: uuid
```

## 🔧 开发指南

### 环境搭建

1. **安装依赖**
   ```bash
   # 安装 JDK 17
   # 安装 Maven 3.6.3+
   # 安装 PostgreSQL 17+
   # 安装 Redis 7+
   ```

2. **数据库初始化**
   ```sql
   CREATE DATABASE rega_erp;
   CREATE USER rega_user WITH PASSWORD 'rega_pass';
   GRANT ALL PRIVILEGES ON DATABASE rega_erp TO rega_user;
   ```

3. **启动 Redis**
   ```bash
   redis-server
   ```

### 开发流程

1. **克隆代码**
   ```bash
   git clone <repository-url>
   cd RegaWebERP/rega-parent
   ```

2. **编译项目**
   ```bash
   mvn clean compile
   ```

3. **运行测试**
   ```bash
   mvn test
   ```

4. **启动应用**
   ```bash
   cd rega-api
   mvn spring-boot:run
   ```

### 代码规范

- 遵循 Java 编码规范
- 使用 Lombok 减少样板代码
- 统一异常处理机制
- 完善的单元测试覆盖

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
mvn test

# 运行特定模块测试
mvn test -pl rega-common-cache

# 跳过测试编译
mvn clean package -DskipTests
```

### 测试覆盖率

```bash
# 生成测试覆盖率报告
mvn jacoco:report
```

## 📈 性能监控

### 应用监控

- **健康检查**: `/api/actuator/health`
- **应用信息**: `/api/actuator/info`
- **性能指标**: `/api/actuator/metrics`

### 缓存监控

- 缓存命中率统计
- 缓存大小监控
- 缓存操作性能指标

## 🤝 贡献指南

### 提交代码

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request


**RegaWebERP** - 现代化企业资源规划系统
© 2024 RegaWebERP Team. All rights reserved.
