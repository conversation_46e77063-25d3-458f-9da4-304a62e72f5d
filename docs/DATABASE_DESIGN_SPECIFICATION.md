# RegaWebERP 数据库设计规范

## 📋 概述

本文档定义了 RegaWebERP 项目的数据库设计规范，包括命名约定、字段类型、索引规则等，确保数据库设计的一致性和可维护性。

## 🏗️ 基础设计原则

### 1. 设计理念
- **一致性优先**：所有表结构遵循统一规范
- **可扩展性**：支持业务功能扩展
- **多租户支持**：所有业务表必须支持租户隔离
- **审计完整**：记录数据变更历史
- **性能优化**：合理设计索引和分区

### 2. 技术选型
- **数据库**：PostgreSQL 17.x+
- **字符集**：UTF-8
- **时区**：Asia/Shanghai
- **ID生成**：CosID 雪花算法

## 📝 命名规范

### 1. 表命名规范

#### 表名格式
```
{模块前缀}_{业务名称}
```

#### 模块前缀定义
| 前缀 | 模块 | 说明 |
|------|------|------|
| `sys_` | 系统管理 | 用户、角色、权限、配置等 |
| `tenant_` | 多租户 | 租户管理相关 |
| `form_` | 动态表单 | 表单设计、数据等 |
| `workflow_` | 工作流 | 流程定义、实例等 |
| `report_` | 报表 | 报表模板、数据等 |
| `biz_` | 业务数据 | 具体业务表 |

#### 表名示例
```sql
-- 系统管理
sys_user          -- 用户表
sys_role          -- 角色表
sys_permission    -- 权限表

-- 多租户
tenant_info       -- 租户信息
tenant_config     -- 租户配置

-- 动态表单
form_template     -- 表单模板
form_data         -- 表单数据

-- 工作流
workflow_definition  -- 流程定义
workflow_instance    -- 流程实例
```

### 2. 字段命名规范

#### 基本规则
- 使用小写字母和下划线
- 见名知意，避免缩写
- 布尔字段使用 `is_` 前缀
- 状态字段使用 `status` 后缀
- 时间字段使用 `_time` 后缀

#### 标准字段命名
| 字段类型 | 字段名 | 说明 |
|----------|--------|------|
| 主键 | `id` | 表主键 |
| 租户ID | `tenant_id` | 租户标识 |
| 名称 | `name` / `{业务}_name` | 通用名称字段 |
| 编码 | `code` / `{业务}_code` | 业务编码 |
| 状态 | `status` | 状态字段 |
| 类型 | `type` / `{业务}_type` | 类型字段 |
| 排序 | `sort_order` | 排序字段 |
| 备注 | `remark` | 备注说明 |
| 创建时间 | `create_time` | 记录创建时间 |
| 更新时间 | `update_time` | 记录更新时间 |
| 创建人 | `create_by` | 创建人ID |
| 更新人 | `update_by` | 更新人ID |
| 删除标记 | `deleted` | 逻辑删除标记 |

## 🔢 字段类型规范

### 1. 主键字段
```sql
-- 统一使用 BIGINT 类型，支持雪花算法生成的长整型ID
id BIGINT PRIMARY KEY
```

### 2. 外键字段
```sql
-- 所有外键字段统一使用 BIGINT 类型
tenant_id BIGINT NOT NULL COMMENT '租户ID'
user_id BIGINT COMMENT '用户ID'
parent_id BIGINT DEFAULT 0 COMMENT '父级ID'
```

### 3. 字符串字段
```sql
-- 编码字段：50字符
code VARCHAR(50) NOT NULL COMMENT '编码'

-- 名称字段：100字符
name VARCHAR(100) NOT NULL COMMENT '名称'

-- 短文本：200字符以内
title VARCHAR(200) COMMENT '标题'
url VARCHAR(200) COMMENT 'URL地址'

-- 长文本：使用 TEXT
content TEXT COMMENT '内容'
remark VARCHAR(500) COMMENT '备注'
```

### 4. 数值字段
```sql
-- 整数类型
status INTEGER NOT NULL DEFAULT 1 COMMENT '状态'
sort_order INTEGER DEFAULT 0 COMMENT '排序'
count INTEGER DEFAULT 0 COMMENT '数量'

-- 金额类型（使用 DECIMAL 避免精度问题）
amount DECIMAL(15,2) COMMENT '金额'
price DECIMAL(10,2) COMMENT '价格'

-- 百分比（存储为小数）
rate DECIMAL(5,4) COMMENT '比率'
```

### 5. 时间字段
```sql
-- 时间戳类型（推荐）
create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'

-- 日期类型
birthday DATE COMMENT '生日'
expire_date DATE COMMENT '过期日期'
```

### 6. 布尔字段
```sql
-- 使用 INTEGER 类型，0-否，1-是
is_enabled INTEGER NOT NULL DEFAULT 1 COMMENT '是否启用'
is_system INTEGER NOT NULL DEFAULT 0 COMMENT '是否系统数据'
deleted INTEGER NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
```

## 🏷️ 标准字段定义

### 1. 必备字段（所有表）
```sql
-- 主键
id BIGINT PRIMARY KEY,

-- 创建和更新信息
create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
create_by BIGINT COMMENT '创建人',
update_by BIGINT COMMENT '更新人',

-- 逻辑删除
deleted INTEGER NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
```

### 2. 业务表必备字段
```sql
-- 租户隔离（除系统表外）
tenant_id BIGINT NOT NULL COMMENT '租户ID',

-- 状态控制
status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',

-- 备注信息
remark VARCHAR(500) COMMENT '备注'
```

### 3. 树形结构表
```sql
-- 父级关系
parent_id BIGINT DEFAULT 0 COMMENT '父级ID',

-- 层级路径（可选）
path VARCHAR(500) COMMENT '层级路径',

-- 排序
sort_order INTEGER DEFAULT 0 COMMENT '排序'
```

## 📊 索引设计规范

### 1. 主键索引
```sql
-- 自动创建，无需手动定义
PRIMARY KEY (id)
```

### 2. 唯一索引
```sql
-- 业务唯一性约束
CREATE UNIQUE INDEX uk_{table}_{field} ON {table}({field}) WHERE deleted = 0;

-- 多字段唯一约束
CREATE UNIQUE INDEX uk_{table}_{field1}_{field2} ON {table}({field1}, {field2}) WHERE deleted = 0;

-- 示例
CREATE UNIQUE INDEX uk_sys_user_tenant_username ON sys_user(tenant_id, username) WHERE deleted = 0;
```

### 3. 普通索引
```sql
-- 外键索引
CREATE INDEX idx_{table}_{foreign_key} ON {table}({foreign_key});

-- 查询条件索引
CREATE INDEX idx_{table}_{field} ON {table}({field});

-- 组合索引
CREATE INDEX idx_{table}_{field1}_{field2} ON {table}({field1}, {field2});

-- 示例
CREATE INDEX idx_sys_user_tenant_id ON sys_user(tenant_id);
CREATE INDEX idx_sys_user_status ON sys_user(status);
```

### 4. 索引命名规范
| 索引类型 | 命名格式 | 示例 |
|----------|----------|------|
| 主键索引 | `pk_{table}` | `pk_sys_user` |
| 唯一索引 | `uk_{table}_{field}` | `uk_sys_user_username` |
| 普通索引 | `idx_{table}_{field}` | `idx_sys_user_status` |
| 外键索引 | `fk_{table}_{ref_table}` | `fk_user_role_user` |

## 🔐 约束规范

### 1. 非空约束
```sql
-- 必填字段添加 NOT NULL
tenant_id BIGINT NOT NULL COMMENT '租户ID',
username VARCHAR(50) NOT NULL COMMENT '用户名',
create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
```

### 2. 默认值约束
```sql
-- 状态字段默认值
status INTEGER NOT NULL DEFAULT 1 COMMENT '状态',

-- 数值字段默认值
sort_order INTEGER DEFAULT 0 COMMENT '排序',
login_count INTEGER DEFAULT 0 COMMENT '登录次数',

-- 时间字段默认值
create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
```

### 3. 检查约束
```sql
-- 状态值检查
CONSTRAINT chk_status CHECK (status IN (0, 1)),

-- 数值范围检查
CONSTRAINT chk_sort_order CHECK (sort_order >= 0),

-- 字符长度检查
CONSTRAINT chk_username_length CHECK (LENGTH(username) >= 3)
```

## 📝 注释规范

### 1. 表注释
```sql
-- 表注释格式
COMMENT ON TABLE {table_name} IS '{表中文名称}';

-- 示例
COMMENT ON TABLE sys_user IS '用户信息表';
COMMENT ON TABLE sys_role IS '角色信息表';
```

### 2. 字段注释
```sql
-- 字段注释格式（在字段定义中）
{field_name} {data_type} COMMENT '{字段说明}'

-- 枚举值说明
status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
gender INTEGER COMMENT '性别：1-男，2-女，0-未知',
user_type INTEGER COMMENT '用户类型：1-管理员，2-普通用户，3-访客'
```

## 🔄 数据迁移规范

### 1. 脚本命名
```
V{版本号}__{描述}.sql

示例：
V1.0.0__init_database.sql      -- 初始化数据库
V1.0.1__init_data.sql          -- 初始化数据
V1.1.0__add_user_avatar.sql    -- 添加用户头像字段
V1.2.0__create_form_module.sql -- 创建表单模块
```

### 2. 脚本结构
```sql
-- 脚本头部注释
-- RegaWebERP 数据库变更脚本
-- 版本: V1.1.0
-- 描述: 添加用户头像功能
-- 作者: 开发者姓名
-- 日期: 2024-01-01

-- 变更内容
ALTER TABLE sys_user ADD COLUMN avatar VARCHAR(200) COMMENT '头像URL';

-- 创建索引（如需要）
CREATE INDEX idx_sys_user_avatar ON sys_user(avatar);

-- 数据迁移（如需要）
UPDATE sys_user SET avatar = '/default/avatar.png' WHERE avatar IS NULL;
```

## 🎯 最佳实践

### 1. 性能优化
- 合理使用索引，避免过度索引
- 大表考虑分区策略
- 定期分析表统计信息
- 监控慢查询并优化

### 2. 数据安全
- 敏感数据加密存储
- 重要操作记录审计日志
- 定期备份数据
- 控制数据访问权限

### 3. 维护性
- 统一的命名规范
- 完整的字段注释
- 版本化的迁移脚本
- 文档与代码同步更新

### 4. 扩展性
- 预留扩展字段
- 支持多租户架构
- 考虑国际化需求
- 支持业务配置化

## 📋 检查清单

### 新建表检查项
- [ ] 表名符合命名规范
- [ ] 包含所有必备字段
- [ ] 字段类型选择合适
- [ ] 添加必要索引
- [ ] 设置合理约束
- [ ] 编写完整注释
- [ ] 考虑多租户隔离
- [ ] 支持逻辑删除

### 字段设计检查项
- [ ] 字段名称规范
- [ ] 数据类型合适
- [ ] 长度设置合理
- [ ] 默认值正确
- [ ] 非空约束合理
- [ ] 注释信息完整

### 索引设计检查项
- [ ] 主键索引存在
- [ ] 外键字段有索引
- [ ] 查询条件有索引
- [ ] 唯一约束正确
- [ ] 索引命名规范
- [ ] 避免冗余索引

---

**遵循本规范，确保 RegaWebERP 数据库设计的一致性和可维护性！** 📊

**最后更新**: 2025-06-17  
**版本**: v1.0  
**维护者**: RegaWebERP 开发团队
