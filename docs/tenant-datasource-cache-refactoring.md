# TenantDataSourceManager 缓存重构总结

## 概述

根据用户要求，我们已经成功重构了 `TenantDataSourceManager`，使其基于 `common-cache` 模块中已定义的通用缓存方法，避免了重复造轮子，实现了数据源配置从数据库加载后的统一缓存管理。

## 重构内容

### 1. 新增 DataSourceCacheManager

**位置**: `rega-common/rega-common-db/src/main/java/com/rega/erp/common/db/tenant/cache/DataSourceCacheManager.java`

#### 核心功能
- **数据源配置缓存**: 缓存数据源配置信息，支持按ID和按键查询
- **租户数据源关联缓存**: 缓存租户与数据源的关联关系
- **数据源使用统计缓存**: 实时跟踪数据源使用情况
- **数据源健康状态缓存**: 缓存健康检查结果

#### 缓存策略
```java
// 缓存过期时间配置
private static final Duration DATASOURCE_CONFIG_TTL = Duration.ofHours(1);      // 数据源配置缓存1小时
private static final Duration TENANT_DATASOURCE_TTL = Duration.ofMinutes(30);   // 租户数据源关联缓存30分钟
private static final Duration DATASOURCE_USAGE_TTL = Duration.ofMinutes(10);    // 数据源使用统计缓存10分钟
private static final Duration DATASOURCE_HEALTH_TTL = Duration.ofMinutes(5);    // 数据源健康状态缓存5分钟
```

#### 主要方法
```java
// 数据源配置缓存
public void cacheDataSourceConfig(Long datasourceId, SysDataSource dataSource)
public SysDataSource getDataSourceConfig(Long datasourceId, Function<Long, SysDataSource> loader)
public SysDataSource getDataSourceConfigByKey(String datasourceKey, Function<String, SysDataSource> loader)

// 租户数据源关联缓存
public void cacheTenantDataSources(Long tenantId, List<SysTenantDataSource> tenantDataSources)
public List<SysTenantDataSource> getTenantDataSources(Long tenantId, Function<Long, List<SysTenantDataSource>> loader)

// 数据源使用统计缓存
public void cacheDataSourceUsage(String datasourceKey, Integer usageCount)
public Integer getDataSourceUsage(String datasourceKey, Function<String, Integer> loader)
public long incrementDataSourceUsage(String datasourceKey, long delta)
public long decrementDataSourceUsage(String datasourceKey, long delta)

// 数据源健康状态缓存
public void cacheDataSourceHealth(String datasourceKey, Boolean isHealthy)
public Boolean getDataSourceHealth(String datasourceKey)
```

### 2. 重构 TenantDataSourceManager

#### 移除的内容
- ❌ 删除了自定义的 `Map<Long, String> tenantDataSourceCache`
- ❌ 删除了自定义的 `Map<String, Integer> dataSourceUsageCache`
- ❌ 删除了手动缓存管理逻辑

#### 新增的依赖
- ✅ 注入 `DataSourceCacheManager cacheManager`
- ✅ 使用 common-cache 模块的统一缓存接口

#### 重构的方法

##### 数据源分配方法
```java
// 原来的手动缓存更新
tenantDataSourceCache.put(tenantId, assignedDataSourceKey);
updateDataSourceUsageCache(assignedDataSourceKey, 1);

// 重构后使用缓存管理器
cacheManager.incrementDataSourceUsage(assignedDataSourceKey, 1);
cacheManager.evictTenantDataSources(tenantId);
```

##### 获取租户数据源方法
```java
// 原来的手动缓存查询
String dataSourceKey = tenantDataSourceCache.get(tenantId);

// 重构后使用缓存管理器
List<SysTenantDataSource> tenantDataSources = cacheManager.getTenantDataSources(
    tenantId, dataSourceService::getTenantDataSources);
```

##### 健康检查方法
```java
// 原来没有缓存
// TODO: 实现具体的健康检查逻辑

// 重构后使用缓存
Boolean cachedHealth = cacheManager.getDataSourceHealth(dataSourceKey);
if (cachedHealth != null) {
    return cachedHealth;
}
// 执行实际检查并缓存结果
cacheManager.cacheDataSourceHealth(dataSourceKey, isHealthy);
```

### 3. 配置更新

#### 依赖配置
在 `rega-common-db/pom.xml` 中添加了 common-cache 依赖：
```xml
<dependency>
    <groupId>com.rega.erp</groupId>
    <artifactId>rega-common-cache</artifactId>
</dependency>
```

#### Spring 配置
在 `TenantAutoConfiguration` 中添加了 `DataSourceCacheManager` 的 Bean 配置：
```java
@Bean
@ConditionalOnMissingBean
public DataSourceCacheManager dataSourceCacheManager(CacheService cacheService) {
    log.info("Register DataSourceCacheManager");
    return new DataSourceCacheManager(cacheService);
}
```

## 架构优势

### 1. 统一缓存管理
- ✅ **统一接口**: 使用 common-cache 模块的 `CacheService` 统一接口
- ✅ **一致性**: 所有缓存操作遵循相同的模式和配置
- ✅ **可维护性**: 缓存逻辑集中管理，易于维护和扩展

### 2. 高性能缓存策略
- ✅ **多级缓存**: 支持本地缓存和分布式缓存
- ✅ **智能过期**: 不同类型数据使用不同的过期策略
- ✅ **原子操作**: 支持原子性的增减操作

### 3. 租户隔离
- ✅ **租户缓存隔离**: 通过 `TenantCacheManager` 实现租户级别的缓存隔离
- ✅ **安全性**: 租户间数据完全隔离，避免数据泄露
- ✅ **可扩展性**: 支持大量租户的并发访问

### 4. 分布式锁支持
- ✅ **并发控制**: 使用分布式锁确保缓存操作的原子性
- ✅ **数据一致性**: 避免并发更新导致的数据不一致

## 缓存键设计

### 缓存键前缀
```java
private static final String DATASOURCE_PREFIX = "datasource";
private static final String TENANT_DATASOURCE_PREFIX = "tenant_datasource";
private static final String DATASOURCE_CONFIG_PREFIX = "datasource_config";
private static final String DATASOURCE_USAGE_PREFIX = "datasource_usage";
private static final String DATASOURCE_HEALTH_PREFIX = "datasource_health";
```

### 缓存键示例
```
datasource_config:id:1                    // 数据源配置（按ID）
datasource_config:key:primary             // 数据源配置（按键）
tenant_datasource:123                     // 租户数据源关联
datasource_usage:primary                  // 数据源使用统计
datasource_health:primary                 // 数据源健康状态
```

## 使用示例

### 基本缓存操作
```java
// 缓存数据源配置
cacheManager.cacheDataSourceConfig(datasourceId, dataSource);

// 获取数据源配置（带加载器）
SysDataSource dataSource = cacheManager.getDataSourceConfig(
    datasourceId, dataSourceService::getDataSourceById);

// 增加使用计数
cacheManager.incrementDataSourceUsage(dataSourceKey, 1);

// 缓存健康状态
cacheManager.cacheDataSourceHealth(dataSourceKey, true);
```

### 租户数据源管理
```java
// 获取租户数据源（自动缓存）
DataSource dataSource = tenantDataSourceManager.getTenantDataSource(tenantId);

// 分配数据源（自动更新缓存）
String dataSourceKey = tenantDataSourceManager.assignDataSourceToTenant(
    tenantId, isolationType, datasourceId);

// 移除关联（自动清理缓存）
boolean success = tenantDataSourceManager.removeTenantFromDataSource(
    tenantId, datasourceId);
```

## 性能优化

### 1. 缓存命中率优化
- **预加载**: 系统启动时预加载常用数据源配置
- **智能过期**: 根据数据更新频率设置合适的过期时间
- **批量操作**: 支持批量获取和设置缓存

### 2. 内存使用优化
- **合理过期**: 避免缓存数据长期占用内存
- **压缩存储**: 对大对象进行压缩存储
- **清理策略**: 定期清理无效缓存

### 3. 网络优化
- **本地缓存**: 优先使用本地缓存减少网络开销
- **批量传输**: 批量获取减少网络往返次数
- **压缩传输**: 对缓存数据进行压缩传输

## 监控和统计

### 缓存统计
```java
// 获取所有数据源使用统计
Map<String, Integer> statistics = cacheManager.getAllDataSourceUsage();

// 获取缓存统计信息
CacheStats stats = cacheService.getStats();
```

### 健康检查
```java
// 检查数据源健康状态（带缓存）
boolean isHealthy = tenantDataSourceManager.checkDataSourceHealth(dataSourceKey);
```

## 总结

通过这次重构，我们实现了：

1. ✅ **避免重复造轮子**: 完全基于 common-cache 模块的通用缓存方法
2. ✅ **统一缓存管理**: 所有数据源相关缓存统一管理
3. ✅ **高性能**: 多级缓存、智能过期、原子操作
4. ✅ **高可用**: 分布式锁、故障恢复、健康检查
5. ✅ **易维护**: 清晰的架构、统一的接口、完善的文档

这个重构为 RegaWebERP 项目提供了企业级的数据源缓存管理能力，支持大规模多租户应用的高并发访问需求。
