# 后端开发规范与技术栈

## 1. 项目结构

### 1.1 多模块架构

RegaWebERP采用基于Spring Boot的多模块架构，遵循高内聚低耦合原则，各模块职责清晰，依赖关系明确。

项目结构如下：

- **rega-parent**：父项目
  - **rega-common**：公共模块
    - **rega-common-core**：核心工具模块
    - **rega-common-redis**：Redis模块
    - **rega-common-security**：安全模块  
    - **rega-common-log**：日志模块
    - **rega-common-i18n**：国际化模块
    - **rega-common-db**：数据库模块
    - **rega-common-sdk**：业务SDK模块                                                                                                                        
  - **rega-system**：系统管理模块
  - **rega-tenant**：多租户模块
  - **rega-form**：动态表单模块
  - **rega-workflow**：工作流模块
  - **rega-report**：报表模块
  - **rega-api**：API模块
  - **rega-integration**：集成模块
  - **rega-application**：应用启动模块

### 1.2 模块内部结构

每个业务模块内部遵循相同的分层结构：

- **controller**：控制器层，处理HTTP请求
- **service**：服务层，业务逻辑
  - **impl**：服务实现
- **repository**：数据访问层，数据库操作
- **model**：数据模型
  - **entity**：数据库实体
  - **dto**：数据传输对象
  - **vo**：视图对象
- **config**：模块配置
- **util**：模块工具类

资源文件组织：

- **i18n**：国际化资源文件
  - **messages_zh_CN.properties**：中文资源
  - **messages_zh_TW.properties**：繁体中文资源
  - **messages_en_US.properties**：英文资源

### 1.3 模块职责

| 模块名称 | 职责描述 |
|---------|---------|
| rega-common-core | 提供核心工具类、异常处理、常量定义等基础功能 |
| rega-common-redis | 提供Redis缓存配置与操作封装 |
| rega-common-security | 提供安全认证、授权、JWT处理等功能 |
| rega-common-log | 提供日志记录、审计功能 |
| rega-common-i18n | 提供国际化资源管理与翻译服务 |
| rega-common-db | 提供数据库配置、连接池管理等功能 |
| rega-common-sdk | 基于Anyline封装的业务开发SDK，提供通用CRUD操作 |
| rega-system | 系统管理功能，包括用户、角色、权限、菜单等 |
| rega-tenant | 多租户管理功能，包括租户管理、数据隔离等 |
| rega-form | 动态表单引擎，提供表单设计、渲染、数据处理等功能 |
| rega-workflow | 工作流引擎，提供流程设计、执行、管理等功能 |
| rega-report | 报表功能，提供报表设计、生成、导出等功能 |
| rega-api | 对外API接口，提供系统集成能力 |
| rega-integration | 系统集成功能，对接第三方系统 |
| rega-application | 应用启动模块，整合各模块，提供运行环境 |

### 1.4 依赖关系

模块间依赖关系遵循以下原则：

- 应用模块依赖业务模块
- 业务模块依赖公共模块
- 公共模块间可能存在依赖，但避免循环依赖
- 工作流模块依赖表单模块
- SDK模块依赖数据库模块
- 所有模块都依赖核心模块

## 2. 技术栈

### 2.1 核心框架

| 技术 | 版本 | 说明 |
|------|------|------|
| Java | 17 | 编程语言 |
| Spring Boot | 3.1.x | 应用框架 |
| Sa-Token | 1.34.0 | 认证与权限框架 |
| Anyline | 8.6.0 | ORM框架 |
| PostgreSQL | 17.x+ | 数据库 |
| HikariCP | 5.0.1 | 数据库连接池 |
| JetCache | 2.7.x | 多级缓存框架 |
| Redisson | 3.20.x | Redis客户端 |
| CosID | 2.5.x | 分布式ID生成器 |
| Maven | 3.6.3+ | 项目管理 |

### 2.2 工具库

| 技术 | 说明 |
|------|------|
| Lombok | 简化Java代码 |
| Hutool | 工具集合 |
| MapStruct | 对象映射 |
| Knife4j | API文档 |
| JWT | JWT工具（Sa-Token整合） |
| Jackson | JSON处理 |
| JUnit5 | 单元测试 |
| Mockito | 测试模拟 |

### 2.3 开发工具

| 工具 | 说明 |
|------|------|
| IntelliJ IDEA | 推荐IDE |
| Maven | 依赖管理 |
| Git | 版本控制 |
| Docker | 容器化 |
| Jenkins | CI/CD |
| SonarQube | 代码质量 |

## 3. 编码规范

### 3.1 命名规范

#### 包命名

- 基础包名：`com.rega.erp`
- 模块包名：`com.rega.erp.{模块名}`
- 层级包名：`com.rega.erp.{模块名}.{层级}`

#### 类命名

- 控制器类：`*Controller`
- 服务接口：`*Service`
- 服务实现：`*ServiceImpl`
- 数据访问：`*Repository`
- 实体类：`*Entity` 或领域名称
- DTO：`*DTO`
- VO：`*VO`
- 工具类：`*Utils`

#### 方法命名

- 获取单个对象：`get*`
- 获取多个对象：`list*`
- 分页查询：`page*`
- 创建：`create*`
- 修改：`update*`
- 删除：`delete*`
- 条件查询：`query*`
- 统计：`count*`
- 检查：`check*`、`validate*`
- 处理：`handle*`、`process*`

### 3.2 接口规范

#### RESTful API设计

| 请求方法 | 功能 |
|---------|------|
| GET | 查询资源 |
| POST | 创建资源 |
| PUT | 修改资源（全量更新） |
| PATCH | 修改资源（部分更新） |
| DELETE | 删除资源 |

#### 接口路径

- 基础路径：`/api/{版本}/{模块}/{资源}`
- 例如：`/api/v1/system/users`

#### 统一响应格式

```json
{
  "code": 200,          // 状态码
  "message": "成功",     // 消息
  "data": {},           // 数据
  "timestamp": 1600000000000  // 时间戳
}
```

### 3.3 多租户规范

- 所有业务表必须包含`tenant_id`字段
- 所有业务查询必须包含租户条件
- 使用`@TenantRequired`或`@IgnoreTenant`注解标记租户要求
- 租户上下文通过`TenantContextHolder`管理

### 3.4 国际化规范

- 所有面向用户的文本必须使用国际化资源
- 错误信息必须国际化
- 国际化资源按模块组织
- 使用`i18n.getMessage()`获取国际化文本

### 3.5 异常处理

- 业务异常继承`BusinessException`
- 系统异常继承`SystemException`
- 使用全局异常处理器统一处理异常
- 异常信息必须国际化
- 不允许吞掉异常，必须记录日志

### 3.6 日志规范

- 使用SLF4J+Logback框架
- 关键业务操作必须记录日志
- 异常必须记录日志
- 敏感信息不允许打印日志
- 使用`@Log`注解自动记录操作日志

### 3.7 事务管理

- 服务层方法使用`@Transactional`注解
- 查询方法使用`@Transactional(readOnly = true)`
- 事务传播行为默认使用`REQUIRED`
- 必要时指定回滚异常
- 避免事务嵌套过深

### 3.8 安全规范

- 敏感数据加密存储
- 密码必须加密存储，使用BCrypt算法
- API接口必须进行权限校验
- 防止SQL注入，使用参数化查询
- 防止XSS攻击，输入输出过滤
- 使用HTTPS传输
- 实现CSRF防护

## 4. 开发流程

### 4.1 分支管理

- `master`：主分支，保持稳定，只合并经过测试的代码
- `develop`：开发分支，日常开发工作的集成分支
- `feature/*`：功能分支，开发新功能
- `bugfix/*`：修复分支，修复bug
- `release/*`：发布分支，准备发布

### 4.2 提交规范

提交信息格式：`类型(模块): 描述`

类型:
- `feat`: 新功能
- `fix`: 修复Bug
- `docs`: 文档更新
- `style`: 代码格式（不影响功能）
- `refactor`: 重构
- `perf`: 性能优化
- `test`: 测试相关
- `build`: 构建相关
- `ci`: CI配置相关
- `chore`: 其他修改

例如：`feat(system): 添加用户删除功能`

### 4.3 代码审查

- 所有代码必须经过代码审查才能合并
- 代码审查重点关注：
  - 功能正确性
  - 代码质量
  - 性能问题
  - 安全问题
  - 是否符合编码规范

### 4.4 测试要求

- 核心业务逻辑必须编写单元测试
- 单元测试覆盖率不低于70%
- 所有API接口必须测试
- 修复bug时必须添加对应的测试用例

## 5. 多租户实现

### 5.1 数据隔离策略

RegaWebERP支持两种租户隔离级别：

#### 5.1.1 字段隔离

- 共享数据库、共享表
- 通过`tenant_id`字段区分不同租户数据
- 优点：简单，资源共享程度高
- 缺点：隔离性较差，可能存在性能影响

#### 5.1.2 数据源隔离

- 独立数据库
- 每个租户使用独立的数据源
- 优点：隔离性最好，性能最优
- 缺点：资源消耗最大，管理复杂度最高

### 5.2 租户上下文

- 使用`TenantContextHolder`管理当前线程的租户信息
- 通过拦截器自动设置租户上下文
- 支持手动切换租户上下文
- 请求完成后自动清除租户上下文

### 5.3 自动过滤

- 使用Anyline的数据权限功能自动添加租户过滤条件
- 所有业务查询自动应用租户过滤
- 特殊场景可通过`@IgnoreTenant`注解忽略租户过滤

### 5.4 Anyline SDK封装

为了简化开发，基于Anyline封装SDK提供以下功能：

- **通用CRUD操作**：封装带租户ID的增删改查操作
- **租户工具**：提供租户上下文管理工具
- **查询构建器**：自动注入租户条件的查询构建

## 6. 动态表单引擎

### 6.1 表单模型

- 使用JSON Schema描述表单结构
- 表单元数据存储在专用表中
- 支持的字段类型：文本、数字、日期、选择、开关、文件等
- 支持字段校验规则定义
- 支持字段间联动规则定义

### 6.2 数据存储策略
  - 动态表存储：为每个表单模板创建对应的数据表

### 6.3 表单权限控制

- 字段级权限控制
- 根据用户角色显示/隐藏/只读字段
- 权限规则存储在表单元数据中
- 前端渲染时应用权限规则

### 6.4 表单版本管理

- 支持表单模板版本控制
- 历史数据关联对应版本的表单模板
- 支持表单模板升级和数据迁移

## 7. 工作流引擎

### 7.1 流程模型

- 基于BPMN 2.0标准
- 支持的节点类型：
  - 开始节点
  - 结束节点
  - 用户任务节点
  - 自动任务节点
  - 网关节点（条件、并行、排他）
  - 子流程节点

### 7.2 流程定义

- 流程定义存储为JSON格式
- 包含节点、连线、条件、表单关联等信息
- 支持流程定义导入导出
- 支持流程定义版本控制

### 7.3 流程执行

- 流程实例与业务数据关联
- 支持流程暂停和恢复
- 支持流程取消和终止
- 支持流程回退和跳转
- 任务分配支持指定用户、角色、部门

### 7.4 表单集成

- 支持动态表单和工作流集成
- 支持一个流程关联多个表单
- 支持节点间表单数据传递
- 支持条件表达式引用表单数据

## 8. 性能优化

### 8.1 缓存策略

- 使用JetCache实现多级缓存：
  - 本地缓存（Caffeine）
  - 分布式缓存（Redisson）
- 缓存常用数据：
  - 字典数据
  - 配置信息
  - 菜单和权限数据
  - 租户信息
- 缓存自动失效和更新机制

### 8.2 数据库优化

- 合理设计索引
- 使用HikariCP连接池
- 大数据量分页查询优化
- 避免N+1查询问题
- 合理使用批量操作

### 8.3 JVM优化

- 合理设置JVM内存参数
- 定期分析GC日志
- 避免内存泄漏
- 使用JVM监控工具

### 8.4 API响应优化

- 使用响应压缩
- 实现API结果缓存
- 大数据集合实现流式处理
- 支持部分字段查询

## 9. 安全措施

### 9.1 认证与授权

- 基于Sa-Token+JWT的认证
- 基于RBAC的权限控制
- 支持OAuth2认证（可选）
- 多因素认证支持（可选）

### 9.2 数据安全

- 敏感数据加密存储
- 传输数据HTTPS加密
- 重要操作需二次验证
- 实现防重放攻击机制

### 9.3 操作审计

- 记录关键操作日志
- 记录数据变更历史
- 敏感操作实时告警
- 操作日志不可篡改

## 10. 部署架构

### 10.1 开发环境

- 单机部署
- PostgreSQL单节点
- 本地Redis
- 本地开发工具

### 10.2 测试环境

- 单机部署
- PostgreSQL单节点
- Redis单节点
- 测试工具集成

### 10.3 生产环境

- 多节点集群
- PostgreSQL单节点（后续可扩展）
- Redis集群
- 使用Kubernetes编排（可选）
- 配置负载均衡
- 实现高可用部署 