# RegaWebERP Anyline ORM 集成计划

## 📋 集成概述

基于 Anyline ORM 的强大功能，我们将其作为 RegaWebERP 项目的主要 ORM 工具和 DDL 元数据处理工具，替代传统的 MyBatis Plus，以获得更好的跨数据库兼容性和动态表单支持。

## 🎯 集成目标

### 1. 核心目标
- ✅ **跨数据库兼容** - 支持 PostgreSQL、MySQL、Oracle 等多种数据库
- ✅ **自动方言转换** - 无需手动处理 `find_in_set` 等数据库差异
- ✅ **动态表单支持** - 运行时创建表结构，支持动态字段
- ✅ **元数据管理** - 强大的 DDL 操作和数据库结构对比功能
- ✅ **数据迁移** - 跨数据库的数据迁移和同步

### 2. 技术优势
- **466+ 数据库支持** - 真正的跨数据库 ORM
- **运行时元数据操作** - 完美支持动态业务场景
- **内置缓存机制** - 元数据缓存提升性能
- **Spring Boot 集成** - 无缝集成现有技术栈

## 🚀 集成阶段

### 阶段一：基础集成 (1-2天)

#### 1.1 依赖配置
```xml
<!-- 添加 Anyline 依赖 -->
<dependency>
    <groupId>org.anyline</groupId>
    <artifactId>anyline-spring-boot-starter</artifactId>
    <version>8.7.3</version>
</dependency>
```

#### 1.2 配置文件
```yaml
anyline:
  datasource:
    default: primary
    list:
      primary:
        url: ${spring.datasource.url}
        username: ${spring.datasource.username}
        password: ${spring.datasource.password}
        driver-class-name: ${spring.datasource.driver-class-name}
  metadata:
    cache:
      enable: true
      expire: 3600
  sql:
    log:
      enable: true
      level: DEBUG
  primary:
    generator: snowflake
```

#### 1.3 基础服务封装
- 创建 `AnylineBaseService` 基础服务类
- 封装常用的 CRUD 操作
- 集成租户隔离和权限控制

### 阶段二：数据访问层改造 (2-3天)

#### 2.1 实体类适配
- 保持现有的实体类结构
- 添加 Anyline 注解支持
- 实现实体与 DataRow 的转换

#### 2.2 Service 层改造
- 用户管理服务改造
- 角色权限服务改造
- 租户管理服务改造
- 保持现有接口不变，内部实现替换

#### 2.3 查询条件构建
- 使用 Anyline 的 ConfigStore 替代 MyBatis 的条件构建
- 实现动态查询条件
- 支持复杂的多表关联查询

### 阶段三：动态表单支持 (3-4天)

#### 3.1 表单元数据管理
- 表单模板设计和存储
- 动态表结构创建
- 字段类型映射和验证

#### 3.2 动态数据操作
- 动态表的 CRUD 操作
- 表单数据的验证和处理
- 支持复杂的表单关联

#### 3.3 表单引擎
- 表单渲染引擎
- 数据绑定和验证
- 工作流集成

### 阶段四：高级功能 (2-3天)

#### 4.1 数据库迁移
- 跨数据库数据迁移工具
- 数据库结构对比
- DDL 脚本生成

#### 4.2 多数据源支持
- 多租户数据源隔离
- 动态数据源切换
- 数据源监控和管理

#### 4.3 性能优化
- 查询缓存优化
- 批量操作优化
- 连接池配置优化

## 🔧 技术实现

### 1. 核心服务架构

```java
@Component
public class AnylineBaseService {
    
    @Autowired
    @Qualifier("anyline.service")
    protected AnylineService service;
    
    // 基础 CRUD 操作
    public <T> T save(String tableName, T entity) { }
    public <T> T findById(String tableName, Long id) { }
    public <T> List<T> findAll(String tableName, ConfigStore configs) { }
    public <T> PageResult<T> findPage(String tableName, ConfigStore configs, PageNavi navi) { }
    public boolean deleteById(String tableName, Long id) { }
    
    // 动态表单操作
    public void createDynamicTable(FormTemplate template) { }
    public void saveDynamicForm(String tableName, Map<String, Object> formData) { }
    public Map<String, Object> getDynamicForm(String tableName, Long id) { }
}
```

### 2. 实体转换器

```java
@Component
public class EntityConverter {
    
    public <T> T convertFromDataRow(DataRow row, Class<T> clazz) {
        // DataRow 转实体
    }
    
    public DataRow convertToDataRow(Object entity) {
        // 实体转 DataRow
    }
    
    public <T> List<T> convertFromDataSet(DataSet dataSet, Class<T> clazz) {
        // DataSet 转实体列表
    }
}
```

### 3. 动态表单管理器

```java
@Service
public class DynamicFormManager extends AnylineBaseService {
    
    public void createFormTable(FormTemplate template) {
        Table table = buildTableFromTemplate(template);
        service.ddl().create(table);
    }
    
    public void updateFormTable(FormTemplate template) {
        Table currentTable = service.metadata().table(template.getTableName());
        Table newTable = buildTableFromTemplate(template);
        
        // 对比差异并生成 DDL
        List<String> ddlScripts = compareAndGenerateDDL(currentTable, newTable);
        ddlScripts.forEach(service::execute);
    }
    
    private Table buildTableFromTemplate(FormTemplate template) {
        // 根据表单模板构建表结构
    }
}
```

## 📊 迁移策略

### 1. 渐进式迁移
- **第一步**: 新功能使用 Anyline
- **第二步**: 逐步迁移现有功能
- **第三步**: 完全替换 MyBatis Plus

### 2. 兼容性保证
- 保持现有 API 接口不变
- 内部实现逐步替换
- 提供迁移工具和文档

### 3. 数据安全
- 迁移前完整备份
- 分阶段验证数据一致性
- 提供回滚方案

## 🧪 测试计划

### 1. 单元测试
- 基础 CRUD 操作测试
- 动态表单功能测试
- 跨数据库兼容性测试

### 2. 集成测试
- 多租户数据隔离测试
- 权限控制测试
- 性能压力测试

### 3. 兼容性测试
- PostgreSQL 兼容性测试
- MySQL 兼容性测试
- 数据迁移测试

## 📝 开发规范

### 1. 代码规范
- 统一使用 Anyline 的 DataRow/DataSet
- 封装通用的转换方法
- 保持现有的业务逻辑不变

### 2. 命名规范
- 表名：小写 + 下划线
- 字段名：小写 + 下划线
- 服务类：XxxAnylineService

### 3. 配置规范
- 统一的数据源配置
- 标准化的缓存配置
- 规范的日志配置

## 🎯 预期收益

### 1. 技术收益
- **跨数据库兼容** - 支持多种数据库，降低迁移成本
- **动态表单** - 强大的动态业务支持能力
- **元数据管理** - 完善的 DDL 操作和结构对比
- **性能提升** - 内置缓存和优化机制

### 2. 业务收益
- **快速开发** - 动态表单大幅提升开发效率
- **灵活配置** - 运行时调整业务结构
- **数据迁移** - 便捷的跨数据库迁移能力
- **维护成本** - 统一的数据访问层，降低维护成本

### 3. 长期收益
- **技术前瞻** - 面向未来的动态元数据架构
- **生态兼容** - 与多种技术栈的良好集成
- **扩展能力** - 强大的扩展和定制能力

## 📅 时间计划

| 阶段 | 时间 | 主要任务 | 交付物 |
|------|------|----------|--------|
| 阶段一 | 1-2天 | 基础集成 | 基础服务类、配置文件 |
| 阶段二 | 2-3天 | 数据访问层改造 | 改造后的 Service 层 |
| 阶段三 | 3-4天 | 动态表单支持 | 表单管理器、表单引擎 |
| 阶段四 | 2-3天 | 高级功能 | 迁移工具、多数据源支持 |
| **总计** | **8-12天** | **完整集成** | **生产就绪的 Anyline 集成** |

---

**通过 Anyline ORM 的集成，RegaWebERP 将获得强大的跨数据库能力和动态表单支持，为企业级应用提供更加灵活和强大的数据处理能力！** 🚀

**最后更新**: 2025-06-17  
**版本**: v1.0  
**维护者**: RegaWebERP 开发团队
