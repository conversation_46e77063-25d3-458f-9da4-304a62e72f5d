# 多数据源存储逻辑重构文档

## 概述

本文档记录了 RegaWebERP 项目中多数据源存储逻辑的重构工作，将原本基于内存缓存的数据源管理改为基于数据库存储的方案。

## 重构目标

1. **持久化存储**: 将数据源配置从内存缓存迁移到数据库存储
2. **配置管理**: 提供完整的数据源配置管理功能
3. **监控支持**: 增加数据源监控和健康检查功能
4. **租户隔离**: 完善多租户数据源隔离机制
5. **运维友好**: 提供数据源操作日志和统计信息

## 数据库设计

### 1. 核心表结构

#### sys_datasource - 数据源配置表
- **用途**: 存储数据源的基本配置信息
- **关键字段**: 
  - `datasource_key`: 数据源唯一标识
  - `datasource_type`: 数据源类型（postgresql, mysql等）
  - `url`, `username`, `password`: 连接信息
  - 连接池配置参数
  - 健康检查配置

#### sys_tenant_datasource - 租户数据源关联表
- **用途**: 管理租户与数据源的关联关系
- **关键字段**:
  - `tenant_id`, `datasource_id`: 关联关系
  - `isolation_type`: 隔离类型（1-字段隔离，2-数据源隔离）
  - `is_primary`: 是否主数据源
  - `priority`: 优先级配置
  - `read_weight`, `write_weight`: 读写权重

#### sys_datasource_monitor - 数据源监控表
- **用途**: 记录数据源的监控信息
- **关键字段**:
  - `datasource_id`: 关联数据源
  - `status`: 健康状态
  - `response_time`: 响应时间
  - 连接池状态信息
  - 系统资源使用情况

#### sys_datasource_log - 数据源操作日志表
- **用途**: 记录数据源相关的操作日志
- **关键字段**:
  - `operation_type`: 操作类型
  - `operator_id`: 操作人
  - `status`: 操作结果
  - `error_message`: 错误信息

#### sys_datasource_template - 数据源配置模板表
- **用途**: 提供常用数据库的配置模板
- **预置模板**: PostgreSQL, MySQL, Oracle, SQL Server

### 2. 数据库脚本

创建了以下迁移脚本：
- `V1.0.2__create_datasource_tables.sql`: 创建表结构
- `V1.0.3__init_datasource_data.sql`: 初始化基础数据

## 代码重构

### 1. 新增实体类

#### SysDataSource
- 数据源配置实体，对应 `sys_datasource` 表
- 包含完整的数据源配置信息
- 提供配置验证和辅助方法

#### SysTenantDataSource
- 租户数据源关联实体，对应 `sys_tenant_datasource` 表
- 支持字段隔离和数据源隔离两种模式
- 包含优先级和权重配置

#### SysDataSourceMonitor
- 数据源监控实体，对应 `sys_datasource_monitor` 表
- 提供健康状态检查和告警判断方法

### 2. 服务层重构

#### DataSourceService 接口
定义了完整的数据源管理功能：
- **配置管理**: 创建、更新、删除、查询数据源配置
- **租户关联**: 管理租户与数据源的关联关系
- **实例管理**: 创建、销毁、重载数据源实例
- **监控管理**: 健康检查、监控记录、统计信息

#### DataSourceServiceImpl 实现类
- 基于 Anyline ORM 实现数据库操作
- 使用 HikariCP 创建数据源实例
- 提供连接测试和健康检查功能
- 包含完整的异常处理和日志记录

### 3. DataSourceRegistry 重构

#### 重构前
- 基于内存 `ConcurrentHashMap` 存储
- 数据源信息在重启后丢失
- 缺乏持久化和配置管理

#### 重构后
- 基于数据库存储，内存作为缓存
- 支持配置持久化和动态加载
- 集成 `DataSourceService` 进行统一管理
- 提供缓存刷新和实例重载功能

## 功能特性

### 1. 多租户支持
- **字段隔离**: 多租户共享数据源，通过 `tenant_id` 字段区分
- **数据源隔离**: 租户使用专用数据源，提供更高隔离性
- **灵活配置**: 支持租户级别的隔离策略选择

### 2. 监控和健康检查
- **实时监控**: 记录连接池状态、响应时间等指标
- **健康检查**: 定期检查数据源可用性
- **告警机制**: 基于阈值的自动告警判断
- **历史记录**: 保留监控历史数据用于分析

### 3. 配置模板
- **预置模板**: 支持主流数据库的配置模板
- **快速配置**: 基于模板快速创建数据源
- **标准化**: 统一的配置参数和最佳实践

### 4. 操作审计
- **操作日志**: 记录所有数据源相关操作
- **用户追踪**: 记录操作人和操作时间
- **错误记录**: 详细的错误信息和堆栈跟踪

## 配置示例

### 数据源配置
```yaml
# 主数据源配置
datasource:
  primary:
    key: primary
    name: 主数据源
    type: postgresql
    url: *****************************************
    username: rega_user
    password: encrypted_password
    pool:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
```

### 租户隔离配置
```yaml
# 租户配置
tenant:
  isolation:
    default-type: field  # field 或 datasource
    field-name: tenant_id
  datasource:
    assignment-strategy: auto  # auto, manual, dedicated
```

## 迁移指南

### 1. 数据库迁移
1. 执行 `V1.0.2__create_datasource_tables.sql` 创建表结构
2. 执行 `V1.0.3__init_datasource_data.sql` 初始化数据
3. 配置现有数据源到新表结构

### 2. 代码迁移
1. 更新依赖注入，使用新的 `DataSourceService`
2. 修改数据源获取方式，使用 `DataSourceRegistry`
3. 更新配置文件，添加数据源相关配置

### 3. 测试验证
1. 验证数据源连接和切换功能
2. 测试多租户隔离效果
3. 检查监控和日志功能

## 后续计划

### 1. 完善实现
- 完成 `DataSourceServiceImpl` 中的 TODO 方法
- 实现基于 Anyline 的数据库操作
- 添加密码加密和解密功能

### 2. 功能增强
- 实现数据源连接池监控
- 添加数据源性能统计
- 支持数据源故障转移

### 3. 运维工具
- 开发数据源管理界面
- 实现监控告警通知
- 提供数据源诊断工具

## 注意事项

1. **密码安全**: 数据库中的密码需要加密存储
2. **连接池管理**: 注意连接池的生命周期管理
3. **缓存一致性**: 确保内存缓存与数据库的一致性
4. **性能考虑**: 合理设置缓存策略和刷新频率
5. **异常处理**: 完善的异常处理和降级机制

## 总结

通过本次重构，RegaWebERP 的多数据源管理从简单的内存缓存升级为完整的数据库存储方案，提供了：

- ✅ **持久化配置**: 数据源配置持久化存储
- ✅ **多租户支持**: 灵活的租户隔离策略
- ✅ **监控体系**: 完整的监控和健康检查
- ✅ **操作审计**: 详细的操作日志记录
- ✅ **配置模板**: 标准化的配置模板
- ✅ **扩展性**: 良好的扩展性和可维护性

这为系统的稳定运行和运维管理提供了坚实的基础。
