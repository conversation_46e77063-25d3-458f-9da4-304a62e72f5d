# Java开发规则

以下规则基于RegaWebERP项目的开发规范、功能清单和开发计划生成，旨在确保代码质量和一致性。

## 1. 命名规范

### 1.1 包命名规范

```json
{
  "name": "包命名规范",
  "patterns": [
    {
      "pattern": "package\\s+(?!com\\.rega\\.erp)",
      "message": "包名必须以com.rega.erp开头"
    },
    {
      "pattern": "package\\s+com\\.rega\\.erp\\.[a-z]+\\.[a-z]+",
      "exclude": ["controller", "service", "repository", "model", "config", "util", "entity", "dto", "vo"],
      "message": "包名应遵循com.rega.erp.{模块名}.{层级}格式，层级应为controller/service/repository/model/config/util等"
    }
  ]
}
```

### 1.2 类命名规范

```json
{
  "name": "类命名规范",
  "patterns": [
    {
      "pattern": "class\\s+[a-z]\\w*\\s+",
      "message": "类名应使用PascalCase命名法（首字母大写）"
    },
    {
      "pattern": "class\\s+\\w*Controller\\s+",
      "exclude": ["@RestController", "@Controller"],
      "message": "Controller类应添加@RestController或@Controller注解"
    },
    {
      "pattern": "class\\s+\\w*ServiceImpl\\s+",
      "exclude": ["@Service"],
      "message": "ServiceImpl类应添加@Service注解"
    },
    {
      "pattern": "class\\s+\\w*Repository\\s+",
      "exclude": ["@Repository"],
      "message": "Repository类应添加@Repository注解"
    }
  ]
}
```

### 1.3 方法命名规范

```json
{
  "name": "方法命名规范",
  "patterns": [
    {
      "pattern": "public\\s+\\w+\\s+[A-Z]\\w*\\(",
      "message": "方法名应以小写字母开头"
    },
    {
      "pattern": "public\\s+\\w+\\s+get\\w*\\(",
      "exclude": ["@Transactional", "readOnly"],
      "message": "查询方法应添加@Transactional(readOnly = true)注解"
    },
    {
      "pattern": "public\\s+\\w+\\s+(create|update|delete|save)\\w*\\(",
      "exclude": ["@Transactional"],
      "message": "修改操作应添加@Transactional注解"
    }
  ]
}
```

## 2. 多租户规范

```json
{
  "name": "多租户规范",
  "patterns": [
    {
      "pattern": "@\\w*Mapping\\s*\\([^)]*\\)",
      "exclude": ["@TenantRequired", "@IgnoreTenant"],
      "message": "控制器方法应标注租户注解以明确租户隔离要求"
    },
    {
      "pattern": "repository\\.\\w+\\([^)]*\\)",
      "exclude": ["tenantId", "getTenantContext"],
      "message": "数据访问操作应包含租户上下文"
    },
    {
      "pattern": "CREATE\\s+TABLE|create\\s+table",
      "exclude": ["tenant_id"],
      "message": "业务表必须包含tenant_id字段"
    }
  ]
}
```

## 3. 国际化规范

```json
{
  "name": "国际化规范",
  "patterns": [
    {
      "pattern": "\"[^\"]*[\u4e00-\u9fa5]+[^\"]*\"",
      "exclude": ["i18n.getMessage", "I18nUtil"],
      "message": "硬编码中文应使用国际化资源key替代"
    },
    {
      "pattern": "throw\\s+new\\s+\\w+Exception\\([^)]*\"",
      "exclude": ["i18n.getMessage", "I18nUtil"],
      "message": "异常信息应使用国际化资源"
    }
  ]
}
```

## 4. 异常处理规范

```json
{
  "name": "异常处理规范",
  "patterns": [
    {
      "pattern": "try\\s*\\{[\\s\\S]*\\}\\s*catch\\s*\\([\\s\\S]*\\)\\s*\\{\\s*\\}",
      "message": "不允许空的catch块，必须记录日志或重新抛出异常"
    },
    {
      "pattern": "throw\\s+new\\s+\\w+Exception\\(",
      "exclude": ["BusinessException", "ServiceException", "SystemException"],
      "message": "应使用业务异常类而非通用异常"
    },
    {
      "pattern": "e\\.printStackTrace\\(\\)",
      "message": "不应使用printStackTrace()，应使用日志框架记录异常"
    }
  ]
}
```

## 5. 日志规范

```json
{
  "name": "日志规范",
  "patterns": [
    {
      "pattern": "System\\.out\\.print|System\\.err\\.print",
      "message": "不应使用System.out或System.err，应使用日志框架"
    },
    {
      "pattern": "log\\.(debug|info|warn|error)\\([^)]+\\)",
      "exclude": ["\\{\\}", "\\$\\{"],
      "message": "日志应使用占位符格式，如log.info(\"用户 {} 登录成功\", username)"
    },
    {
      "pattern": "log\\.(debug|info|warn|error)\\([^)]*password[^)]*\\)",
      "message": "日志中不应包含敏感信息如密码"
    }
  ]
}
```

## 6. 安全规范

```json
{
  "name": "安全规范",
  "patterns": [
    {
      "pattern": "password\\s*=\\s*\"[^\"]*\"",
      "message": "不应硬编码密码"
    },
    {
      "pattern": "@\\w*Mapping\\s*\\([^)]*\\)",
      "exclude": ["@PreAuthorize", "@SaCheckPermission"],
      "message": "API接口应进行权限校验"
    },
    {
      "pattern": "String\\s+sql\\s*=\\s*\".*\\$\\{.*\\}.*\"",
      "message": "避免SQL注入风险，应使用参数化查询"
    }
  ]
}
```

## 7. 数据库操作规范

```json
{
  "name": "数据库操作规范",
  "patterns": [
    {
      "pattern": "Sql\\(\"[^\"]*\"\\)",
      "message": "避免使用原生SQL，优先使用Anyline提供的API"
    },
    {
      "pattern": "for\\s*\\([^)]*\\)\\s*\\{[\\s\\S]*repository\\.[^;]*\\}",
      "message": "避免循环中进行数据库操作，应使用批量操作"
    },
    {
      "pattern": "new\\s+\\w+\\([^)]*\\)\\s*;[\\s\\S]*repository\\.save\\(",
      "exclude": ["@Builder"],
      "message": "创建实体应使用Builder模式或工厂方法"
    }
  ]
}
```

## 8. API设计规范

```json
{
  "name": "API设计规范",
  "patterns": [
    {
      "pattern": "@\\w*Mapping\\s*\\([^)]*\\)[^{]*\\{[\\s\\S]*return\\s+(?!ResponseEntity|ApiResult|R\\.|Result\\.|AjaxResult\\.).+;",
      "message": "接口返回值应使用统一响应包装类"
    },
    {
      "pattern": "@RequestMapping\\s*\\(\\s*\"[^\"]*\"",
      "exclude": ["/api/v\\d+/"],
      "message": "API路径应遵循/api/{版本}/{模块}/{资源}格式"
    },
    {
      "pattern": "@(GetMapping|PostMapping|PutMapping|DeleteMapping|PatchMapping)",
      "exclude": ["@Operation", "@ApiOperation"],
      "message": "API方法应添加Swagger/OpenAPI注解"
    }
  ]
}
```

## 9. 事务管理规范

```json
{
  "name": "事务管理规范",
  "patterns": [
    {
      "pattern": "@Transactional\\s*\\([^)]*propagation\\s*=\\s*Propagation\\.REQUIRES_NEW[^)]*\\)",
      "message": "谨慎使用REQUIRES_NEW传播行为，可能导致事务嵌套问题"
    },
    {
      "pattern": "@Transactional[^(]",
      "message": "应明确指定@Transactional的属性，如readOnly、rollbackFor等"
    },
    {
      "pattern": "public\\s+\\w+\\s+\\w+\\([^)]*\\)\\s*\\{[\\s\\S]*@Transactional[\\s\\S]*\\}",
      "message": "@Transactional注解应放在方法上而非方法内部"
    }
  ]
}
```

## 10. 动态表单规范

```json
{
  "name": "动态表单规范",
  "patterns": [
    {
      "pattern": "jsonb_\\w+\\(|->|#>>",
      "exclude": ["FormDataRepository", "FormQueryService"],
      "message": "PostgreSQL JSONB操作应封装在表单数据仓库或查询服务中"
    },
    {
      "pattern": "new\\s+JSONObject\\(|new\\s+JSONArray\\(",
      "exclude": ["ObjectMapper", "JsonNode"],
      "message": "应使用Jackson处理JSON数据而非直接操作JSONObject"
    }
  ]
}
```

## 11. 工作流规范

```json
{
  "name": "工作流规范",
  "patterns": [
    {
      "pattern": "workflow\\.\\w+\\([^)]*\\)",
      "exclude": ["@Transactional"],
      "message": "工作流操作应在事务中执行"
    },
    {
      "pattern": "new\\s+\\w*Task\\([^)]*\\)",
      "message": "任务创建应通过工厂方法或构建器模式，避免直接实例化"
    }
  ]
}
```

## 12. 缓存使用规范

```json
{
  "name": "缓存使用规范",
  "patterns": [
    {
      "pattern": "@Cacheable\\s*\\([^)]*\\)",
      "exclude": ["key", "keyGenerator"],
      "message": "缓存注解应指定key或keyGenerator"
    },
    {
      "pattern": "@CacheEvict\\s*\\([^)]*\\)",
      "exclude": ["allEntries"],
      "message": "缓存清除应考虑是否需要清除所有条目"
    }
  ]
}
```

## 13. 代码质量规范

```json
{
  "name": "代码质量规范",
  "patterns": [
    {
      "pattern": "public\\s+class\\s+\\w+\\s*\\{[\\s\\S]*\\}",
      "length": 500,
      "message": "类过长，建议拆分或重构"
    },
    {
      "pattern": "public\\s+\\w+\\s+\\w+\\([^)]*\\)\\s*\\{[\\s\\S]*\\}",
      "length": 50,
      "message": "方法过长，建议拆分或重构"
    },
    {
      "pattern": "if\\s*\\([^)]+\\)\\s*\\{[\\s\\S]*\\}\\s*else\\s+if\\s*\\([^)]+\\)[\\s\\S]*else\\s+if\\s*\\([^)]+\\)[\\s\\S]*else\\s+if",
      "message": "过多的if-else分支，建议使用策略模式或状态模式重构"
    }
  ]
}
```

## 14. 注释规范

```json
{
  "name": "注释规范",
  "patterns": [
    {
      "pattern": "public\\s+(class|interface|enum)\\s+\\w+",
      "exclude": ["/**", "//"],
      "message": "公共类、接口、枚举应有注释说明"
    },
    {
      "pattern": "@(RestController|Controller|Service|Repository|Component)",
      "exclude": ["/**", "//"],
      "message": "Spring组件应有注释说明"
    },
    {
      "pattern": "public\\s+\\w+\\s+\\w+\\([^)]*\\)",
      "exclude": ["/**", "//"],
      "message": "公共方法应有注释说明"
    }
  ]
}
```

## 15. 多租户SDK使用规范

```json
{
  "name": "多租户SDK使用规范",
  "patterns": [
    {
      "pattern": "new\\s+QueryBuilder\\([^)]*\\)",
      "exclude": ["tenant"],
      "message": "使用QueryBuilder时应考虑租户上下文"
    },
    {
      "pattern": "TenantUtils\\.doIgnoreTenant\\(",
      "message": "谨慎使用doIgnoreTenant方法，可能导致跨租户数据泄露"
    }
  ]
}
```
```

这个Java规则文件包含了15个主要规则类别，每个类别都基于您项目文档中的关键规范和要求。这些规则将帮助您的团队在开发过程中遵循一致的编码标准，确保代码质量，并避免常见的错误和安全问题。

您可以根据实际需求调整或扩展这些规则，特别是随着项目的发展，可能需要添加更多特定于业务领域的规则。