# Anyline ORM 集成指南

## 概述

本文档描述了 RegaWebERP 项目中 Anyline ORM 的集成方案。Anyline 是一个跨数据库通用 ORM 工具，支持 466+ 数据库，具有内置方言转换引擎和元数据映射库。

## 模块结构

### rega-common-anyline

新增的 Anyline 集成模块，提供以下功能：

- **AnylineBaseService**: Anyline 基础服务类，封装常用的 CRUD 操作
- **EntityConverter**: 实体转换器（已简化，使用 Anyline 内置转换）
- **AnylineConfiguration**: Anyline 配置类
- **AnylineAutoConfiguration**: 自动配置类
- **UserAnylineService**: 用户服务示例

## 版本配置

### 重要原则

1. **版本一致性**: 项目中所有 Anyline 相关依赖必须保持版本号一致
2. **开发环境**: 使用 SNAPSHOT 版本如 `8.7.2-SNAPSHOT`
3. **生产环境**: 必须使用发行版本如 `8.7.2-yyyyMMdd`
4. **Java 版本**: Spring Boot 2.x 使用 `anyline 8.7.2`，Spring Boot 3.x 需要 `anyline 8.7.2-jdk17`

### Maven 配置

```xml
<properties>
    <anyline.version>8.7.2-SNAPSHOT</anyline.version>
</properties>

<repositories>
    <!-- Anyline SNAPSHOT 仓库 -->
    <repository>
        <id>ossrh</id>
        <name>OSS Sonatype Snapshots</name>
        <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
        <snapshots>
            <enabled>true</enabled>
            <updatePolicy>always</updatePolicy>
        </snapshots>
    </repository>
</repositories>

<dependencies>
    <!-- Anyline 核心依赖 -->
    <dependency>
        <groupId>org.anyline</groupId>
        <artifactId>anyline-data-jdbc</artifactId>
        <version>${anyline.version}</version>
    </dependency>
    
    <!-- Anyline PostgreSQL 适配器 -->
    <dependency>
        <groupId>org.anyline</groupId>
        <artifactId>anyline-data-jdbc-postgresql</artifactId>
        <version>${anyline.version}</version>
    </dependency>
</dependencies>
```

## 实体转换功能

Anyline 内置强大的实体转换功能：

1. **DataRow.entity(User.class)**: 将 DataRow 转换为实体
2. **DataSet.entitys(User.class)**: 将 DataSet 转换为实体列表  
3. **service.select(User.class)**: 直接返回实体对象
4. **service.selects(User.class)**: 直接返回实体列表
5. **属性映射**: 默认按小驼峰 vs 下划线格式自动转换
6. **注解支持**: 支持 MyBatis、Hibernate、JPA 注解

## 配置文件

### application.yml

```yaml
# Anyline ORM 配置
anyline:
  # 数据源配置
  datasource:
    default: primary
    list:
      primary:
        url: ${spring.datasource.url}
        username: ${spring.datasource.username}
        password: ${spring.datasource.password}
        driver-class-name: ${spring.datasource.driver-class-name}
        
  # 元数据缓存配置
  metadata:
    cache:
      enable: true
      expire: 3600  # 缓存1小时
      
  # SQL 日志配置
  sql:
    log:
      enable: true
      level: DEBUG
      
  # 主键生成策略
  primary:
    generator: snowflake
```

## 使用示例

### 基础 CRUD 操作

```java
@Service
public class UserService extends AnylineBaseService {
    
    // 保存实体
    public User saveUser(User user) {
        return save(user);
    }
    
    // 根据ID查询
    public User getUserById(Long id) {
        return findById(User.class, id);
    }
    
    // 删除实体
    public boolean deleteUser(Long id) {
        return deleteById(User.class, id);
    }
}
```

### 实体类定义

```java
public class User extends TenantBaseEntity {
    private String username;
    private String password;
    private String realName;
    private String email;
    private String phone;
    
    // getters and setters...
}
```

## 多租户支持

Anyline 集成自动支持多租户隔离：

1. **字段级隔离**: 自动添加 `TENANT_ID` 条件
2. **逻辑删除**: 自动添加 `DELETED = 0` 条件
3. **审计字段**: 自动填充创建时间、更新时间等

## 数据库兼容性

Anyline 支持 466+ 数据库，包括：

- **关系型数据库**: PostgreSQL, MySQL, Oracle, SQL Server 等
- **内置方言转换**: 自动处理 SQL 语法差异，如 `find_in_set` 函数
- **动态元数据**: 运行时基于元数据操作
- **DDL 支持**: 支持动态表结构操作

## 注意事项

1. **依赖版本**: 确保所有 Anyline 依赖版本一致
2. **SNAPSHOT 更新**: 开发环境设置 `updatePolicy=always` 获取最新快照
3. **生产部署**: 生产环境必须使用稳定的发行版本
4. **Java 17**: 使用 `jakarta.annotation.PostConstruct` 替代 `javax.annotation.PostConstruct`

## 后续开发

当前实现为基础框架，后续需要完善：

1. **实际 API 调用**: 根据 Anyline 官方文档完善具体实现
2. **错误处理**: 完善异常处理和错误码
3. **性能优化**: 添加缓存和批量操作支持
4. **测试用例**: 编写单元测试和集成测试

## 参考资源

- [Anyline 官方文档](http://www.anyline.org/doc)
- [版本说明](http://www.anyline.org/ss/e3_1054)
- [实体转换](http://www.anyline.org/aa/87_3779)
- [Maven 中央库](https://mvnrepository.com/artifact/org.anyline/anyline-core)
