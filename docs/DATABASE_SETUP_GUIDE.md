# RegaWebERP 数据库设置指南

## 📋 概述

本指南将帮助您设置 RegaWebERP 项目所需的 PostgreSQL 数据库环境。

## 🛠️ 环境要求

- PostgreSQL 17.x+ （推荐）
- 或 PostgreSQL 13.x+ （最低要求）

## 📦 PostgreSQL 安装

### macOS 安装

#### 方法一：使用 Homebrew（推荐）
```bash
# 安装 PostgreSQL
brew install postgresql@17

# 启动 PostgreSQL 服务
brew services start postgresql@17

# 设置开机自启动
brew services enable postgresql@17
```

#### 方法二：使用 Postgres.app
1. 下载 [Postgres.app](https://postgresapp.com/)
2. 安装并启动应用
3. 点击 "Initialize" 创建新的数据库集群

### Windows 安装

1. 下载 [PostgreSQL Windows 安装包](https://www.postgresql.org/download/windows/)
2. 运行安装程序，按照向导完成安装
3. 记住设置的超级用户密码

### Linux 安装

#### Ubuntu/Debian
```bash
# 更新包列表
sudo apt update

# 安装 PostgreSQL
sudo apt install postgresql postgresql-contrib

# 启动服务
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### CentOS/RHEL
```bash
# 安装 PostgreSQL
sudo yum install postgresql-server postgresql-contrib

# 初始化数据库
sudo postgresql-setup initdb

# 启动服务
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## 🔧 数据库配置

### 1. 连接到 PostgreSQL

```bash
# 使用 postgres 用户连接
sudo -u postgres psql

# 或者直接连接（如果已配置用户）
psql -U postgres -h localhost
```

### 2. 创建数据库和用户

```sql
-- 创建 RegaWebERP 数据库
CREATE DATABASE rega_erp;

-- 创建专用用户（可选，推荐）
CREATE USER rega_user WITH PASSWORD 'rega_pass';

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE rega_erp TO rega_user;

-- 切换到 rega_erp 数据库
\c rega_erp

-- 授予 schema 权限
GRANT ALL ON SCHEMA public TO rega_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO rega_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO rega_user;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO rega_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO rega_user;
```

### 3. 验证数据库创建

```sql
-- 查看数据库列表
\l

-- 查看用户列表
\du

-- 退出 psql
\q
```

## ⚙️ 应用配置

### 开发环境配置

编辑 `rega-api/src/main/resources/application-dev.yml`：

```yaml
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ************************************************************************************************************************************
    username: postgres  # 或 rega_user
    password: postgres  # 您设置的密码
```

### 生产环境配置

编辑 `rega-api/src/main/resources/application-prod.yml`：

```yaml
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:rega_erp}?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf8&stringtype=unspecified
    username: ${DB_USERNAME:rega_user}
    password: ${DB_PASSWORD:rega_pass}
```

## 🗄️ 数据库初始化

### 自动初始化（推荐）

应用启动时会自动检查数据库表是否存在，如果不存在会提示执行初始化脚本。

### 手动初始化

```bash
# 连接到数据库
psql -U postgres -d rega_erp

# 执行初始化脚本
\i /path/to/rega-api/src/main/resources/db/migration/V1.0.0__init_database.sql
\i /path/to/rega-api/src/main/resources/db/migration/V1.0.1__init_data.sql
```

### 使用 psql 命令行

```bash
# 执行建表脚本
psql -U postgres -d rega_erp -f rega-api/src/main/resources/db/migration/V1.0.0__init_database.sql

# 执行数据初始化脚本
psql -U postgres -d rega_erp -f rega-api/src/main/resources/db/migration/V1.0.1__init_data.sql
```

## 🧪 连接测试

### 1. 使用 psql 测试

```bash
# 测试连接
psql -U postgres -h localhost -d rega_erp -c "SELECT version();"
```

### 2. 启动应用测试

```bash
# 编译应用
cd rega-api
mvn clean package -DskipTests

# 启动应用
java -jar target/rega-api-1.0.0-SNAPSHOT.jar
```

如果数据库连接成功，您会看到类似以下的日志：

```
=== 数据库连接信息 ===
数据库产品名称: PostgreSQL
数据库产品版本: 17.x
驱动名称: PostgreSQL JDBC Driver
✅ 数据库连接测试成功！
```

## 🔍 故障排除

### 常见问题

#### 1. 连接被拒绝
```
Connection refused
```

**解决方案：**
- 确认 PostgreSQL 服务已启动
- 检查端口 5432 是否被占用
- 确认防火墙设置

#### 2. 认证失败
```
password authentication failed
```

**解决方案：**
- 检查用户名和密码是否正确
- 确认用户是否存在
- 检查 `pg_hba.conf` 配置

#### 3. 数据库不存在
```
database "rega_erp" does not exist
```

**解决方案：**
- 创建数据库：`CREATE DATABASE rega_erp;`
- 检查数据库名称拼写

#### 4. 权限不足
```
permission denied
```

**解决方案：**
- 授予用户权限：`GRANT ALL PRIVILEGES ON DATABASE rega_erp TO rega_user;`
- 检查 schema 权限

### 配置文件位置

- **macOS (Homebrew)**: `/opt/homebrew/var/postgresql@17/`
- **Linux**: `/etc/postgresql/17/main/`
- **Windows**: `C:\Program Files\PostgreSQL\17\data\`

### 重要配置文件

- `postgresql.conf` - 主配置文件
- `pg_hba.conf` - 客户端认证配置

## 📊 性能优化

### 基础优化配置

编辑 `postgresql.conf`：

```conf
# 内存配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB

# 连接配置
max_connections = 100

# 日志配置
log_statement = 'all'
log_duration = on
```

### 连接池配置

应用中的 HikariCP 配置已优化：

```yaml
hikari:
  minimum-idle: 5
  maximum-pool-size: 20
  connection-timeout: 30000
  idle-timeout: 600000
  max-lifetime: 1800000
```

## 🔐 安全建议

1. **不要使用默认密码**
2. **创建专用数据库用户**
3. **限制网络访问**
4. **定期备份数据**
5. **启用 SSL 连接**

## 📝 备份和恢复

### 备份数据库

```bash
# 备份整个数据库
pg_dump -U postgres -h localhost rega_erp > rega_erp_backup.sql

# 备份数据（仅数据，不包括结构）
pg_dump -U postgres -h localhost --data-only rega_erp > rega_erp_data.sql
```

### 恢复数据库

```bash
# 恢复数据库
psql -U postgres -h localhost rega_erp < rega_erp_backup.sql
```

---

**完成数据库设置后，您就可以正常启动 RegaWebERP 应用了！** 🎉

如有问题，请参考故障排除部分或联系开发团队。
