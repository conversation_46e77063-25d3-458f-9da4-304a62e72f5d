# PostgreSQL SQL 语法修复文档

## 问题描述

在执行 `V1.0.2__create_datasource_tables.sql` 脚本时遇到语法错误：
```
syntax error at or near "COMMENT"
```

## 问题原因

PostgreSQL 不支持在 `CREATE TABLE` 语句中直接使用 `COMMENT` 关键字来添加字段备注，这是 MySQL 的语法。

### 错误语法（MySQL 风格）
```sql
CREATE TABLE test_table (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '姓名',  -- ❌ PostgreSQL 不支持
    status INTEGER DEFAULT 1 COMMENT '状态'     -- ❌ PostgreSQL 不支持
);
```

### 正确语法（PostgreSQL 风格）
```sql
-- 1. 先创建表结构
CREATE TABLE test_table (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    status INTEGER DEFAULT 1
);

-- 2. 再添加字段备注
COMMENT ON COLUMN test_table.id IS '主键ID';
COMMENT ON COLUMN test_table.name IS '姓名';
COMMENT ON COLUMN test_table.status IS '状态';
```

## 修复方案

### 1. 表结构修复

将所有表的创建语句从 MySQL 风格改为 PostgreSQL 风格：

#### 修复前
```sql
CREATE TABLE sys_datasource (
    id BIGINT PRIMARY KEY,
    datasource_name VARCHAR(100) NOT NULL COMMENT '数据源名称',
    datasource_key VARCHAR(50) NOT NULL UNIQUE COMMENT '数据源标识键',
    -- ... 其他字段
);
```

#### 修复后
```sql
-- 创建表结构
CREATE TABLE sys_datasource (
    id BIGINT PRIMARY KEY,
    datasource_name VARCHAR(100) NOT NULL,
    datasource_key VARCHAR(50) NOT NULL UNIQUE,
    -- ... 其他字段
);

-- 添加字段备注
COMMENT ON COLUMN sys_datasource.id IS '主键ID';
COMMENT ON COLUMN sys_datasource.datasource_name IS '数据源名称';
COMMENT ON COLUMN sys_datasource.datasource_key IS '数据源标识键';
-- ... 其他字段备注
```

### 2. 完整修复的表

已修复以下 5 个表的语法：

1. **sys_datasource** - 数据源配置表（32个字段）
2. **sys_tenant_datasource** - 租户数据源关联表（13个字段）
3. **sys_datasource_monitor** - 数据源监控表（13个字段）
4. **sys_datasource_log** - 数据源操作日志表（15个字段）
5. **sys_datasource_template** - 数据源配置模板表（18个字段）

### 3. 字段备注完整性

确保每个表的所有字段都有详细的备注信息：

#### sys_datasource 表字段备注示例
```sql
COMMENT ON COLUMN sys_datasource.id IS '主键ID';
COMMENT ON COLUMN sys_datasource.datasource_name IS '数据源名称';
COMMENT ON COLUMN sys_datasource.datasource_key IS '数据源标识键';
COMMENT ON COLUMN sys_datasource.datasource_type IS '数据源类型：postgresql, mysql, oracle等';
COMMENT ON COLUMN sys_datasource.driver_class_name IS '驱动类名';
COMMENT ON COLUMN sys_datasource.url IS '数据库连接URL';
COMMENT ON COLUMN sys_datasource.username IS '用户名';
COMMENT ON COLUMN sys_datasource.password IS '密码（加密存储）';
-- ... 共32个字段的备注
```

## 修复结果

### ✅ 语法兼容性
- 所有 SQL 语句符合 PostgreSQL 语法规范
- 移除了不兼容的 `COMMENT` 内联语法
- 使用标准的 `COMMENT ON COLUMN` 语句

### ✅ 备注完整性
- **sys_datasource**: 32个字段，32条备注 ✅
- **sys_tenant_datasource**: 13个字段，13条备注 ✅
- **sys_datasource_monitor**: 13个字段，13条备注 ✅
- **sys_datasource_log**: 15个字段，15条备注 ✅
- **sys_datasource_template**: 18个字段，18条备注 ✅
- **sys_tenant**: 新增1个字段，1条备注 ✅

### ✅ 表备注
```sql
COMMENT ON TABLE sys_datasource IS '数据源配置表';
COMMENT ON TABLE sys_tenant_datasource IS '租户数据源关联表';
COMMENT ON TABLE sys_datasource_monitor IS '数据源监控表';
COMMENT ON TABLE sys_datasource_log IS '数据源操作日志表';
COMMENT ON TABLE sys_datasource_template IS '数据源配置模板表';
```

## 验证方法

### 1. 语法检查
```sql
-- 检查表是否创建成功
SELECT table_name, table_comment 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE 'sys_datasource%';
```

### 2. 字段备注检查
```sql
-- 检查字段备注是否添加成功
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default,
    col_description(pgc.oid, a.attnum) as column_comment
FROM information_schema.columns a
JOIN pg_class pgc ON pgc.relname = a.table_name
WHERE table_schema = 'public' 
AND table_name = 'sys_datasource'
ORDER BY ordinal_position;
```

### 3. 索引检查
```sql
-- 检查索引是否创建成功
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename LIKE 'sys_datasource%'
ORDER BY tablename, indexname;
```

## 最佳实践

### 1. 数据库兼容性
- 在编写 SQL 脚本时，明确目标数据库类型
- 避免使用数据库特定的语法扩展
- 使用标准 SQL 语法以提高兼容性

### 2. 备注规范
- 所有表都应该有表备注
- 所有字段都应该有字段备注
- 备注信息应该清晰、准确、有意义
- 对于枚举值字段，在备注中说明所有可能的值

### 3. 脚本结构
```sql
-- 1. 创建表结构
CREATE TABLE table_name (...);

-- 2. 添加表备注
COMMENT ON TABLE table_name IS '表描述';

-- 3. 添加字段备注
COMMENT ON COLUMN table_name.field1 IS '字段1描述';
COMMENT ON COLUMN table_name.field2 IS '字段2描述';

-- 4. 创建索引
CREATE INDEX idx_name ON table_name(field);

-- 5. 添加外键约束
ALTER TABLE table_name ADD CONSTRAINT fk_name FOREIGN KEY ...;
```

## 总结

通过本次修复，`V1.0.2__create_datasource_tables.sql` 脚本现在完全符合 PostgreSQL 语法规范，包含：

- ✅ 5个核心表的完整结构
- ✅ 91个字段的详细备注
- ✅ 完整的索引和约束定义
- ✅ 标准的 PostgreSQL 语法

现在可以在 PostgreSQL 数据库中成功执行该脚本，创建完整的多数据源管理表结构。
