# Anyline ORM 架构决策记录

## 背景

在 RegaWebERP 项目中，我们需要集成 Anyline ORM 框架来支持动态表单、多租户数据隔离和跨数据库兼容性。

## 问题

最初创建了两个与 Anyline 相关的模块：
1. `rega-common-db` - 已存在，专注多租户和数据库基础设施
2. `rega-common-anyline` - 新创建，尝试封装 Anyline 服务层

这导致了功能重叠和架构混乱。

## 决策

**删除 `rega-common-anyline` 模块，统一使用 `rega-common-db` 模块进行 Anyline 集成。**

## 理由

### 1. 避免功能重叠
- `rega-common-db` 已经集成了 Anyline ORM 依赖
- 两个模块都在处理数据库访问和 ORM 功能
- 重复的抽象层增加了复杂性

### 2. 明确模块职责
- `rega-common-db` 定位清晰：数据库访问和多租户管理的基础设施层
- 包含完整的多租户隔离策略（字段隔离 vs 数据源隔离）
- 已有完善的动态数据源管理和租户配置

### 3. 现有功能完备
`rega-common-db` 已经提供：
- ✅ Anyline ORM 集成 (`anyline-environment-spring-data-jdbc`, `anyline-data-jdbc-postgresql`)
- ✅ 多租户数据隔离配置
- ✅ 动态数据源注册和路由
- ✅ 租户管理和状态监控
- ✅ 数据源工厂和连接池管理
- ✅ 完整的测试用例

## 实施

### 已完成的清理工作

1. **删除重复模块**
   ```bash
   rm -rf rega-common/rega-common-anyline
   ```

2. **更新模块依赖**
   - 从 `rega-common/pom.xml` 移除 `rega-common-anyline` 模块
   - 从父 `pom.xml` 移除 `rega-common-anyline` 依赖管理
   - 更新 `rega-api/pom.xml` 使用 `rega-common-db` 替代 `rega-common-anyline`

3. **清理文档**
   - 删除重复的 `docs/anyline-integration.md`
   - 保留 `rega-common-db` 中的完整文档

### 当前架构

```
rega-common-db/
├── src/main/java/com/rega/erp/common/db/
│   └── tenant/                    # 多租户管理
│       ├── config/               # 租户配置
│       ├── core/                 # 核心服务
│       ├── datasource/           # 数据源管理
│       ├── domain/               # 领域模型
│       └── isolation/            # 隔离策略
├── ANYLINE_INTEGRATION.md        # Anyline 集成指南
├── DEPENDENCIES.md               # 依赖说明
└── README.md                     # 模块说明
```

## 后续开发指导

### 使用 Anyline ORM

1. **依赖 rega-common-db 模块**
   ```xml
   <dependency>
       <groupId>com.rega.erp</groupId>
       <artifactId>rega-common-db</artifactId>
   </dependency>
   ```

2. **注入 AnylineService**
   ```java
   @Service
   public class UserService {
       @Autowired
       private AnylineService anylineService;
       
       // 使用 Anyline 进行数据操作
   }
   ```

3. **利用多租户功能**
   ```java
   // 自动支持租户隔离
   TenantContextHolder.setTenantId("tenant_001");
   DataSet result = anylineService.querys("user_table", conditions);
   ```

### 配置参考

详细的配置说明请参考：
- `rega-common-db/ANYLINE_INTEGRATION.md` - Anyline 集成指南
- `rega-common-db/README.md` - 模块功能说明
- `rega-common-db/DEPENDENCIES.md` - 依赖配置

## 优势

1. **简化架构** - 单一模块负责数据库访问
2. **避免重复** - 消除功能重叠和代码冗余
3. **明确职责** - 清晰的模块边界和职责分工
4. **易于维护** - 集中管理 Anyline 相关配置和代码
5. **完整功能** - 保留所有必要的多租户和 ORM 功能

## 影响

- ✅ **正面影响**: 简化了项目结构，避免了架构混乱
- ✅ **兼容性**: 不影响现有功能，`rega-common-db` 已经提供完整支持
- ✅ **可维护性**: 集中管理提高了代码的可维护性

## 结论

通过删除重复的 `rega-common-anyline` 模块，我们实现了：
- 更清晰的架构设计
- 避免了功能重叠
- 保持了完整的 Anyline ORM 支持
- 维护了强大的多租户功能

这个决策符合单一职责原则，提高了项目的整体质量和可维护性。
