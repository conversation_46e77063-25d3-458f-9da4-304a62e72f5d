# RegaWebERP 数据库兼容性指南

## 📋 概述

RegaWebERP 项目主要使用 PostgreSQL 数据库，但在开发过程中需要注意数据库方言的差异，确保代码的可移植性和兼容性。

## 🎯 支持的数据库

| 数据库 | 版本要求 | 支持状态 | 说明 |
|--------|----------|----------|------|
| PostgreSQL | 13.x+ | ✅ 主要支持 | 项目主要数据库 |
| MySQL | 8.0+ | 🔄 兼容支持 | 通过 SqlUtils 兼容 |
| Oracle | 19c+ | 🔄 兼容支持 | 通过 SqlUtils 兼容 |
| SQL Server | 2019+ | 🔄 兼容支持 | 通过 SqlUtils 兼容 |

## ⚠️ 常见方言差异

### 1. FIND_IN_SET 函数

**问题**: MySQL 的 `find_in_set` 函数在其他数据库中不存在

#### MySQL 语法
```sql
SELECT * FROM users WHERE find_in_set('admin', roles);
```

#### PostgreSQL 兼容语法
```sql
SELECT * FROM users WHERE 
    roles LIKE '%,admin,%' OR 
    roles LIKE 'admin,%' OR 
    roles LIKE '%,admin' OR 
    roles = 'admin';
```

#### 解决方案
使用 `SqlUtils.findInSet()` 方法：

```java
// Java 代码
String condition = SqlUtils.findInSet("admin", "roles");

// 自动生成兼容的 SQL
// PostgreSQL: (roles LIKE '%,admin,%' OR roles LIKE 'admin,%' OR roles LIKE '%,admin' OR roles = 'admin')
// MySQL: find_in_set('admin', roles)
```

### 2. 分页查询

#### MySQL 语法
```sql
SELECT * FROM users LIMIT 10 OFFSET 20;
```

#### Oracle 语法
```sql
SELECT * FROM users OFFSET 20 ROWS FETCH NEXT 10 ROWS ONLY;
```

#### 解决方案
```java
String paginationSql = SqlUtils.paginate(20, 10);
```

### 3. 字符串连接

#### MySQL 语法
```sql
SELECT CONCAT(first_name, ' ', last_name) FROM users;
```

#### PostgreSQL 语法
```sql
SELECT first_name || ' ' || last_name FROM users;
```

#### 解决方案
```java
String concatSql = SqlUtils.concat("first_name", "' '", "last_name");
```

### 4. 日期格式化

#### MySQL 语法
```sql
SELECT DATE_FORMAT(create_time, '%Y-%m-%d') FROM users;
```

#### PostgreSQL 语法
```sql
SELECT TO_CHAR(create_time, 'YYYY-MM-DD') FROM users;
```

#### 解决方案
```java
String dateFormatSql = SqlUtils.dateFormat("create_time", "%Y-%m-%d");
```

## 🛠️ SqlUtils 工具类使用

### 1. 配置数据库类型

```java
// 在应用启动时配置
@Configuration
public class DatabaseConfig {
    
    @PostConstruct
    public void configureDatabaseType() {
        // 根据实际使用的数据库设置
        SqlUtils.setCurrentDatabaseType(SqlUtils.DatabaseType.POSTGRESQL);
    }
}
```

### 2. 常用方法示例

```java
// FIND_IN_SET 兼容
String condition1 = SqlUtils.findInSet("value", "column_name");
String condition2 = SqlUtils.findInSet(123L, "id_list");

// 分页查询
String pagination = SqlUtils.paginate(offset, limit);

// 字符串连接
String concat = SqlUtils.concat("field1", "field2", "'separator'");

// 日期格式化
String dateFormat = SqlUtils.dateFormat("date_column", "%Y-%m-%d %H:%i:%s");

// LIKE 查询
String likeCondition = SqlUtils.like("name", "张三", SqlUtils.LikeMatchType.ANYWHERE);

// SQL 转义
String escapedValue = SqlUtils.escapeSql("user'input");
```

## 📝 开发规范

### 1. 禁止使用的 MySQL 特有语法

❌ **不要使用**：
```sql
-- MySQL 特有函数
find_in_set(value, set)
group_concat(column)
date_format(date, format)
concat(str1, str2, ...)

-- MySQL 特有语法
LIMIT n
INSERT IGNORE INTO
ON DUPLICATE KEY UPDATE
```

✅ **推荐使用**：
```java
// 使用 SqlUtils 工具类
SqlUtils.findInSet(value, "set_column")
SqlUtils.concat("str1", "str2")
SqlUtils.dateFormat("date_column", "format")
SqlUtils.paginate(offset, limit)
```

### 2. 数据类型映射

| 概念 | PostgreSQL | MySQL | Oracle | SQL Server |
|------|------------|-------|--------|------------|
| 自增主键 | BIGSERIAL | BIGINT AUTO_INCREMENT | NUMBER + SEQUENCE | BIGINT IDENTITY |
| 长文本 | TEXT | TEXT/LONGTEXT | CLOB | NVARCHAR(MAX) |
| 布尔值 | BOOLEAN | TINYINT(1) | NUMBER(1) | BIT |
| 时间戳 | TIMESTAMP | TIMESTAMP | TIMESTAMP | DATETIME2 |
| JSON | JSON/JSONB | JSON | CLOB | NVARCHAR(MAX) |

### 3. 索引命名规范

```sql
-- 主键索引
pk_{table_name}

-- 唯一索引  
uk_{table_name}_{column_name}

-- 普通索引
idx_{table_name}_{column_name}

-- 外键索引
fk_{table_name}_{ref_table_name}
```

## 🔧 MyBatis 配置

### 1. 数据库方言配置

```yaml
# application.yml
spring:
  datasource:
    # PostgreSQL 配置
    driver-class-name: org.postgresql.Driver
    url: *****************************************
    
mybatis-plus:
  configuration:
    # 数据库方言
    database-id: postgresql
  global-config:
    db-config:
      # ID 生成策略
      id-type: assign_id
      # 逻辑删除
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

### 2. 动态 SQL 示例

```xml
<!-- MyBatis Mapper XML -->
<select id="findUsers" resultType="User">
    SELECT * FROM sys_user 
    WHERE 1=1
    <if test="deptIds != null and deptIds.size() > 0">
        AND dept_id IN
        <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
            #{deptId}
        </foreach>
    </if>
    <if test="ancestorDeptId != null">
        <!-- 使用数据库兼容的方式 -->
        AND dept_id IN (
            SELECT dept_id FROM sys_dept 
            WHERE dept_id = #{ancestorDeptId} 
            OR ${@com.rega.erp.common.core.util.SqlUtils@findInSet(ancestorDeptId, 'ancestors')}
        )
    </if>
</select>
```

## 🧪 测试建议

### 1. 多数据库测试

```java
@TestPropertySource(properties = {
    "spring.datasource.driver-class-name=org.postgresql.Driver",
    "spring.datasource.url=****************************************"
})
class PostgreSQLTest {
    // PostgreSQL 特定测试
}

@TestPropertySource(properties = {
    "spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver", 
    "spring.datasource.url=***********************************"
})
class MySQLTest {
    // MySQL 特定测试
}
```

### 2. SQL 兼容性测试

```java
@Test
void testSqlCompatibility() {
    // 测试 FIND_IN_SET 兼容性
    String sql = SqlUtils.findInSet("admin", "roles");
    assertThat(sql).isNotEmpty();
    
    // 测试分页兼容性
    String pagination = SqlUtils.paginate(10, 20);
    assertThat(pagination).contains("LIMIT").contains("OFFSET");
}
```

## 📚 最佳实践

### 1. 代码层面

- ✅ 使用 `SqlUtils` 工具类处理方言差异
- ✅ 使用 MyBatis 动态 SQL 而非拼接字符串
- ✅ 使用标准 SQL 语法，避免数据库特有功能
- ✅ 参数化查询，防止 SQL 注入

### 2. 设计层面

- ✅ 使用统一的数据类型映射
- ✅ 遵循数据库设计规范
- ✅ 合理设计索引策略
- ✅ 考虑数据迁移的兼容性

### 3. 部署层面

- ✅ 环境配置参数化
- ✅ 数据库连接池配置优化
- ✅ 监控 SQL 性能
- ✅ 定期备份和恢复测试

## 🚨 注意事项

1. **性能差异**: 不同数据库的性能特性不同，需要针对性优化
2. **事务隔离**: 各数据库的默认隔离级别可能不同
3. **字符集**: 确保字符集配置一致，避免乱码问题
4. **大小写敏感**: PostgreSQL 对大小写敏感，MySQL 默认不敏感
5. **NULL 处理**: 各数据库对 NULL 值的处理可能有差异

---

**遵循本指南，确保 RegaWebERP 在不同数据库环境下的兼容性！** 🎯

**最后更新**: 2025-06-17  
**版本**: v1.0  
**维护者**: RegaWebERP 开发团队
