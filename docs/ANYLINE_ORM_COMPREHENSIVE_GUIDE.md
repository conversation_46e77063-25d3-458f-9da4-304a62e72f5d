# Anyline ORM 全面功能指南

## 📋 概述

Anyline ORM 是一个跨数据库的通用标准 ORM 工具，支持 **466+ 种数据库**，内置方言转换引擎与元数据映射库，实现异构数据库的统一操作。

### 🎯 核心设计理念

1. **面向动态，面向元数据，基于运行时**
   - 摆脱静态属性束缚，实现高度的灵活性与可扩展性
   - 运行时确定数据结构，支持动态变化
   - 基于元数据驱动的操作模式

2. **源于极简的用户直觉**
   - 通过极致的设计，实现调用流程的简单自然
   - 让操作体验符合用户直觉甚至猜测，达到所想即所得的效果

3. **多元生态支持**
   - 不局限于 Spring，支持纯 Java 环境、Vertx、Quarkus 等
   - 借鉴 Spring 生态，但不依赖于 Spring

## 🗄️ 数据库支持

### 支持的数据库类型
- **关系型数据库**: MySQL, PostgreSQL, Oracle, SQL Server, DB2, SQLite 等
- **时序数据库**: TDengine, InfluxDB, TimescaleDB, ClickHouse 等  
- **图数据库**: Neo4j, ArangoDB, TigerGraph, Nebula 等
- **文档数据库**: MongoDB, CouchDB, DocumentDB 等
- **键值数据库**: Redis, Cassandra, DynamoDB 等
- **搜索引擎**: Elasticsearch, Solr, OpenSearch 等
- **国产数据库**: 达梦(DM), 人大金仓(KingBase), 神通(Oscar), 南大通用(GBase) 等

### 数据库方言自动转换
- 内置 466+ 数据库的方言转换引擎
- 自动处理 SQL 语法差异（如 `find_in_set`, 分页语法等）
- 统一的数据类型映射和转换
- 跨数据库的元数据标准化

## 🏗️ 核心架构

### 1. AnylineService - 核心服务接口

```java
// 注入方式
@Autowired
@Qualifier("anyline.service")
protected AnylineService service;

// 继承方式
public class UserService extends AnylineService {
    // 业务逻辑
}

// 静态调用方式
ServiceProxy.querys("CRM_USER");

// 多数据源
AnylineService crmService = ServiceProxy.service("crm");
```

### 2. 数据结构体系

#### DataRow & DataSet
```java
// 单行数据
DataRow row = service.query("CRM_USER", "ID:1");

// 多行数据  
DataSet set = service.querys("CRM_USER");

// 操作示例
row.put("NAME", "张三");
set.add(row);
```

#### Entity & EntitySet
```java
// 实体操作
User user = service.select(User.class, "ID:1");
EntitySet<User> users = service.selects(User.class);

// 泛型支持
AnylineService<User> userService = new AnylineService<>();
User user = userService.get("ID:1");
```

## 🔧 核心功能模块

### 1. DML/DQL 数据操作

#### 查询操作
```java
// 基础查询
DataRow user = service.query("CRM_USER", "ID:1");
DataSet users = service.querys("CRM_USER", "STATUS:1");

// 实体查询
User user = service.select(User.class, "ID:1");
EntitySet<User> users = service.selects(User.class);

// 高性能查询（返回 Map）
List<Map<String, Object>> maps = service.maps("CRM_USER");

// 缓存查询
DataSet cached = service.caches("cache_key", "CRM_USER", "STATUS:1");
```

#### 插入操作
```java
// 插入 DataRow
DataRow row = new DataRow();
row.put("NAME", "张三");
service.insert("CRM_USER", row);

// 插入实体
User user = new User();
user.setName("张三");
service.insert(user);

// 批量插入
service.insert("CRM_USER", dataSet);

// Upsert（存在则更新，不存在则插入）
service.save("CRM_USER", row);
```

#### 更新操作
```java
// 条件更新
service.update("CRM_USER", "NAME:李四", "ID:1");

// 实体更新
service.update(user);

// 批量更新
service.update("CRM_USER", dataSet);
```

#### 删除操作
```java
// 条件删除
service.delete("CRM_USER", "ID:1");

// 实体删除
service.delete(user);

// 批量删除
service.delete("CRM_USER", "STATUS:0");
```

### 2. Metadata 元数据操作

```java
// 查询所有表
LinkedHashMap<String, Table> tables = service.metadata().tables();

// 查询指定表信息
Table table = service.metadata().table("CRM_USER");

// 查询表的所有列
LinkedHashMap<String, Column> columns = service.metadata().columns("CRM_USER");

// 查询主键
PrimaryKey pk = service.metadata().primary("CRM_USER");

// 查询索引
LinkedHashMap<String, Index> indexes = service.metadata().indexes("CRM_USER");

// 查询外键
LinkedHashMap<String, ForeignKey> fks = service.metadata().foreigns("CRM_USER");

// 查询子表（分区表）
LinkedHashMap<String, Table> ptables = service.metadata().ptables("CRM_USER");
```

### 3. DDL 数据定义操作

#### 表操作
```java
// 创建表
Table table = new Table("CRM_USER");
table.addColumn("ID", "BIGINT").setPrimary(true);
table.addColumn("NAME", "VARCHAR(50)").setNullable(false);
service.ddl().create(table);

// 修改表
service.ddl().alter(table);

// 删除表
service.ddl().drop(table);

// 重命名表
service.ddl().rename(table, "NEW_CRM_USER");
```

#### 列操作
```java
// 添加列
Column column = new Column("EMAIL", "VARCHAR(100)");
service.ddl().add(table, column);

// 修改列
column.setType("VARCHAR(200)");
service.ddl().alter(table, column);

// 删除列
service.ddl().drop(table, column);

// 重命名列
service.ddl().rename(table, column, "USER_EMAIL");
```

#### 索引操作
```java
// 创建索引
Index index = new Index("IDX_USER_NAME");
index.addColumn("NAME");
service.ddl().create(table, index);

// 删除索引
service.ddl().drop(table, index);
```

### 4. 查询条件构建

#### ConfigStore 条件构建器
```java
ConfigStore configs = new ConfigStore();
configs.and("STATUS", 1);
configs.and("NAME", "LIKE", "%张%");
configs.and("AGE", "BETWEEN", 18, 65);
configs.or("DEPT_ID", "IN", Arrays.asList(1,2,3));

DataSet result = service.querys("CRM_USER", configs);
```

#### 动态条件
```java
// 根据参数动态构建条件
public DataSet searchUsers(String name, Integer status, List<Integer> deptIds) {
    ConfigStore configs = new ConfigStore();
    
    if(StringUtils.isNotEmpty(name)) {
        configs.and("NAME", "LIKE", "%" + name + "%");
    }
    
    if(status != null) {
        configs.and("STATUS", status);
    }
    
    if(CollectionUtils.isNotEmpty(deptIds)) {
        configs.and("DEPT_ID", "IN", deptIds);
    }
    
    return service.querys("CRM_USER", configs);
}
```

### 5. 分页支持

```java
// 基础分页
PageNavi navi = new PageNavi(1, 20); // 第1页，每页20条
DataSet result = service.querys("CRM_USER", configs, navi);

// 获取分页信息
long total = navi.getTotalRow();
int totalPage = navi.getTotalPage();
boolean hasNext = navi.hasNext();
```

### 6. 多数据源支持

#### 配置多数据源
```yaml
spring:
  datasource:
    primary:
      url: **************************************
      username: root
      password: password
    secondary:
      url: *********************************************  
      username: postgres
      password: password
```

#### 使用多数据源
```java
// 切换数据源
AnylineService primaryService = ServiceProxy.service("primary");
AnylineService secondaryService = ServiceProxy.service("secondary");

// 跨数据源操作
DataSet mysqlData = primaryService.querys("USER");
secondaryService.insert("USER_BACKUP", mysqlData);
```

### 7. 事务控制

```java
@Transactional
public void transferData() {
    try {
        service.insert("LOG", logData);
        service.update("ACCOUNT", accountData);
        service.delete("TEMP", tempData);
    } catch (Exception e) {
        // 自动回滚
        throw e;
    }
}
```

### 8. 缓存集成

```java
// 缓存查询
DataSet cached = service.caches("user_list_key", "CRM_USER", "STATUS:1");

// 缓存配置
@CacheConfig(cacheNames = "userCache")
public class UserService {
    
    @Cacheable(key = "#id")
    public User getUserById(Long id) {
        return service.select(User.class, "ID:" + id);
    }
}
```

## 🚀 高级特性

### 1. 动态表单支持
- 运行时创建表结构
- 动态字段验证
- 表单数据的 CRUD 操作
- 支持复杂的表单关联

### 2. 数据库结构对比
```java
// 对比两个数据库的结构差异
DatabaseDiff diff = service.metadata().compare("db1", "db2");
List<String> ddlScripts = diff.getDDLScripts();
```

### 3. 数据迁移
```java
// 跨数据库数据迁移
AnylineService sourceService = ServiceProxy.service("mysql");
AnylineService targetService = ServiceProxy.service("postgresql");

DataSet sourceData = sourceService.querys("USER");
targetService.insert("USER", sourceData);
```

### 4. 存储过程支持
```java
// 调用存储过程
DataSet result = service.procedure("sp_get_user_report", param1, param2);
```

### 5. 自定义 SQL
```java
// 执行自定义 SQL
String sql = "SELECT u.*, d.name as dept_name FROM user u LEFT JOIN dept d ON u.dept_id = d.id";
DataSet result = service.querys(sql);

// 参数化查询
String sql = "SELECT * FROM user WHERE name LIKE ? AND status = ?";
DataSet result = service.querys(sql, "%张%", 1);
```

## 📊 性能优化

### 1. 元数据缓存
- 自动缓存表结构信息
- 可配置缓存时间和范围
- 支持手动刷新缓存

### 2. 连接池优化
- 支持多种连接池（HikariCP、Druid 等）
- 自动连接池配置优化
- 连接泄漏检测

### 3. 批量操作
- 批量插入、更新、删除
- 自动批次大小控制
- 事务批处理优化

## 🔌 集成支持

### 1. Spring Boot 集成
```java
@SpringBootApplication
@EnableAnyline
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 2. MyBatis 兼容
- 可与 MyBatis 共存
- 支持 MyBatis 的 Mapper 接口
- 统一的事务管理

### 3. JPA 兼容
- 支持 JPA 注解
- 实体类自动映射
- 与 Spring Data JPA 集成

## 🛠️ 开发工具

### 1. 代码生成
- 根据数据库表生成实体类
- 生成基础的 CRUD 代码
- 支持自定义模板

### 2. SQL 调试
- SQL 执行日志
- 性能监控
- 慢查询分析

### 3. 元数据管理
- 可视化表结构管理
- DDL 脚本生成
- 数据库文档生成

## 📝 最佳实践

### 1. 项目结构建议
```
src/main/java/
├── entity/          # 实体类
├── service/         # 业务服务
├── controller/      # 控制器
└── config/          # 配置类

src/main/resources/
├── sql/            # SQL 脚本
├── ddl/            # DDL 脚本
└── application.yml # 配置文件
```

### 2. 命名规范
- 表名：小写 + 下划线
- 列名：小写 + 下划线  
- 实体类：大驼峰命名
- 属性：小驼峰命名

### 3. 性能建议
- 合理使用索引
- 避免 N+1 查询
- 使用批量操作
- 启用查询缓存

---

**Anyline ORM 为 RegaWebERP 项目提供了强大的跨数据库操作能力，特别适合需要支持多种数据库和动态表单的企业级应用！** 🎯

## 🎯 RegaWebERP 集成方案

### 1. 依赖配置

#### Maven 依赖
```xml
<dependency>
    <groupId>org.anyline</groupId>
    <artifactId>anyline-spring-boot-starter</artifactId>
    <version>8.7.3</version>
</dependency>

<!-- PostgreSQL 驱动 -->
<dependency>
    <groupId>org.postgresql</groupId>
    <artifactId>postgresql</artifactId>
</dependency>

<!-- 连接池 -->
<dependency>
    <groupId>com.zaxxer</groupId>
    <artifactId>HikariCP</artifactId>
</dependency>
```

### 2. 配置文件

#### application.yml
```yaml
spring:
  datasource:
    url: *****************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver

anyline:
  datasource:
    default: primary
    list:
      primary:
        url: ${spring.datasource.url}
        username: ${spring.datasource.username}
        password: ${spring.datasource.password}
        driver-class-name: ${spring.datasource.driver-class-name}

  # 元数据缓存配置
  metadata:
    cache:
      enable: true
      expire: 3600  # 缓存1小时

  # SQL 日志配置
  sql:
    log:
      enable: true
      level: DEBUG

  # 主键生成策略
  primary:
    generator: snowflake
```

### 3. 核心服务封装

#### BaseService 基础服务类
```java
@Component
public class BaseService {

    @Autowired
    @Qualifier("anyline.service")
    protected AnylineService service;

    /**
     * 生成雪花ID
     */
    protected Long generateId() {
        return service.primary().generate();
    }

    /**
     * 获取当前租户ID
     */
    protected Long getCurrentTenantId() {
        return TenantUtils.getCurrentTenantId();
    }

    /**
     * 获取当前用户ID
     */
    protected Long getCurrentUserId() {
        return SecurityUtils.getCurrentUserId();
    }
}
```

---

**Anyline ORM 为 RegaWebERP 项目提供了强大的跨数据库操作能力，特别适合需要支持多种数据库和动态表单的企业级应用！** 🎯

**核心优势**:
- ✅ **466+ 数据库支持** - 真正的跨数据库兼容
- ✅ **自动方言转换** - 无需担心 SQL 语法差异
- ✅ **动态元数据** - 完美支持动态表单场景
- ✅ **运行时操作** - 灵活的 DDL 和元数据管理
- ✅ **Spring 集成** - 无缝集成 Spring Boot 生态

**最后更新**: 2025-06-17
**版本**: v1.0
**维护者**: RegaWebERP 开发团队
