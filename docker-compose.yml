version: '3'

services:
  # PostgreSQL数据库服务
  postgres:
    image: postgres:14
    container_name: rega-postgres
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: rega_erp
      TZ: Asia/Shanghai
    ports:
      - "5432:5432"
    volumes:
      - ./docker/postgres/data:/var/lib/postgresql/data
    networks:
      - rega-network

  # Redis缓存服务
  redis:
    image: redis:6.2
    container_name: rega-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - ./docker/redis/data:/data
    command: redis-server --appendonly yes
    networks:
      - rega-network

  # API服务
  rega-api:
    build:
      context: ./rega-api
      dockerfile: Dockerfile
    container_name: rega-api
    restart: always
    depends_on:
      - postgres
      - redis
    ports:
      - "8000:8000"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=rega_erp
      - DB_USER=postgres
      - DB_PWD=postgres
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./logs:/var/logs
    networks:
      - rega-network

networks:
  rega-network:
    driver: bridge 