#!/bin/bash

# 修复 BusinessException 调用的脚本

echo "开始修复 BusinessException 调用..."

# 查找所有包含 BusinessException 两个字符串参数的文件
find src/main/java -name "*.java" -exec grep -l 'new BusinessException("[^"]*", "[^"]*")' {} \;

# 使用 sed 进行批量替换
find src/main/java -name "*.java" -exec sed -i '' \
    -e 's/new BusinessException("tenant\.not\.found", \([^)]*\))/new BusinessException("租户不存在: " + \1)/g' \
    -e 's/new BusinessException("tenant\.not\.active", \([^)]*\))/new BusinessException("租户未激活: " + \1)/g' \
    -e 's/new BusinessException("tenant\.not\.available", \([^)]*\))/new BusinessException("租户不可用: " + \1)/g' \
    -e 's/new BusinessException("tenant\.already\.exists", \([^)]*\))/new BusinessException("租户已存在: " + \1)/g' \
    -e 's/new BusinessException("tenant\.init\.failed", \([^)]*\))/new BusinessException("租户环境初始化失败: " + \1)/g' \
    -e 's/new BusinessException("tenant\.delete\.failed", \([^)]*\))/new BusinessException("租户删除失败: " + \1)/g' \
    -e 's/new BusinessException("datasource\.not\.found", \([^)]*\))/new BusinessException("数据源不存在: " + \1)/g' \
    -e 's/new BusinessException("datasource\.already\.exists", \([^)]*\))/new BusinessException("数据源已存在: " + \1)/g' \
    -e 's/new BusinessException("datasource\.not\.active", \([^)]*\))/new BusinessException("数据源未激活: " + \1)/g' \
    -e 's/new BusinessException("datasource\.not\.available", \([^)]*\))/new BusinessException("数据源不可用: " + \1)/g' \
    -e 's/new BusinessException("datasource\.create\.failed", \([^)]*\))/new BusinessException("数据源创建失败: " + \1)/g' \
    -e 's/new BusinessException("datasource\.connection\.test\.failed", \([^)]*\))/new BusinessException("数据源连接测试失败: " + \1)/g' \
    -e 's/new BusinessException("datasource\.tenant\.limit\.exceeded", \([^)]*\))/new BusinessException("数据源租户数量超限: " + \1)/g' \
    -e 's/new BusinessException("datasource\.callback\.failed", \([^)]*\))/new BusinessException("数据源回调执行失败: " + \1)/g' \
    -e 's/new BusinessException("tenant\.datasource\.callback\.failed", \([^)]*\))/new BusinessException("租户数据源回调执行失败: " + \1)/g' \
    -e 's/new BusinessException("query\.context\.invalid", \([^)]*\))/new BusinessException("查询上下文无效: " + \1)/g' \
    -e 's/new BusinessException("query\.execution\.failed", \([^)]*\))/new BusinessException("查询执行失败: " + \1)/g' \
    -e 's/new BusinessException("data\.operation\.failed", \([^)]*\))/new BusinessException("数据操作失败: " + \1)/g' \
    -e 's/new BusinessException("isolation\.processing\.failed", \([^)]*\))/new BusinessException("隔离处理失败: " + \1)/g' \
    {} \;

# 处理单个字符串参数的情况
find src/main/java -name "*.java" -exec sed -i '' \
    -e 's/new BusinessException("tenant\.info\.null")/new BusinessException("租户信息不能为空")/g' \
    -e 's/new BusinessException("tenant\.info\.invalid")/new BusinessException("租户信息无效")/g' \
    -e 's/new BusinessException("tenant\.request\.null")/new BusinessException("租户请求不能为空")/g' \
    -e 's/new BusinessException("tenant\.request\.invalid")/new BusinessException("租户请求无效")/g' \
    -e 's/new BusinessException("datasource\.id\.required")/new BusinessException("数据源ID不能为空")/g' \
    -e 's/new BusinessException("datasource\.instance\.null")/new BusinessException("数据源实例不能为空")/g' \
    -e 's/new BusinessException("datasource\.info\.null")/new BusinessException("数据源信息不能为空")/g' \
    -e 's/new BusinessException("datasource\.info\.invalid")/new BusinessException("数据源信息无效")/g' \
    -e 's/new BusinessException("datasource\.config\.invalid")/new BusinessException("数据源配置无效")/g' \
    -e 's/new BusinessException("datasource\.connection\.invalid")/new BusinessException("数据源连接无效")/g' \
    -e 's/new BusinessException("primary\.datasource\.not\.found")/new BusinessException("主数据源不存在")/g' \
    -e 's/new BusinessException("query\.context\.invalid")/new BusinessException("查询上下文无效")/g' \
    -e 's/new BusinessException("data\.operation\.context\.invalid")/new BusinessException("数据操作上下文无效")/g' \
    -e 's/new BusinessException("data\.operation\.tenant\.set\.failed")/new BusinessException("设置租户ID失败")/g' \
    -e 's/new BusinessException("isolation\.type\.invalid")/new BusinessException("隔离类型无效")/g' \
    -e 's/new BusinessException("isolation\.strategy\.not\.found")/new BusinessException("隔离策略不存在")/g' \
    {} \;

echo "BusinessException 调用修复完成！"
